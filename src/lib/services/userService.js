// lib/services/userService.js

export const userService = {
  // Get current user dashboard data
  getCurrentUser: async () => {
    const response = await fetch('/api/dashboard', {
      method: 'GET',
      credentials: 'include', // Include cookies
      headers: {
        'Accept': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch user data: ${response.status}`)
    }

    const result = await response.json()
    
    if (!result.success) {
      throw new Error(result.message || 'Failed to fetch user data')
    }

    // Transform backend data to match frontend expectations
    return transformDashboardData(result.data)
  },

  // Get dashboard data specifically
  getDashboardData: async () => {
    const response = await fetch('/api/dashboard', {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Accept': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch dashboard data: ${response.status}`)
    }

    const result = await response.json()
    
    if (!result.success) {
      throw new Error(result.message || 'Failed to fetch dashboard data')
    }

    return result.data
  }
}

// Transform backend data structure to match frontend component expectations
function transformDashboardData(backendData) {
  // Defensive programming: ensure all required properties exist
  if (!backendData) {
    throw new Error('Backend data is null or undefined')
  }

  const { user = {}, stats = {}, websites = [], tierInfo = {} } = backendData

  // Ensure nested objects exist with defaults
  const userProfile = user.profile || {}
  const statsWebsites = stats.websites || {}
  const statsCredits = stats.credits || {}

  return {
    // User info
    id: user.id || '',
    email: user.email || '',
    username: userProfile.username || '',
    displayName: userProfile.displayName || '',
    type: user.type || 'user',
    createdAt: user.createdAt || new Date().toISOString(),

    // Account details for QuickStats component
    accountDetails: {
      limits: {
        currentUsage: {
          websites: statsWebsites.current || 0,
        },
        restrictions: {
          websiteLimit: statsWebsites.max || 0,
          creditLimit: statsCredits.max || 0,
        }
      },
      subscription: {
        currentPlan: (stats.currentTier || 'free').toUpperCase(),
      }
    },

    // Metrics for QuickStats component
    metrics: {
      totalCreditsUsed: statsCredits.current || 0,
      totalScans: stats.posts || 0,
      totalUsage: stats.totalUsage || 0,
    },

    // Websites data
    websites: Array.isArray(websites) ? websites.map(website => ({
      domain: website?.domain || '',
      status: website?.status || 'inactive',
      // Fix credits display: used/total format
      used: website?.creditsUsed || 0, // Credits yang sudah digunakan
      credit: statsCredits.max || 100, // Total credit limit dari tier (shared across websites)
      creditsUsed: website?.creditsUsed || 0,
      lastScanned: website?.lastScan || null, // Match WebsitesTable expectation
      key: website?.apiKey || '', // Match WebsitesTable expectation
      apiKey: website?.apiKey || '',
      totalScans: website?.totalScans || 0,
      // Add scanHistory for activity page
      scanHistory: website?.scanHistory || (website?.lastScan ? [{
        id: `scan_${website?.domain || 'unknown'}_${Date.now()}`,
        date: website.lastScan,
        status: 'completed',
        scansCount: website?.totalScans || 0,
        type: 'auto_scan',
        domain: website?.domain || 'unknown'
      }] : [])
    })) : [],

    // Tier information
    tierInfo: {
      tier: tierInfo?.tier || 'free',
      tierName: tierInfo?.tierName || 'Free',
      creditLimit: tierInfo?.creditLimit || 0,
      websiteLimit: tierInfo?.websiteLimit || 0,
      price: tierInfo?.price || 0,
      features: tierInfo?.features || [],
    },

    // Stats summary
    stats: {
      websites: stats.websites,
      credits: stats.credits,
      currentTier: stats.currentTier,
      posts: stats.posts,
      totalUsage: stats.totalUsage,
    },

    // Raw backend data for any custom usage
    _raw: backendData,
  }
}

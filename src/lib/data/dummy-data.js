export const users = [
  {
    id: 'usr_' + Date.now(),
    email: '<EMAIL>',
    username: 'johns<PERSON>',
    password: '$2a$10$hashedpassword',
    firstName: '<PERSON>',
    lastName: '<PERSON>',

    accountDetails: {
      registered: {
        date: '2021-01-01T00:00:00+07:00',
        source: 'web',
        referredBy: null,
        verifiedAt: '2021-01-01T00:01:23+07:00',
      },

      loginHistory: {
        lastLogin: '2025-01-11T12:18:59+07:00',
        lastLoginIP: '***********',
        lastLoginDevice: 'Chrome/Windows',
        loginCount: 157,
        failedAttempts: 0,
        lastFailedLogin: null,
      },

      status: {
        current: 'active',
        lastChanged: '2021-01-01T00:00:00+07:00',
        reason: null,
        history: [
          {
            status: 'active',
            timestamp: '2021-01-01T00:00:00+07:00',
            reason: 'Account creation',
          },
        ],
      },

      subscription: {
        currentPlan: 'pro',
        status: 'active',
        startDate: '2024-01-01T00:00:00+07:00',
        expiryDate: '2025-01-11T23:59:59+07:00',
        autoRenew: true,
        billingCycle: 'monthly',
        price: 99000,
        currency: 'Rp',

        features: {
          maxWebsites: 10,
          maxCreditsPerSite: 100,
          maxUsers: 5,
          customReports: true,
          apiAccess: true,
          priority_support: true,
        },

        history: [
          {
            plan: 'freemium',
            startDate: '2021-01-01T00:00:00+07:00',
            endDate: '2023-12-31T23:59:59+07:00',
            reason: 'Initial plan',
          },
          {
            plan: 'pro',
            startDate: '2024-01-01T00:00:00+07:00',
            endDate: null,
            reason: 'Upgrade',
          },
        ],
      },

      limits: {
        currentUsage: {
          websites: 3,
          credits: 60,
          users: 2,
        },
        restrictions: {
          websiteLimit: 10,
          creditLimit: 100,
          userLimit: 5,
        },
      },

      flags: {
        isVerified: true,
        isBetaTester: false,
        hasCustomPricing: false,
        requiresMFA: true,
        isGrandfathered: false,
      },
    },

    contact: {
      phone: '+62812345678',
      address: {
        street: '123 Main Street',
        city: 'Jakarta',
        state: 'DKI Jakarta',
        country: 'Indonesia',
        postalCode: '12345',
      },
    },

    billing: {
      paymentMethod: 'credit_card',
      cardLast4: '4242',
      expiryDate: '12/25',
      currency: 'Rp',
      billingAddress: {
        street: '123 Main Street',
        city: 'Jakarta',
        state: 'DKI Jakarta',
        country: 'Indonesia',
        postalCode: '12345',
      },
      invoices: [
        {
          id: 'inv_123',
          amount: 99000,
          status: 'paid',
          date: '2024-01-01T00:00:00+07:00',
        },
      ],
    },

    websites: [
      {
        id: 'web_123',
        domain: 'domain1.com',
        key: 'xxx-yyy-zzz',
        credit: 50,
        used: 20,
        status: 'active',
        createdAt: '2024-01-01T00:00:00+07:00',
        lastScanned: '2025-01-11T12:00:00+07:00',
        settings: {
          notifications: true,
          autoScan: true,
          scanFrequency: 'daily',
          customHeaders: {},
          allowedPaths: [],
          excludedPaths: [],
        },
        scanHistory: [
          {
            id: 'scan_123',
            date: '2025-01-11T12:00:00+07:00',
            status: 'completed',
            findings: 0,
          },
        ],
      },
      {
        id: 'web_124',
        domain: 'domain2.com',
        key: 'xxx-yyy-zzz',
        credit: 50,
        used: 30,
        status: 'active',
        createdAt: '2024-01-01T00:00:00+07:00',
        lastScanned: '2025-01-11T12:00:00+07:00',
        settings: {
          notifications: true,
          autoScan: true,
          scanFrequency: 'weekly',
          customHeaders: {},
          allowedPaths: [],
          excludedPaths: [],
        },
        scanHistory: [
          {
            id: 'scan_124',
            date: '2025-01-11T12:00:00+07:00',
            status: 'completed',
            findings: 2,
          },
        ],
      },
      {
        id: 'web_125',
        domain: 'domain3.com',
        key: 'xxx-yyy-zzz',
        credit: 50,
        used: 10,
        status: 'active',
        createdAt: '2024-01-01T00:00:00+07:00',
        lastScanned: '2025-01-11T12:00:00+07:00',
        settings: {
          notifications: true,
          autoScan: true,
          scanFrequency: 'monthly',
          customHeaders: {},
          allowedPaths: [],
          excludedPaths: [],
        },
        scanHistory: [
          {
            id: 'scan_125',
            date: '2025-01-11T12:00:00+07:00',
            status: 'completed',
            findings: 1,
          },
        ],
      },
    ],

    preferences: {
      language: 'en',
      timezone: 'Asia/Jakarta',
      notifications: {
        email: true,
        push: true,
        sms: false,
        scanComplete: true,
        billingReminders: true,
        securityAlerts: true,
      },
      theme: 'light',
      dashboard: {
        defaultView: 'overview',
        widgets: ['usage', 'alerts', 'scans'],
      },
    },

    metrics: {
      totalScans: 250,
      totalCreditsUsed: 60,
      totalFindings: 3,
      lastActivityDate: '2025-01-11T12:18:59+07:00',
      averageScanTime: 120,
      scanSuccess: 98.5,
    },

    security: {
      mfa: {
        enabled: true,
        method: 'authenticator',
        backup_codes: [],
      },
      lastPasswordChange: '2024-12-01T00:00:00+07:00',
      passwordResetRequired: false,
      loginAttempts: 0,
    },

    createdAt: '2021-01-01T00:00:00+07:00',
    updatedAt: '2025-01-11T12:18:59+07:00',
    deletedAt: null,
  },
]

'use client'

import Link from 'next/link'
import { useEffect, useState } from 'react'
import { useTheme } from '@/contexts/ThemeProvider'

export function Navbar({ onMobileMenuClick }) {
  const [notifications] = useState(3) // Example notification count
  const { theme, setTheme } = useTheme()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  return (
    <div className="sticky top-0 z-40 border-b-4 border-gray-200 bg-white dark:border-gray-800 dark:bg-[#151821]">
      <div className="px-6">
        <div className="flex h-16 items-center justify-between">
          {/* Left side */}
          <div className="flex items-center gap-6">
            <button
              type="button"
              className="text-gray-700 hover:text-gray-900 lg:hidden dark:text-gray-500 dark:hover:text-gray-300"
              onClick={onMobileMenuClick}
            >
              <i className="pi pi-bars text-xl" />
            </button>

            <div className="hidden text-sm text-gray-700 lg:block dark:text-gray-500">
              <Link href="/dashboard" aria-label="Home">
                              </Link>
            </div>
          </div>

          {/* Right side */}
          <div className="flex items-center gap-7">
            {/* Search */}
            {/* <div className="hidden md:block"> */}
            {/*   <div className="relative"> */}
            {/*     <input */}
            {/*       type="text" */}
            {/*       placeholder="Search..." */}
            {/*       className="w-64 border-0 border-b border-gray-200 py-2 pl-8 text-sm placeholder:text-gray-400 focus:border-gray-900 focus:ring-0 dark:border-gray-700 dark:bg-[#151821] dark:text-gray-300" */}
            {/*     /> */}
            {/*     <i className="pi pi-search absolute left-0 top-2.5 text-gray-400 dark:text-gray-500" /> */}
            {/*   </div> */}
            {/* </div> */}

            {/* Notifications */}
            {/* <button className="relative text-gray-700 hover:text-gray-900 dark:text-gray-500 dark:hover:text-gray-300"> */}
            {/*   <i className="pi pi-bell text-xl" /> */}
            {/*   {notifications > 0 && ( */}
            {/*     <span className="absolute -right-1 -top-1 flex h-4 w-4 items-center justify-center rounded-full bg-gray-900 text-xs font-medium text-white dark:bg-gray-700"> */}
            {/*       {notifications} */}
            {/*     </span> */}
            {/*   )} */}
            {/* </button> */}

            {/* Settings */}
            <Link
              href="/settings"
              className="text-gray-700 hover:text-gray-900 dark:text-gray-500 dark:hover:text-gray-300"
            >
              <i className="pi pi-cog text-xl" />
            </Link>

            {/* Theme switcher */}
            {mounted && (
              <button
                aria-label={theme === 'dark' ? 'Switch to light mode' : 'Switch to dark mode'}
                onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
                className="text-gray-700 hover:text-blue-500 focus:outline-none dark:text-gray-500 dark:hover:text-blue-400"
              >
                {theme === 'dark' ? (
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="h-6 w-6">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M12 3v1.5M12 19.5V21M4.219 4.219l1.06 1.06M18.719 18.719l1.061 1.061M1.5 12H3m16.5 0h1.5M4.219 19.781l1.06-1.061M18.719 5.281l1.061-1.061M12 7.5A4.5 4.5 0 1012 16.5 4.5 4.5 0 0012 7.5z" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="h-6 w-6">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M21.752 15.002A9.721 9.721 0 0112 21.75c-5.385 0-9.75-4.365-9.75-9.75 0-4.169 2.557-7.797 6.248-9.252a.75.75 0 01.945.964 7.501 7.501 0 003.958 9.958.75.75 0 01.964.945z" />
                  </svg>
                )}
              </button>
            )}

            {/* Divider */}
            <div className="h-6 w-px bg-gray-200 dark:bg-gray-700" />

            {/* Logout */}
            <button
              onClick={async () => {
                await fetch('/api/logout', { method: 'POST' });
                window.location.href = '/';
              }}
              className="flex items-center gap-2 text-sm font-medium text-gray-700 hover:text-gray-600 dark:text-gray-300 dark:hover:text-gray-400"
            >
              <span className="hidden sm:block">Logout</span>
              <i className="pi pi-sign-out" />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

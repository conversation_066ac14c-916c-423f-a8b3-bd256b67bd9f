'use client'

import Link from 'next/link'
import { Logo } from '@/components/logo/LogoBolt'
import { LogoText } from '@/components/logo/LogoText'
import { usePathname } from 'next/navigation'

const navigation = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: 'pi pi-gauge',
  },
  {
    name: 'Activity',
    href: '/activity',
    icon: 'pi pi-wave-pulse',
  },
  {
    name: 'Plans',
    href: '/plans',
    icon: 'pi pi-star',
  },
  {
    name: 'Payment',
    href: '/payment',
    icon: 'pi pi-money-bill',
  },
]

export function Sidebar() {
  const pathname = usePathname()

  return (
    <aside className="fixed inset-y-0 left-0 z-50 hidden w-64 border-r border-gray-200 bg-white dark:border-gray-800 dark:bg-[#151821] lg:block">
      <div className="flex h-16 items-center bg-white px-6 dark:border-gray-700 dark:bg-[#151821]">
        <Link
          href="#"
          aria-label="Home"
          className="inline-flex items-center"
        >
          <Logo className="h-10" />
          <LogoText className="h-6 w-auto text-gray-900 dark:text-gray-300" />
        </Link>
      </div>

      <nav className="mt-8 space-y-1">
        {navigation.map((item) => {
          const isActive = pathname === item.href
          return (
            <Link
              key={item.name}
              href={item.href}
              className={`group relative flex items-center gap-3 px-4 py-3 text-sm font-medium transition-colors duration-200 ${
                isActive
                  ? 'border-l-4 border-gray-800 bg-gray-100 text-gray-800 dark:border-blue-950 dark:bg-gray-950 dark:text-gray-200'
                  : 'text-gray-600 hover:bg-gray-100 hover:text-gray-800 dark:text-gray-400 dark:hover:bg-gray-950 dark:hover:text-gray-200'
              } `}
            >
              <i className={`${item.icon} text-lg`} />
              {item.name}
            </Link>
          )
        })}
      </nav>
    </aside>
  )
}

// components/dashboard/layout/DashboardShell.jsx
'use client'

import { useState } from 'react'
import { Sidebar } from './Sidebar'
import { MobileSidebar } from './MobileSidebar'
import { Navbar } from './Navbar'
import { Footer } from './Footer'

export function DashboardShell({ children }) {
  const [sidebarOpen, setSidebarOpen] = useState(false)

  return (
    <>
      <MobileSidebar
        isOpen={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
      />
      <Sidebar />

      <div className="lg:pl-64">
        <Navbar onMobileMenuClick={() => setSidebarOpen(true)} />

        <main className="relative min-h-[calc(100vh-3.5rem)] bg-white px-6 py-6 dark:bg-gradient-to-b dark:from-[#050608] dark:to-[#0a0c12]">
          {/* Subtle grid background */}
          <div className="pointer-events-none absolute inset-0 bg-[linear-gradient(to_right,#0066ff05_1px,transparent_1px),linear-gradient(to_bottom,#0066ff05_1px,transparent_1px)] bg-[size:24px_24px] dark:bg-[linear-gradient(to_right,#0066ff05_1px,transparent_1px),linear-gradient(to_bottom,#0066ff05_1px,transparent_1px)]" />

          {/* Ambient glow effect */}

          <div className="relative mx-auto max-w-full">{children}</div>
        </main>
      </div>
    </>
  )
}

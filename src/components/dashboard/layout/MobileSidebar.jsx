// components/dashboard/layout/MobileSidebar.jsx
'use client'

import { Fragment } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import Link from 'next/link'
import { Logo } from '@/components/logo/LogoBolt'
import { LogoBrutalish } from '@/components/logo/LogoBrutalish'
import { usePathname } from 'next/navigation'

const navigation = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: 'pi pi-gauge',
  },
  {
    name: 'Activity',
    href: '/activity',
    icon: 'pi pi-wave-pulse',
  },
  {
    name: 'Plans',
    href: '/plans',
    icon: 'pi pi-star',
  },
  {
    name: 'Payment',
    href: '/payment',
    icon: 'pi pi-money-bill',
  },
]

export function MobileSidebar({ isOpen, onClose }) {
  const pathname = usePathname()

  return (
    <Transition.Root show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50 lg:hidden" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="transition-opacity ease-linear duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="transition-opacity ease-linear duration-300"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-900/80 backdrop-blur-sm" />
        </Transition.Child>

        <div className="fixed inset-0 flex">
          <Transition.Child
            as={Fragment}
            enter="transition ease-in-out duration-300 transform"
            enterFrom="-translate-x-full"
            enterTo="translate-x-0"
            leave="transition ease-in-out duration-300 transform"
            leaveFrom="translate-x-0"
            leaveTo="-translate-x-full"
          >
            <Dialog.Panel className="relative mr-16 flex w-full max-w-xs flex-1">
              <div className="absolute right-0 top-0 flex w-16 justify-center pt-5">
                <button
                  type="button"
                  className="text-gray-400 transition-colors hover:text-white"
                  onClick={onClose}
                >
                  <i className="pi pi-times text-xl" />
                </button>
              </div>

              <div className="flex grow flex-col bg-[#0a0b0f]/90 px-6 pb-4 backdrop-blur-sm">
                <div className="ms-3 flex h-16 items-center">
                  <Link
                    href="#"
                    aria-label="Home"
                    className="inline-flex items-center"
                  >
                    <Logo className="h-8 w-auto text-blue-500" />
                    <LogoBrutalish />
                  </Link>
                </div>

                {/* Subtle gradient line */}
                <div className="h-px w-full bg-gradient-to-r from-transparent via-blue-500/20 to-transparent" />

                <nav className="mt-4">
                  {navigation.map((item) => {
                    const isActive = pathname === item.href
                    return (
                      <Link
                        key={item.name}
                        href={item.href}
                        className={`group flex items-center gap-x-3 rounded-md px-4 py-3 font-mono text-sm ${
                          isActive
                            ? 'bg-blue-500/10 text-blue-400'
                            : 'text-gray-400 hover:bg-[#151821] hover:text-gray-300'
                        } `}
                        onClick={onClose}
                      >
                        <i
                          className={`${item.icon} text-xl ${isActive ? 'text-blue-400' : 'text-gray-500 group-hover:text-gray-300'}`}
                        />
                        {item.name}
                      </Link>
                    )
                  })}
                </nav>

                {/* Bottom section with logout */}
                <div className="mt-auto pt-6">
                  <div className="h-px w-full bg-gradient-to-r from-transparent via-blue-900/30 to-transparent" />
                  <Link
                    href="/logout"
                    className="group mt-4 flex items-center gap-2 rounded-md px-4 py-3 font-mono text-sm font-medium text-gray-400 transition-colors hover:bg-[#151821] hover:text-gray-300"
                    onClick={onClose}
                  >
                    <i className="pi pi-sign-out text-gray-500 group-hover:text-gray-300" />
                    <span>Logout</span>
                    <i className="pi pi-arrow-right ml-auto text-gray-500 transition-transform group-hover:translate-x-0.5 group-hover:text-gray-300" />
                  </Link>
                </div>
              </div>
            </Dialog.Panel>
          </Transition.Child>
        </div>
      </Dialog>
    </Transition.Root>
  )
}

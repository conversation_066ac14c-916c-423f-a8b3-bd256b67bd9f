'use client'

import Link from 'next/link'
import { useState, useEffect, useRef } from 'react'
import { useUserContext } from '@/contexts/UserProvider'

export function Navbar({ onMobileMenuClick }) {
  const { user } = useUserContext()
  const [menuOpen, setMenuOpen] = useState(false)
  const menuRef = useRef(null)

  // <PERSON>le click outside to close menu
  useEffect(() => {
    function handleClickOutside(event) {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setMenuOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [menuRef])

  return (
    <header className="sticky top-0 z-40 flex h-14 items-center border-b border-blue-900/10 bg-[#14161D]/90 px-4 backdrop-blur-sm sm:px-6">
      <button
        type="button"
        className="rounded-full p-1.5 text-gray-400 hover:bg-blue-500/10 hover:text-blue-400 lg:hidden"
        onClick={onMobileMenuClick}
      >
        <span className="sr-only">Open sidebar</span>
        <i className="pi pi-bars text-sm" />
      </button>

      <div className="ml-auto flex items-center gap-5">
        {/* User email display */}
        {user && (
          <div className="hidden font-mono text-sm text-gray-300 sm:block">
            {user.email || 'Not available'}
          </div>
        )}

        {/* User menu dropdown */}
        <div className="relative" ref={menuRef}>
          <button
            className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-500/20 text-blue-400 transition-colors hover:bg-blue-500/30"
            onClick={() => setMenuOpen(!menuOpen)}
          >
            <i className="pi pi-user text-xs" />
          </button>

          {menuOpen && (
            <div className="absolute right-0 mt-2 w-48 origin-top-right rounded-md border border-blue-900/20 bg-[#151821] py-1  ">
              <Link
                href="/settings"
                className="flex items-center px-4 py-2 font-mono text-sm text-gray-300 transition-colors hover:bg-blue-500/10"
              >
                <i className="pi pi-cog mr-2 text-xs text-blue-400"></i>
                Settings
              </Link>
              <div className="my-1 border-t border-blue-900/20"></div>
              <Link
                href="/logout"
                className="flex items-center px-4 py-2 font-mono text-sm text-gray-300 transition-colors hover:bg-blue-500/10"
              >
                <i className="pi pi-sign-out mr-2 text-xs text-blue-400"></i>
                Sign out
              </Link>
            </div>
          )}
        </div>
      </div>
    </header>
  )
}

'use client'

import Link from 'next/link'
import { useState } from 'react'

export function Navbar({ onMobileMenuClick }) {
  const [notifications] = useState(3)

  return (
    <div className="sticky top-0 z-40 bg-white/90 backdrop-blur-sm">
      <div className="px-6">
        <div className="flex h-16 items-center justify-between">
          {/* Left side */}
          <div className="flex items-center gap-6">
            <button
              type="button"
              className="text-gray-400 transition-colors hover:text-gray-900 lg:hidden"
              onClick={onMobileMenuClick}
            >
              <i className="pi pi-bars text-xl" />
            </button>
          </div>

          {/* Right side */}
          <div className="flex items-center gap-5">
            {/* Enhanced Search */}
            {/* <div className="hidden md:block"> */}
            {/*   <div className="relative"> */}
            {/*     <input */}
            {/*       type="text" */}
            {/*       placeholder="system.search" */}
            {/*       className="w-64 rounded-none border-0 border-b border-gray-200 bg-transparent py-2 pl-8 font-mono text-sm text-gray-600 transition-colors placeholder:text-gray-400 focus:border-blue-500 focus:ring-0" */}
            {/*     /> */}
            {/*     <i className="pi pi-search absolute left-0 top-2.5 text-gray-400" /> */}
            {/*   </div> */}
            {/* </div> */}

            {/* Enhanced Notifications */}
            <button className="group relative text-gray-400 transition-colors hover:text-gray-900">
              <i className="pi pi-bell text-xl" />
              {notifications > 0 && (
                <span className="absolute -right-1 -top-1 flex h-4 w-4 items-center justify-center rounded-full bg-blue-500 font-mono text-xs text-white">
                  {notifications}
                </span>
              )}
            </button>

            {/* Enhanced Settings */}
            <Link
              href="/settings"
              className="text-gray-400 transition-colors hover:text-gray-900"
            >
              <i className="pi pi-cog animate-spin-slow text-xl" />
            </Link>

            {/* Enhanced Divider */}
            <div className="h-6 w-px bg-gray-200" />

            {/* Enhanced Logout */}
            <Link
              href="/logout"
              className="group flex items-center gap-2 font-mono text-sm font-medium text-gray-600 transition-colors hover:text-gray-900"
            >
              <span className="hidden sm:block">Logout</span>
              <i className="pi pi-sign-out transition-transform group-hover:translate-x-0.5" />
            </Link>
          </div>
        </div>
      </div>

      {/* Subtle gradient line */}
      <div className="absolute inset-x-0 -bottom-px h-px bg-gradient-to-r from-transparent ${theme === 'dark' ? 'via-white/10' : 'via-zinc-950/10'} to-transparent" />
    </div>
  )
}

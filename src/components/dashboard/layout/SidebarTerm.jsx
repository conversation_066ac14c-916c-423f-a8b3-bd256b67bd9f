'use client'

import Link from 'next/link'
import { Logo } from '@/components/logo/LogoBolt'
import { LogoBrutalish } from '@/components/logo/LogoBrutalish'
import { usePathname } from 'next/navigation'

const navigation = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: 'pi pi-gauge',
  },
  {
    name: 'Activity',
    href: '/activity',
    icon: 'pi pi-wave-pulse',
  },
  {
    name: 'Plans',
    href: '/plans',
    icon: 'pi pi-star',
  },
  {
    name: 'Payment',
    href: '/payment',
    icon: 'pi pi-money-bill',
  },
]

export function Sidebar() {
  const pathname = usePathname()

  return (
    <aside className="fixed inset-y-0 left-0 z-50 hidden w-64 border-r border-blue-900/20 bg-gradient-to-b from-[#0f1117] to-[#151821] lg:block">
      {/* Header */}
      <div className="relative flex h-14 items-center border-b border-blue-900/20 bg-[#151821]/50 px-6">
        <Link
          href="#"
          aria-label="Home"
          className="group inline-flex items-center gap-2"
        >
          <div className="relative flex">
            <Logo className="h-8 w-auto text-blue-500" />
            <LogoBrutalish />
          </div>
        </Link>

        {/* Enhanced glow effect */}
        <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-blue-500/30 to-transparent" />
      </div>

      {/* Navigation */}
      <div className="relative">
        <nav className="mt-2 px-2">
          {navigation.map((item) => {
            const isActive = pathname === item.href
            return (
              <Link
                key={item.name}
                href={item.href}
                className={`group relative flex items-center gap-3 rounded-sm px-4 py-2.5 font-mono text-sm transition-colors ${
                  isActive
                    ? 'bg-blue-500/10 text-blue-400'
                    : 'text-gray-400 hover:bg-blue-500/5 hover:text-blue-400'
                }`}
              >
                <i className={`${item.icon} text-lg`} />
                <span className="text-xs">{item.name}</span>
                {isActive && (
                  <div className="absolute inset-y-0 left-0 w-0.5 bg-gradient-to-b from-blue-400 to-blue-600" />
                )}
              </Link>
            )
          })}
        </nav>

        {/* Enhanced scanline effect */}
        <div className="pointer-events-none absolute inset-0 bg-[linear-gradient(to_bottom,rgba(59,130,246,0.02)_50%,transparent_50%)] bg-[length:100%_4px] opacity-20" />
      </div>
    </aside>
  )
}

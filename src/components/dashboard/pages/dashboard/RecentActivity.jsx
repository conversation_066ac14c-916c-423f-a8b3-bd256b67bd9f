export function RecentActivity({ scanHistory }) {
  return (
    <div className="mt-8">
      <h2 className="mb-4 text-lg font-semibold">Recent Posts</h2>
      <div className="rounded-lg bg-white border border-gray-200 dark:bg-[#151821] dark:border-white/5">
        <ul className="divide-y divide-gray-200">
          {scanHistory.map((scan) => (
            <li key={scan.id} className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium dark:text-gray-300">{scan.domain}</p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {new Date(scan.date).toLocaleString()}
                  </p>
                </div>
                <span
                  className={`rounded-full px-2 py-1 text-sm ${
                    scan.status === 'completed'
                      ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                      : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100'
                  }`}
                >
                  {scan.status}
                </span>
              </div>
            </li>
          ))}
        </ul>
      </div>
    </div>
  )
}

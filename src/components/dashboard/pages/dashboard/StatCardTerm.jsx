export function StatCard({ title, value, total, icon }) {
  return (
    <div className="group relative overflow-hidden rounded-md bg-[#0f1117] transition-all">
      {/* Ambient glow effect */}
      <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-blue-500/20 to-transparent" />

      <div className="relative border-b border-blue-900/20 bg-[#151821] px-4 py-2">
        <div className="flex items-center justify-between">
          <span className="font-mono text-xs text-gray-500">{title}</span>
          <i className={`${icon} text-gray-600`} />
        </div>
      </div>

      <div className="px-4 py-3">
        <div className="font-mono">
          {total ? (
            <div className="flex items-baseline gap-1">
              <span className="text-lg text-gray-300">{value}</span>
              <span className="text-xs text-gray-600">/ {total}</span>
            </div>
          ) : (
            <span className="text-lg text-gray-300">{value}</span>
          )}
        </div>
      </div>

      {/* Hover effect */}
      <div className="absolute inset-x-0 bottom-0 h-px bg-gradient-to-r from-transparent via-blue-500/0 to-transparent transition-all group-hover:via-blue-500/20" />

      {/* Scanline effect */}
      <div className="pointer-events-none absolute inset-0 bg-[linear-gradient(to_bottom,rgba(59,130,246,0.02)_50%,transparent_50%)] bg-[length:100%_4px] opacity-20" />
    </div>
  )
}

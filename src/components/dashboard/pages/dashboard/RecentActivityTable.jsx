'use client'
import { useTheme } from '@/contexts/ThemeProvider'

import { DataTable } from 'primereact/datatable'
import { Column } from 'primereact/column'
import { PrimeReactTableStyles } from '@/components/dashboard/ui/PrimeReactTableStyles'

export const RecentActivityTable = ({ scanHistory }) => {
  const { theme } = useTheme();
  const statusBodyTemplate = (rowData) => (
    <span
      className={`font-mono text-xs ${theme === 'dark' ? 'text-white' : 'text-zinc-950'} ${rowData.status === 'completed' ? 'text-blue-400' : 'text-gray-400'}`}
    >
      <i
        className={`pi pi-circle-fill mr-2 text-[8px] ${
          rowData.status === 'completed' ? 'text-blue-400' : 'text-gray-600'
        }`}
      />
      {rowData.status}
    </span>
  )

  const dateBodyTemplate = (rowData) => (
    <span className={`font-mono text-xs ${theme === 'dark' ? 'text-white' : 'text-zinc-950'}`}>
      {new Date(rowData.date).toLocaleString()}
    </span>
  )

  const domainBodyTemplate = (rowData) => (
    <span className={`font-mono text-sm ${theme === 'dark' ? 'text-white' : 'text-zinc-950'}`}>{rowData.domain}</span>
  )

  return (
    <>
      <PrimeReactTableStyles />
      <DataTable
        key={theme}
        className={`p-datatable ${theme === 'dark' ? 'dark-table' : 'light-table'}`}

        value={scanHistory}
        paginator
        rows={10}
        emptyMessage={
          <span className="font-mono text-sm text-gray-500">
            No activity found
          </span>
        }
      >
        <Column
          field="domain"
          header="Domain"
          sortable
          body={domainBodyTemplate}
          headerClassName="text-zinc-500 dark:text-zinc-400 font-mono"
        />
        <Column
          field="date"
          header="Date"
          body={dateBodyTemplate}
          sortable
          headerClassName="text-zinc-500 dark:text-zinc-400 font-mono"
        />
        <Column
          field="status"
          header="Status"
          body={statusBodyTemplate}
          sortable
          headerClassName="text-zinc-500 dark:text-zinc-400 font-mono"
        />
      </DataTable>
    </>
  )
}

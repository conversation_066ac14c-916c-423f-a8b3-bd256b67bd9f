import { StatCard } from './StatCard'

export function QuickStats({ user }) {
  const stats = [
    {
      title: 'Websites',
      value: user.accountDetails.limits.currentUsage.websites,
      total: user.accountDetails.limits.restrictions.websiteLimit,
      icon: 'pi pi-globe',
    },
    {
      title: 'Pro Credits',
      value: user.metrics.totalCreditsUsed,
      total: user.accountDetails.limits.restrictions.creditLimit,
      icon: 'pi pi-ticket',
    },
    {
      title: 'Total Posts',
      value: user.metrics.totalScans,
      icon: 'pi pi-send',
    },
    {
      title: 'Plans',
      value: user.accountDetails.subscription.currentPlan.toUpperCase(),
      icon: 'pi pi-send',
    },
  ]

  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
      {stats.map((stat) => (
        <StatCard
          key={stat.title}
          title={stat.title}
          value={stat.value}
          total={stat.total}
          icon={stat.icon}
        />
      ))}
    </div>
  )
}

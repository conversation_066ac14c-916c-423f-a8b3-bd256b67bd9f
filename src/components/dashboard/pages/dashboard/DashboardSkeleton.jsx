export function DashboardSkeleton() {
  return (
    <div className="animate-pulse space-y-8">
      {/* Stats Skeleton */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
        {[1, 2, 3, 4].map((i) => (
          <div
            key={i}
            className="relative overflow-hidden border-l-2 border-blue-500/20 bg-[#151821] p-4"
          >
            {/* Header shimmer */}
            <div className="mb-4 h-4 w-24 rounded bg-gray-800" />
            {/* Value shimmer */}
            <div className="h-6 w-16 rounded bg-gray-800" />
            {/* Glow effect */}
            <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-blue-500/20 to-transparent" />
          </div>
        ))}
      </div>

      {/* Websites Table Skeleton */}
      <div className="relative overflow-hidden rounded-md bg-[#0f1117] border border-gray-200 dark:border-white/5 ">
        {/* Header glow */}
        <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-blue-500/20 to-transparent" />

        {/* Header */}
        <div className="border-b border-blue-900/20 bg-[#151821] p-4">
          <div className="flex items-center gap-2">
            <div className="h-4 w-4 rounded bg-gray-800" />
            <div className="h-4 w-32 rounded bg-gray-800" />
          </div>
          <div className="mt-2 flex gap-4">
            <div className="h-3 w-24 rounded bg-gray-800" />
            <div className="h-3 w-24 rounded bg-gray-800" />
          </div>
        </div>

        {/* Table rows */}
        <div className="p-4">
          {[1, 2, 3].map((i) => (
            <div
              key={i}
              className="mb-4 grid grid-cols-5 gap-4 border-b border-blue-900/10 pb-4 last:border-0 last:pb-0"
            >
              <div className="h-6 rounded bg-gray-800" />
              <div className="h-6 w-20 rounded bg-gray-800" />
              <div className="h-6 w-16 rounded bg-gray-800" />
              <div className="h-6 w-24 rounded bg-gray-800" />
              <div className="h-6 w-32 rounded bg-gray-800" />
            </div>
          ))}
        </div>

        {/* Pagination skeleton */}
        <div className="border-t border-blue-900/20 bg-[#151821] p-4">
          <div className="flex items-center justify-between">
            <div className="flex gap-2">
              {[1, 2, 3].map((i) => (
                <div key={i} className="h-8 w-8 rounded bg-gray-800" />
              ))}
            </div>
            <div className="h-4 w-32 rounded bg-gray-800" />
          </div>
        </div>

        {/* Scanline effect */}
        <div className="pointer-events-none absolute inset-0 bg-[linear-gradient(to_bottom,rgba(59,130,246,0.02)_50%,transparent_50%)] bg-[length:100%_4px] opacity-20" />
      </div>

      {/* Global scanline effect */}
      <div className="pointer-events-none fixed inset-0 bg-[linear-gradient(to_bottom,rgba(255,255,255,0.03)_50%,transparent_50%)] bg-[length:100%_4px] opacity-20" />
    </div>
  )
}

export function WebsiteCard({ website }) {
  return (
    <div className="rounded-lg bg-white p-6 border border-gray-200 dark:bg-[#151821] dark:border-white/5">
      <div className="flex items-center justify-between">
        <h3 className="font-semibold">{website.domain}</h3>
        <span
          className={`rounded-full px-2 py-1 text-sm ${
            website.status === 'active'
              ? 'bg-green-100 text-green-800'
              : 'bg-gray-100 text-gray-800'
          }`}
        >
          {website.status}
        </span>
      </div>

      <div className="mt-4 grid grid-cols-2 gap-4">
        <div>
          <p className="text-sm text-gray-500">Credits</p>
          <p className="font-medium">
            {website.used} / {website.credit}
          </p>
        </div>
        <div>
          <p className="text-sm text-gray-500">Last Scan</p>
          <p className="font-medium">
            {new Date(website.lastScanned).toLocaleDateString()}
          </p>
        </div>
      </div>
    </div>
  )
}

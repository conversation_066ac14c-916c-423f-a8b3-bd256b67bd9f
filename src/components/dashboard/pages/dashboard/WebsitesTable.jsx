'use client'

import { DataTable } from 'primereact/datatable'
import { Column } from 'primereact/column'
import { useTheme } from '@/contexts/ThemeProvider'
import { PrimeReactTableStyles } from '@/components/dashboard/ui/PrimeReactTableStyles'

export function WebsitesTable({ websites }) {
  const { theme } = useTheme()

  const statusBodyTemplate = (rowData) => {
    const isActive = rowData.status === 'active'
    const activeColor = theme === 'dark' ? 'text-green-400' : 'text-green-600'
    const inactiveColor = theme === 'dark' ? 'text-gray-500' : 'text-gray-500'
    const iconActiveColor = theme === 'dark' ? 'text-green-500' : 'text-green-500'
    const iconInactiveColor = theme === 'dark' ? 'text-gray-600' : 'text-gray-400'

    return (
      <span
        className={`font-mono text-xs ${
          isActive ? activeColor : inactiveColor
        }`}
      >
        <i
          className={`pi pi-circle-fill mr-2 text-[8px] ${
            isActive ? iconActiveColor : iconInactiveColor
          }`}
        />
        {rowData.status}
      </span>
    )
  }

  const creditsBodyTemplate = (rowData) => {
    const textColor = theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
    const usedColor = theme === 'dark' ? 'text-gray-300' : 'text-gray-800'
    const separatorColor = theme === 'dark' ? 'text-gray-600' : 'text-gray-400'
    const creditColor = theme === 'dark' ? 'text-gray-400' : 'text-gray-500'

    return (
      <div className={`font-mono text-xs ${textColor}`}>
        <span className={usedColor}>{rowData.used}</span>
        <span className={separatorColor}> / </span>
        <span className={creditColor}>{rowData.credit}</span>
      </div>
    )
  }

  const domainBodyTemplate = (rowData) => (
    <span
      className={`font-mono text-sm ${theme === 'dark' ? 'text-gray-100' : 'text-gray-900'}`}
    >
      {rowData.domain}
    </span>
  )

  const keyBodyTemplate = (rowData) => {
    const textColor = theme === 'dark' ? 'text-gray-500' : 'text-gray-600'
    
    // Handle missing API key
    const displayKey = rowData.key || 'Not Available'
    const isAvailable = !!rowData.key
    
    const handleCopyKey = (key) => {
      navigator.clipboard.writeText(key).then(() => {
        // Optional: Add toast notification here
        console.log('API Key copied to clipboard!')
      }).catch(err => {
        console.error('Failed to copy API Key:', err)
      })
    }
    
    return (
      <div className="flex items-center gap-2">
        <span 
          className={`font-mono text-xs font-light ${textColor} ${!isAvailable ? 'italic' : 'cursor-pointer hover:text-blue-500 select-all'}`}
          onClick={isAvailable ? () => handleCopyKey(displayKey) : undefined}
          title={isAvailable ? 'Click to copy API Key' : ''}
        >
          {displayKey}
        </span>
        {isAvailable && (
          <button
            onClick={() => handleCopyKey(displayKey)}
            className={`p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-800 ${textColor} hover:text-blue-500 transition-colors`}
            title="Copy API Key"
          >
            <i className="pi pi-copy text-xs" />
          </button>
        )}
      </div>
    )
  }

  const lastScannedBodyTemplate = (rowData) => {
    const textColor = theme === 'dark' ? 'text-gray-400' : 'text-gray-500'
    
    // Handle invalid date
    const formatDate = (dateString) => {
      if (!dateString) return 'Never'
      
      try {
        const date = new Date(dateString)
        if (isNaN(date.getTime())) return 'Invalid Date'
        return date.toLocaleString('id-ID', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        })
      } catch (error) {
        return 'Invalid Date'
      }
    }
    
    return (
      <span className={`font-mono text-xs ${textColor}`}>
        {formatDate(rowData.lastScanned)}
      </span>
    )
  }

  return (
    <>
      <PrimeReactTableStyles />
      <DataTable
        key={theme}
        value={websites}
        paginator
        rows={10}
        emptyMessage={
          <span className="font-mono text-sm text-gray-500">
            no_records_found
          </span>
        }
        className={`p-datatable ${theme === 'dark' ? 'dark-table' : 'light-table'}`}
        paginatorClassName={`border-t ${theme === 'dark' ? 'border-gray-800' : 'border-gray-100'}`}
      >
        <Column
          field="domain"
          header="Domain"
          sortable
          body={domainBodyTemplate}
          headerClassName="text-gray-400 font-mono"
        />
        <Column
          field="status"
          header="Status"
          body={statusBodyTemplate}
          sortable
          headerClassName="text-gray-400 font-mono"
        />
        <Column
          field="credit"
          header="Credits"
          body={creditsBodyTemplate}
          sortable
          headerClassName="text-gray-400 font-mono"
        />
        <Column
          field="lastScanned"
          header="Last Scan"
          body={lastScannedBodyTemplate}
          sortable
          headerClassName="text-gray-400 font-mono"
        />
        <Column
          field="key"
          header="API Key"
          body={keyBodyTemplate}
          headerClassName="text-gray-400 font-mono"
        />
      </DataTable>
    </>
  )
}

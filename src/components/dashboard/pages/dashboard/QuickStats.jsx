import { StatCard } from './StatCard'

export function QuickStats({ user, theme }) {
  // Handle case where user data is not available
  if (!user) {
    return (
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, index) => (
          <div
            key={index}
            className="animate-pulse rounded-lg bg-gray-200 p-6 dark:bg-gray-700"
          >
            <div className="h-4 w-16 rounded bg-gray-300 dark:bg-gray-600"></div>
            <div className="mt-2 h-8 w-12 rounded bg-gray-300 dark:bg-gray-600"></div>
          </div>
        ))}
      </div>
    )
  }

  const stats = [
    {
      title: 'Websites',
      value: user?.accountDetails?.limits?.currentUsage?.websites || 0,
      // Option 1: Keep showing limit (1/10)
      total: user?.accountDetails?.limits?.restrictions?.websiteLimit || 0,
      // Option 2: Only show current count (uncomment line below and comment line above)
      // total: null, // This will show just "1" without "/10"
      icon: 'pi pi-globe',
    },
    {
      title: 'Credits',
      value: user?.metrics?.totalCreditsUsed || 0,
      total: user?.accountDetails?.limits?.restrictions?.creditLimit || 0,
      icon: 'pi pi-ticket',
    },
    {
      title: 'Posts',
      value: user?.metrics?.totalScans || 0,
      icon: 'pi pi-send',
    },
    {
      title: 'Plan',
      value: user?.accountDetails?.subscription?.currentPlan?.toLowerCase() || 'free',
      icon: 'pi pi-star',
    },
  ]

  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
      {stats.map((stat) => (
        <StatCard
          className={theme === 'dark' ? 'dark:bg-[#151821] dark:border-white/5' : 'bg-white border border-gray-200'}
          key={stat.title}
          theme={theme}
          title={stat.title}
          value={stat.value}
          total={stat.total}
          icon={stat.icon}
        />
      ))}
    </div>
  )
}

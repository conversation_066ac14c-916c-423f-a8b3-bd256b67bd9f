'use client'

import { useState } from 'react'
import { useTheme } from '@/contexts/ThemeProvider'

const tabs = [
  { name: 'Profile' },
  { name: 'Password' },
  { name: 'Notifications' },
  { name: 'Billing' },
]

export function SettingsContent() {
  const [activeTab, setActiveTab] = useState('Profile')
  const [isSaving, setIsSaving] = useState(false)
  const { theme } = useTheme()

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsSaving(true)
    await new Promise((resolve) => setTimeout(resolve, 1000))
    setIsSaving(false)
  }

  return (
    <div className="mx-auto max-w-full space-y-6">
      {/* Header */}
      <div className={`relative overflow-hidden rounded-md border border-gray-200 dark:border-white/5 ${theme === 'dark' ? 'bg-gradient-to-b from-[#0f1117] to-[#151821] ' : 'bg-white '}`}>
        {/* Enhanced glow effect */}
        <div className={`absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent ${theme === 'dark' ? 'via-blue-500/30' : 'via-blue-500/10'} to-transparent`} />

        <div className={`px-6 py-4 ${theme === 'dark' ? '' : 'bg-white'}`}>
          <h1 className={`font-mono text-lg ${theme === 'dark' ? 'text-blue-400' : 'text-blue-600'}`}>Settings</h1>
          <p className={`mt-1 font-mono text-xs ${theme === 'dark' ? 'text-gray-500' : 'text-gray-600'}`}>
            Manage your account settings and preferences
          </p>
        </div>
      </div>

      {/* Tabs */}
      <div className={`relative overflow-hidden rounded-md border border-gray-200 dark:border-white/5 ${theme === 'dark' ? 'bg-gradient-to-b from-[#0f1117] to-[#151821] ' : 'bg-white '}`}>
        {/* Enhanced glow effect */}
        <div className={`absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent ${theme === 'dark' ? 'via-blue-500/30' : 'via-blue-500/10'} to-transparent`} />

        <nav className={`flex border-b ${theme === 'dark' ? 'border-blue-900/20' : 'border-gray-200'}`}>
          {tabs.map((tab) => (
            <button
              key={tab.name}
              onClick={() => setActiveTab(tab.name)}
              className={`px-6 py-3 font-mono text-sm transition-colors ${
                activeTab === tab.name
                  ? `${theme === 'dark' ? 'border-b-2 border-blue-500 bg-blue-500/10 text-blue-400' : 'border-b-2 border-blue-500 bg-blue-500/10 text-blue-600'}`
                  : `${theme === 'dark' ? 'text-gray-400 hover:bg-blue-500/5 hover:text-blue-300' : 'text-gray-700 hover:bg-blue-500/5 hover:text-blue-600'}`
              } `}
            >
              {tab.name}
            </button>
          ))}
        </nav>

        {/* Content */}
        <div className="p-6">
          {activeTab === 'Profile' && (
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label className={`font-mono text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-700'}`}>
                  Email address
                </label>
                <input
                  type="email"
                  className={`mt-2 block w-full rounded border ${theme === 'dark' ? 'border-blue-900/20 bg-[#151821] text-gray-300 placeholder:text-gray-400' : 'border-gray-300 bg-gray-50 text-gray-900 placeholder:text-gray-400'} px-3 py-2 font-mono text-sm focus:border-blue-500/50 focus:ring-1 focus:ring-blue-500/20`}
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label className={`font-mono text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-700'}`}>
                  Password
                </label>
                <input
                  type="password"
                  className={`mt-2 block w-full rounded border ${theme === 'dark' ? 'border-blue-900/20 bg-[#151821] text-gray-300 placeholder:text-gray-400' : 'border-gray-300 bg-gray-50 text-gray-900 placeholder:text-gray-400'} px-3 py-2 font-mono text-sm focus:border-blue-500/50 focus:ring-1 focus:ring-blue-500/20`}
                  placeholder="••••••••"
                />
              </div>

              <div>
                <label className={`font-mono text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-700'}`}>
                  Timezone
                </label>
                <select className={`mt-2 block w-full rounded border ${theme === 'dark' ? 'border-blue-900/20 bg-[#151821] text-gray-300' : 'border-gray-300 bg-gray-50 text-gray-900'} px-3 py-2 font-mono text-sm focus:border-blue-500/50 focus:ring-1 focus:ring-blue-500/20`}>
                  <option>Asia/Jakarta (GMT+7)</option>
                  <option>Asia/Singapore (GMT+8)</option>
                </select>
              </div>

              <div className="flex justify-end gap-3 pt-6">
                <button
                  type="button"
                  className={`rounded px-4 py-2 font-mono text-sm ${theme === 'dark' ? 'text-gray-400 hover:text-blue-400' : 'text-gray-700 hover:text-blue-600'}`}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSaving}
                  className={`flex items-center gap-2 rounded ${theme === 'dark' ? 'bg-blue-500/20 text-blue-400 hover:bg-blue-500/30' : 'bg-blue-500 text-white hover:bg-blue-600'} px-4 py-2 font-mono text-sm transition-colors`}
                >
                  {isSaving ? (
                    <>
                      <i className="pi pi-spin pi-spinner" />
                      Saving...
                    </>
                  ) : (
                    'Save changes'
                  )}
                </button>
              </div>
            </form>
          )}

          {activeTab === 'Password' && (
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label className={`font-mono text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-700'}`}>
                  Current password
                </label>
                <input
                  type="password"
                  className={`mt-2 block w-full rounded border ${theme === 'dark' ? 'border-blue-900/20 bg-[#151821] text-gray-300 placeholder:text-gray-400' : 'border-gray-300 bg-gray-50 text-gray-900 placeholder:text-gray-400'} px-3 py-2 font-mono text-sm focus:border-blue-500/50 focus:ring-1 focus:ring-blue-500/20`}
                />
              </div>

              <div>
                <label className={`font-mono text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-700'}`}>
                  New password
                </label>
                <input
                  type="password"
                  className={`mt-2 block w-full rounded border ${theme === 'dark' ? 'border-blue-900/20 bg-[#151821] text-gray-300 placeholder:text-gray-400' : 'border-gray-300 bg-gray-50 text-gray-900 placeholder:text-gray-400'} px-3 py-2 font-mono text-sm focus:border-blue-500/50 focus:ring-1 focus:ring-blue-500/20`}
                />
              </div>

              <div>
                <label className={`font-mono text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-700'}`}>
                  Confirm password
                </label>
                <input
                  type="password"
                  className={`mt-2 block w-full rounded border ${theme === 'dark' ? 'border-blue-900/20 bg-[#151821] text-gray-300 placeholder:text-gray-400' : 'border-gray-300 bg-gray-50 text-gray-900 placeholder:text-gray-400'} px-3 py-2 font-mono text-sm focus:border-blue-500/50 focus:ring-1 focus:ring-blue-500/20`}
                />
              </div>

              <div className="flex justify-end pt-6">
                <button
                  type="submit"
                  className={`flex items-center gap-2 rounded ${theme === 'dark' ? 'bg-blue-500/20 text-blue-400 hover:bg-blue-500/30' : 'bg-blue-500 text-white hover:bg-blue-600'} px-4 py-2 font-mono text-sm transition-colors`}
                >
                  Update password
                </button>
              </div>
            </form>
          )}

          {activeTab === 'Notifications' && (
            <div className="space-y-4">
              <div className={`rounded border ${theme === 'dark' ? 'border-blue-900/20 bg-[#151821]/50' : 'border-gray-200 bg-white'} px-6 py-4`}>
                <p className="font-mono text-sm text-gray-400">
                  Notification settings coming soon...
                </p>
              </div>
            </div>
          )}

          {activeTab === 'Billing' && (
            <div className="space-y-4">
              <div className={`rounded border ${theme === 'dark' ? 'border-blue-900/20 bg-[#151821]/50' : 'border-gray-200 bg-white'} px-6 py-4`}>
                <p className="font-mono text-sm text-gray-400">
                  Billing settings coming soon...
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Subtle scanline effect */}
        <div className="pointer-events-none absolute inset-0 bg-[linear-gradient(to_bottom,rgba(59,130,246,0.02)_50%,transparent_50%)] bg-[length:100%_4px] opacity-20" />
      </div>

      {/* Global scanline effect */}
      <div className="pointer-events-none fixed inset-0 bg-[linear-gradient(to_bottom,rgba(255,255,255,0.03)_50%,transparent_50%)] bg-[length:100%_4px] opacity-20" />
    </div>
  )
}

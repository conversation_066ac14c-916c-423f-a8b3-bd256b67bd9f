'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { CheckIcon } from '@heroicons/react/20/solid'

const plans = [
  {
    name: 'Basic',
    price: '$15',
    duration: 'per month',
    description: 'Essential features for small websites',
    features: [
      'Up to 5 projects',
      '1,000 keywords/month',
      'Basic templates',
      'Community support',
    ],
    cta: 'Select Basic',
  },
  {
    name: 'Pro',
    price: '$40',
    duration: 'for 6 months',
    description: 'Advanced features for growing websites',
    features: [
      'Up to 15 projects',
      '5,000 keywords/month',
      'Advanced templates',
      'Priority support',
    ],
    discount: '56% OFF',
    cta: 'Select Pro',
    popular: true,
  },
  {
    name: 'Enterprise',
    price: '$99',
    duration: 'per year',
    description: 'Complete solution for large websites',
    features: [
      'Unlimited projects',
      '20,000 keywords/month',
      'Custom templates',
      'Dedicated support',
      'Advanced analytics',
    ],
    cta: 'Select Enterprise',
  },
]

// Add free plan data
const freePlan = {
  name: 'Starter',
  price: '$0',
  duration: 'forever',
  description: 'Get started with essential security features',
  features: [
    'Single project',
    '100 keywords/month',
    'Basic scan reports',
    'Community forum access',
    'Email notifications',
  ],
  cta: 'Current Plan',
  isFree: true,
}

export function PlansContentTerm() {
  const [selectedPlan, setSelectedPlan] = useState('Pro')
  const router = useRouter()

  const handleSelectPlan = (planName) => {
    setSelectedPlan(planName)
    router.push(`/payment?plan=${planName}`)
  }

  return (
    <div className="mx-auto max-w-full space-y-6">
      {/* Header */}
      <div className="relative overflow-hidden rounded-md bg-gradient-to-b from-[#0f1117] to-[#151821] shadow-lg">
        <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-blue-500/30 to-transparent" />
        <div className="px-6 py-4">
          <h1 className="font-mono text-lg text-blue-400">
            Subscription Plans
          </h1>
          <p className="mt-1 font-mono text-xs text-gray-500">
            Choose the plan that best fits your needs
          </p>
        </div>
      </div>

      {/* Free Plan - Displayed at the top */}
      <div className="relative overflow-hidden rounded-md bg-gradient-to-b from-[#0f1117] to-[#151821] shadow-lg">
        <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-blue-500/30 to-transparent" />
        <div className="flex flex-col md:flex-row">
          <div className="flex-1 p-6">
            <div className="flex items-center gap-3">
              <h2 className="font-mono text-base font-bold uppercase text-gray-300">
                {freePlan.name}
              </h2>
              <div className="rounded border border-blue-900/20 bg-blue-500/10 px-2 py-0.5 font-mono text-xs font-bold uppercase text-blue-400">
                Free Forever
              </div>
            </div>
            <div className="mt-2 flex items-baseline">
              <span className="font-mono text-2xl font-black text-gray-300">
                {freePlan.price}
              </span>
              <span className="ml-1 font-mono text-xs font-bold uppercase text-gray-500">
                {freePlan.duration}
              </span>
            </div>
            <p className="mt-2 font-mono text-xs text-gray-400">
              {freePlan.description}
            </p>

            <div className="mt-4 rounded border border-blue-900/20 bg-[#0f1117] p-3">
              <p className="font-mono text-xs font-bold uppercase text-gray-400">
                Perfect for individuals and small projects
              </p>
            </div>
          </div>

          <div className="flex-1 border-t border-blue-900/20 p-6 md:border-l md:border-t-0">
            <ul className="space-y-2">
              {freePlan.features.map((feature) => (
                <li key={feature} className="flex items-start">
                  <CheckIcon className="mr-2 h-4 w-4 flex-shrink-0 text-blue-500" />
                  <span className="font-mono text-xs font-bold uppercase text-gray-400">
                    {feature}
                  </span>
                </li>
              ))}
            </ul>
            <button className="w-full cursor-default rounded bg-gray-500/20 py-2 font-mono text-sm font-bold uppercase text-gray-400">
              {freePlan.cta}
            </button>
          </div>
        </div>
      </div>

      {/* Paid Plans */}
      <div className="relative overflow-hidden rounded-md bg-gradient-to-b from-[#0f1117] to-[#151821] shadow-lg">
        <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-blue-500/30 to-transparent" />
        <div className="p-6">
          <div className="grid gap-6 lg:grid-cols-3">
            {plans.map((plan) => (
              <div
                key={plan.name}
                className={`relative cursor-pointer overflow-hidden rounded border p-6 transition-all ${
                  selectedPlan === plan.name
                    ? 'border-blue-500/50 bg-blue-500/10'
                    : 'border-blue-900/20 bg-[#151821]/50 hover:border-blue-500/30 hover:bg-blue-500/5'
                }`}
                onClick={() => setSelectedPlan(plan.name)}
              >
                {plan.popular && (
                  <div className="absolute right-0 top-0">
                    <div className="rounded-bl border-b border-l border-blue-900/20 bg-blue-500/20 px-3 py-1 font-mono text-xs font-bold uppercase text-blue-400">
                      Popular
                    </div>
                  </div>
                )}

                <div className="mb-4">
                  <h2 className="font-mono text-base font-bold uppercase text-gray-300">
                    {plan.name}
                  </h2>
                  <div className="mt-2 flex items-baseline">
                    <span className="font-mono text-2xl font-black text-gray-300">
                      {plan.price}
                    </span>
                    <span className="ml-1 font-mono text-xs font-bold uppercase text-gray-500">
                      {plan.duration}
                    </span>
                  </div>
                  {plan.discount && (
                    <div className="mt-1 inline-block rounded border border-blue-900/20 bg-blue-500/10 px-2 py-0.5 font-mono text-xs font-bold uppercase text-blue-400">
                      {plan.discount}
                    </div>
                  )}
                  <p className="mt-2 font-mono text-xs text-gray-400">
                    {plan.description}
                  </p>
                </div>

                <ul className="mb-6 space-y-2">
                  {plan.features.map((feature) => (
                    <li key={feature} className="flex items-start">
                      <CheckIcon className="mr-2 h-4 w-4 flex-shrink-0 text-blue-500" />
                      <span className="font-mono text-xs font-bold uppercase text-gray-400">
                        {feature}
                      </span>
                    </li>
                  ))}
                </ul>

                <button
                  onClick={() => handleSelectPlan(plan.name)}
                  className={`w-full rounded py-2 font-mono text-sm font-bold uppercase transition-colors ${
                    selectedPlan === plan.name
                      ? 'bg-blue-500 text-white hover:bg-blue-600'
                      : 'bg-blue-500/20 text-blue-400 hover:bg-blue-500/30'
                  }`}
                >
                  {selectedPlan === plan.name ? 'Upgrade Plan' : plan.cta}
                </button>
              </div>
            ))}
          </div>

          <div className="mt-8 rounded border border-blue-900/20 bg-[#151821]/50 p-4">
            <p className="font-mono text-xs font-bold uppercase text-gray-400">
              All paid plans include a 14-day free trial. No credit card
              required to start. Cancel anytime.
            </p>
          </div>
        </div>

        {/* Global scanline effect */}
        <div className="pointer-events-none fixed inset-0 bg-[linear-gradient(to_bottom,rgba(255,255,255,0.03)_50%,transparent_50%)] bg-[length:100%_4px] opacity-20" />
      </div>
    </div>
  )
}

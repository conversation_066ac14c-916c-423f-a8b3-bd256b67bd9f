'use client'

import { useState } from 'react'
import { useTheme } from '@/contexts/ThemeProvider'
import { useRouter } from 'next/navigation'
import { CheckIcon } from '@heroicons/react/20/solid'
import { useUser } from '@/hooks/useUser'

const plans = [
  {
    name: 'Basic',
    price: '$15',
    duration: 'per month',
    description: 'Essential auto-posting for small WordPress sites',
    features: [
      'Connect up to 5 WordPress sites',
      '100 auto-posts per month',
      'Simple keyword targeting',
      'Community forum access',
    ],
    cta: 'Get Started',
  },
  {
    name: 'Pro',
    price: '$40',
    duration: 'for 6 months',
    description: 'Advanced auto-posting for growing WordPress sites',
    features: [
      'Connect up to 15 WordPress sites',
      '500 auto-posts per month',
      'Smart keyword targeting',
      'Priority email support',
    ],
    discount: '56% OFF',
    cta: 'Choose Pro',
    popular: true,
  },
  {
    name: 'Enterprise',
    price: '$99',
    duration: 'per year',
    description: 'Complete auto-posting solution for WordPress professionals',
    features: [
      'Unlimited WordPress sites',
      '2,000 auto-posts per month',
      'Custom content templates',
      'Dedicated support team',
      'Advanced analytics dashboard',
    ],
    cta: 'Choose Enterprise',
  },
]

// Free plan data
const freePlan = {
  name: 'Starter',
  price: '$0',
  duration: 'forever',
  description: 'Basic WordPress auto-posting essentials',
  features: [
    'For a single WordPress site',
    '10 auto-posts per month',
    'Basic keyword targeting',
    'Community forum access',
    'Simple post scheduling',
  ],
  cta: 'Current Plan',
  isFree: true,
}

export function PlansContent() {
  const [selectedPlan, setSelectedPlan] = useState('Pro')
  const router = useRouter()
  const { theme } = useTheme()
  const { data: user, isLoading } = useUser()

  // Get current user plan (normalize to match plan names)
  const currentUserPlan = user?.accountDetails?.subscription?.currentPlan?.toLowerCase()
  const normalizedCurrentPlan = currentUserPlan === 'starter' ? 'free' : currentUserPlan

  const handlePlanSelection = (planName) => {
    // Don't allow selection of current plan
    if (planName.toLowerCase() === currentUserPlan) {
      return
    }
    router.push(`/payment?plan=${planName}`)
  }

  // Helper function to get button text and state
  const getButtonState = (planName) => {
    const planLower = planName.toLowerCase()
    
    if (planLower === currentUserPlan) {
      return { text: 'Current Plan', disabled: true, isCurrent: true }
    } else if (planName === 'Starter') {
      return { text: 'Downgrade', disabled: false, isCurrent: false }
    } else {
      return { text: `Upgrade to ${planName}`, disabled: false, isCurrent: false }
    }
  }

  return (
    <div className="mx-auto max-w-full space-y-6">
      {/* Header */}
      <div className={`relative overflow-hidden rounded-md border border-gray-200 dark:border-white/5 ${theme === 'dark' ? 'bg-gradient-to-b from-[#0f1117] to-[#151821] ' : 'bg-white '}`}>
        <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-blue-500/30 to-transparent" />
        <div className="px-6 py-4 text-center">
          <div className="inline-flex items-center space-x-2 rounded border border-blue-200/20 bg-blue-500/10 px-3 py-1 font-mono text-sm font-bold uppercase text-blue-600 dark:border-white/5 dark:bg-blue-500/10 dark:text-blue-400">
            <span>Pricing Options</span>
          </div>

          <h1 className="mt-6 font-mono text-[clamp(2rem,4vw,3rem)] font-black uppercase leading-tight text-gray-800 dark:text-gray-300">
            WordPress{' '}
            <span className="relative inline-block text-blue-500 dark:text-blue-400">
              Superpowers
            </span>
          </h1>

          <p className="mx-auto mt-8 max-w-4xl font-mono text-base leading-relaxed text-gray-600 dark:text-gray-400">
            <span className="font-bold">
              Experience the next evolution of WordPress content creation with
              AI-powered automation and intelligent optimization.
            </span>
          </p>
        </div>
      </div>

      <div className="mx-auto max-w-full space-y-6">
        {/* Free Plan - Displayed at the top */}
        <div className={`relative overflow-hidden rounded-md border border-gray-200 dark:border-white/5 ${theme === 'dark' ? 'bg-gradient-to-b from-[#0f1117] to-[#151821] ' : 'bg-white '}`}>
          <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-blue-500/30 to-transparent" />
          <div className="flex flex-col md:flex-row">
            <div className="flex-1 p-6">
              <div className="flex items-center gap-3">
                <h2 className="font-mono text-base font-bold uppercase text-gray-800 dark:text-gray-300">
                  {freePlan.name}
                </h2>
                <div className="rounded border border-blue-200/20 bg-blue-500/10 px-2 py-0.5 font-mono text-xs font-bold uppercase text-blue-600 dark:border-white/5 dark:bg-blue-500/10 dark:text-blue-400">
                  Free Forever
                </div>
                {getButtonState('Starter').isCurrent && (
                  <div className="rounded border border-green-200/20 bg-green-500/10 px-2 py-0.5 font-mono text-xs font-bold uppercase text-green-600 dark:border-green-500/20 dark:bg-green-500/10 dark:text-green-400">
                    Current
                  </div>
                )}
              </div>
              <div className="mt-2 flex items-baseline">
                <span className="font-mono text-2xl font-black text-gray-800 dark:text-gray-300">
                  {freePlan.price}
                </span>
                <span className="ml-1 font-mono text-xs font-bold uppercase text-gray-500">
                  {freePlan.duration}
                </span>
              </div>
              <p className="mt-2 font-mono text-xs text-gray-600 dark:text-gray-400">
                {freePlan.description}
              </p>

              <div className="mt-4 rounded border border-gray-200 bg-gray-100 p-3 dark:border-white/5 dark:bg-[#0f1117]">
                <p className="font-mono text-xs font-bold uppercase text-gray-600 dark:text-gray-400">
                  Perfect for individual WordPress sites and content creators
                </p>
              </div>
            </div>

            <div className="flex-1 border-t border-gray-200 p-6 md:border-l md:border-t-0 dark:border-white/5">
              <ul className="space-y-2">
                {freePlan.features.map((feature) => (
                  <li key={feature} className="flex items-start">
                    <CheckIcon className="mr-2 h-4 w-4 flex-shrink-0 text-blue-500" />
                    <span className="font-mono text-xs font-bold uppercase text-gray-600 dark:text-gray-400">
                      {feature}
                    </span>
                  </li>
                ))}
              </ul>
              <button 
                className={`mt-4 w-full rounded py-2 font-mono text-sm font-bold uppercase transition-colors ${
                  getButtonState('Starter').isCurrent
                    ? 'cursor-default bg-gray-200 text-gray-500 dark:bg-gray-500/20 dark:text-gray-400'
                    : 'cursor-pointer bg-blue-500 text-white hover:bg-blue-600 dark:bg-blue-500/20 dark:text-blue-400 dark:hover:bg-blue-500/30'
                }`}
                disabled={getButtonState('Starter').disabled}
                onClick={() => !getButtonState('Starter').disabled && handlePlanSelection('Starter')}
              >
                {getButtonState('Starter').text}
              </button>
            </div>
          </div>
        </div>

        {/* Paid Plans */}
        <div className={`relative overflow-hidden rounded-md border border-gray-200 dark:border-white/5 ${theme === 'dark' ? 'bg-gradient-to-b from-[#0f1117] to-[#151821] ' : 'bg-white '}`}>
          <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-blue-500/30 to-transparent" />
          <div className="p-6">
            <div className="grid gap-6 lg:grid-cols-3">
              {plans.map((plan) => (
                <div
                  key={plan.name}
                  className={`relative cursor-pointer overflow-hidden rounded border p-6 transition-all ${
                    selectedPlan === plan.name
                      ? 'border-blue-500/50 bg-blue-500/5'
                      : 'border-gray-200 bg-white hover:border-blue-500/30 hover:bg-blue-500/5 dark:border-white/5 dark:bg-[#151821]/50 dark:hover:border-blue-500/30 dark:hover:bg-blue-500/5'
                  }`}
                  onClick={() => setSelectedPlan(plan.name)}
                >
                  {plan.popular && (
                    <div className="absolute right-0 top-0">
                      <div className="rounded-bl border-b border-l border-blue-200/20 bg-blue-500/20 px-3 py-1 font-mono text-xs font-bold uppercase text-blue-600 dark:border-white/5 dark:bg-blue-500/20 dark:text-blue-400">
                        Popular
                      </div>
                    </div>
                  )}

                  <div className="mb-4">
                    <div className="flex items-center gap-3 mb-2">
                      <h2 className="font-mono text-base font-bold uppercase text-gray-800 dark:text-gray-300">
                        {plan.name}
                      </h2>
                      {getButtonState(plan.name).isCurrent && (
                        <div className="rounded border border-green-200/20 bg-green-500/10 px-2 py-0.5 font-mono text-xs font-bold uppercase text-green-600 dark:border-green-500/20 dark:bg-green-500/10 dark:text-green-400">
                          Current
                        </div>
                      )}
                    </div>
                    <div className="mt-2 flex items-baseline">
                      <span className="font-mono text-2xl font-black text-gray-800 dark:text-gray-300">
                        {plan.price}
                      </span>
                      <span className="ml-1 font-mono text-xs font-bold uppercase text-gray-500">
                        {plan.duration}
                      </span>
                    </div>
                    {plan.discount && (
                      <div className="mt-1 inline-block rounded border border-blue-200/20 bg-blue-500/10 px-2 py-0.5 font-mono text-xs font-bold uppercase text-blue-600 dark:border-white/5 dark:bg-blue-500/10 dark:text-blue-400">
                        {plan.discount}
                      </div>
                    )}
                    <p className="mt-2 font-mono text-xs text-gray-600 dark:text-gray-400">
                      {plan.description}
                    </p>
                  </div>

                  <ul className="mb-6 space-y-2">
                    {plan.features.map((feature) => (
                      <li key={feature} className="flex items-start">
                        <CheckIcon className="mr-2 h-4 w-4 flex-shrink-0 text-blue-500" />
                        <span className="font-mono text-xs font-bold uppercase text-gray-600 dark:text-gray-400">
                          {feature}
                        </span>
                      </li>
                    ))}
                  </ul>

                  <button
                    onClick={() => !getButtonState(plan.name).disabled && handlePlanSelection(plan.name)}
                    disabled={getButtonState(plan.name).disabled}
                    className={`relative w-full rounded py-2 font-mono text-sm font-bold uppercase transition-colors ${
                      getButtonState(plan.name).isCurrent
                        ? 'cursor-default bg-gray-200 text-gray-500 dark:bg-gray-500/20 dark:text-gray-400'
                        : 'cursor-pointer bg-blue-500 text-white hover:bg-blue-600 dark:bg-blue-500/20 dark:text-blue-400 dark:hover:bg-blue-500/30'
                    }`}
                  >
                    {getButtonState(plan.name).text}
                  </button>
                </div>
              ))}
            </div>

            <div className="mt-8 rounded border border-gray-200 bg-gray-100 p-4 dark:border-white/5 dark:bg-[#151821]/50">
              <p className="font-mono text-xs font-bold uppercase text-gray-600 dark:text-gray-400">
                All paid plans include a 14-day free trial with full access to
                premium auto-posting features. No credit card required to
                start. Cancel anytime.
              </p>
            </div>
          </div>
        </div>

        {/* FAQ Section */}
        <div className={`relative overflow-hidden rounded-md border border-gray-200 dark:border-white/5 ${theme === 'dark' ? 'bg-gradient-to-b from-[#0f1117] to-[#151821] ' : 'bg-white '}`}>
          <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-blue-500/30 to-transparent" />
          <div className="p-6">
            <div className="mb-8 inline-flex items-center space-x-2 rounded border border-blue-200/20 bg-blue-500/10 px-3 py-1 font-mono text-sm font-bold uppercase text-blue-600 dark:border-white/5 dark:bg-blue-500/10 dark:text-blue-400">
              <span>FAQ</span>
            </div>

            <div className="grid gap-6 md:grid-cols-2">
              {[
                {
                  q: 'How does the auto-posting work?',
                  a: 'Our plugin connects to your WordPress site and automatically creates and publishes content based on your keywords. Simply install the plugin, set your topics, and let it generate engaging posts for your site.',
                },
                {
                  q: 'Will this work with my WordPress theme?',
                  a: 'Yes! Our auto-posting plugin is compatible with all WordPress themes and popular page builders including Elementor, Divi, and the default WordPress editor.',
                },
                {
                  q: 'What payment methods do you accept?',
                  a: 'We accept all major credit cards and PayPal. The checkout process is simple and secure - just select your preferred plan and follow the payment steps.',
                },
                {
                  q: 'Can I upgrade my plan later?',
                  a: 'Absolutely! You can upgrade your auto-posting plan at any time. The remaining value of your current plan will be applied as credit toward your new plan.',
                },
              ].map((item, i) => (
                <div
                  key={i}
                  className="rounded border border-gray-200 bg-white p-6 dark:border-white/5 dark:bg-[#151821]/50"
                >
                  <h3 className="font-mono text-lg font-black uppercase text-gray-800 dark:text-gray-300">
                    {item.q}
                  </h3>
                  <p className="mt-2 font-mono text-sm text-gray-600 dark:text-gray-400">
                    {item.a}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Global scanline effect */}
        <div className="pointer-events-none fixed inset-0 bg-[linear-gradient(to_bottom,rgba(255,255,255,0.03)_50%,transparent_50%)] bg-[length:100%_4px] opacity-20" />
      </div>
    </div>
  )
}

export default PlansContent

// components/dashboard/ui/skeletons.jsx
export function DashboardSkeleton() {
  return (
    <div className="space-y-6">
      {/* Header Skeleton */}
      <div className="space-y-2">
        <div className="h-8 w-1/4 animate-pulse rounded-md bg-gray-200" />
        <div className="h-4 w-1/3 animate-pulse rounded-md bg-gray-200" />
      </div>

      {/* Stats Cards Skeleton */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
        {[1, 2, 3].map((i) => (
          <div
            key={i}
            className="h-32 animate-pulse rounded-lg bg-gray-200 p-4"
          />
        ))}
      </div>

      {/* Websites List Skeleton */}
      <div className="space-y-4">
        <div className="h-6 w-1/6 animate-pulse rounded-md bg-gray-200" />
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div
              key={i}
              className="h-24 animate-pulse rounded-lg bg-gray-200"
            />
          ))}
        </div>
      </div>
    </div>
  )
}

export function WebsiteCardSkeleton() {
  return (
    <div className="space-y-3 rounded-lg border p-4">
      <div className="h-5 w-2/3 animate-pulse rounded bg-gray-200" />
      <div className="space-y-2">
        <div className="h-4 w-1/2 animate-pulse rounded bg-gray-200" />
        <div className="h-4 w-1/3 animate-pulse rounded bg-gray-200" />
        <div className="h-4 w-1/4 animate-pulse rounded bg-gray-200" />
      </div>
    </div>
  )
}

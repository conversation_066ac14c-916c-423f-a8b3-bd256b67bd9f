'use client'

import { useTheme } from '@/contexts/ThemeProvider'

export function PrimeReactTableStyles() {
  const { theme } = useTheme()

  const tableStyles = {
    css: `
      .p-datatable {
        background-color: ${theme === 'dark' ? '#0f1117' : '#ffffff'};
        border: 1px solid ${
          theme === 'dark'
            ? 'rgba(59, 130, 246, 0.1)'
            : 'rgba(0, 0, 0, 0.05)'
        };
        font-family: ui-monospace, monospace;
      }
      .p-datatable .p-datatable-wrapper {
        background-color: ${theme === 'dark' ? '#0f1117' : '#ffffff'};
      }
      .p-datatable .p-datatable-thead > tr > th {
        background: ${theme === 'dark' ? '#151821' : '#f9fafb'};
        border: none;
        border-bottom: 1px solid ${
          theme === 'dark'
            ? 'rgba(59, 130, 246, 0.2)'
            : 'rgba(0, 0, 0, 0.05)'
        };
        color: ${theme === 'dark' ? '#9ca3af' : '#4b5563'};
        padding: 0.75rem 1rem;
        font-size: 0.75rem;
      }
      .p-datatable .p-datatable-tbody > tr {
        background-color: ${theme === 'dark' ? '#0f1117' : '#ffffff'};
        border: none;
        transition: background-color 0.2s;
      }
      .p-datatable .p-datatable-tbody > tr > td {
        border: none;
        border-bottom: 1px solid ${
          theme === 'dark'
            ? 'rgba(59, 130, 246, 0.1)'
            : 'rgba(0, 0, 0, 0.05)'
        };
        padding: 0.75rem 1rem;
        color: ${theme === 'dark' ? '#e5e7eb' : '#374151'};
      }
      .p-datatable .p-datatable-tbody > tr:hover {
        background: ${theme === 'dark' ? '#151821' : '#f3f4f6'};
      }
      .p-paginator {
        background: ${theme === 'dark' ? '#151821' : '#f9fafb'};
        border: none;
        padding: 0.75rem;
        border-top: 1px solid ${
          theme === 'dark'
            ? 'rgba(59, 130, 246, 0.1)'
            : 'rgba(0, 0, 0, 0.05)'
        };
      }
      .p-paginator .p-paginator-element {
        color: ${theme === 'dark' ? '#6b7280' : '#6b7280'};
        min-width: 2rem;
        height: 2rem;
        margin: 0 0.125rem;
        font-size: 0.75rem;
        background-color: ${theme === 'dark' ? '#0f1117' : '#ffffff'};
        border: 1px solid ${
          theme === 'dark' ? 'transparent' : 'rgba(0,0,0,0.1)'
        };
        border-radius: 0;
        padding: 0.25rem 0.5rem;
      }
      .p-paginator .p-paginator-element:not(.p-highlight):hover {
        background: ${theme === 'dark' ? '#1a1f2e' : '#e5e7eb'};
        color: ${theme === 'dark' ? '#60a5fa' : '#3b82f6'};
      }
      .p-paginator .p-paginator-element.p-highlight {
        background: ${
          theme === 'dark'
            ? 'rgba(59, 130, 246, 0.2)'
            : 'rgba(59, 130, 246, 0.1)'
        };
        border: 1px solid ${
          theme === 'dark'
            ? 'rgba(59, 130, 246, 0.2)'
            : 'rgba(59, 130, 246, 0.2)'
        };
        color: ${theme === 'dark' ? '#60a5fa' : '#3b82f6'};
      }
      .p-sortable-column:hover {
        background: ${theme === 'dark' ? '#1a1f2e' : '#f3f4f6'} !important;
      }
      .p-sortable-column.p-highlight {
        background: ${theme === 'dark' ? '#151821' : '#f9fafb'} !important;
        color: ${theme === 'dark' ? '#60a5fa' : '#3b82f6'} !important;
      }
      .p-sortable-column.p-highlight .p-sortable-column-icon {
        color: ${theme === 'dark' ? '#60a5fa' : '#3b82f6'} !important;
      }
      .p-sortable-column-icon {
        color: ${theme === 'dark' ? '#4b5563' : '#6b7280'} !important;
      }
    `,
  }

  return <style>{tableStyles.css}</style>
}

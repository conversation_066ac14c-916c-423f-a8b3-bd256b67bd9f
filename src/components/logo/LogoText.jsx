export function LogoText(props) {
  return (
    <div className="inline-flex items-center text-[22px]">
      <span
        className={`-skew-x-12 font-black leading-none tracking-[0.05em] ${props.className}`}
      >
        SUPER
      </span>
      <span
        className={`-ml-[2px] -skew-x-12 font-black leading-none tracking-[0.02em] !text-blue-500 ${props.className}`}
      >
        SENSE
      </span>
    </div>
  )
}

'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { jwtDecode } from 'jwt-decode'
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/20/solid'
import Link from 'next/link'

import { Logo } from '@/components/logo/LogoBolt'
import { LogoText } from '@/components/logo/LogoText'
import { ImageCaptcha } from '@/components/common/ImageCaptcha'

export function ResetPassword() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const token = searchParams.get('token')
  const email = searchParams.get('email')

  const [decodedEmail, setDecodedEmail] = useState('')
  const [isValidToken, setIsValidToken] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [error, setError] = useState('')
  const [success, setSuccess] = useState(false)

  // Form states
  const [newPassword, setNewPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  // CAPTCHA states
  const [captcha, setCaptcha] = useState('')
  const [inputCaptcha, setInputCaptcha] = useState('')

  const generateCaptcha = useCallback(() => {
    // Only use uppercase letters and numbers to avoid case sensitivity issues
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
    let result = ''
    for (let i = 0; i < 6; i++) {
      result += characters.charAt(Math.floor(Math.random() * characters.length))
    }
    setCaptcha(result)
  }, [])

  useEffect(() => {
    generateCaptcha()
  }, [generateCaptcha])

  useEffect(() => {
    if (email) {
      try {
        setDecodedEmail(atob(email))
      } catch (e) {
        console.error('Failed to decode email:', e)
        setError('Invalid email encoding.')
      }
    }
  }, [email])

  useEffect(() => {
    if (token) {
      try {
        const decodedToken = jwtDecode(token)
        if (decodedToken.exp * 1000 > Date.now()) {
          setIsValidToken(true)
          setError('')
        } else {
          setError(
            'Reset link has expired. Please request a new password reset.',
          )
        }
      } catch (error) {
        setError('Invalid reset link. Please request a new password reset.')
      }
    }
  }, [token])

  const validatePassword = (password) => {
    if (password.length < 8) {
      return 'Password must be at least 8 characters long'
    }
    if (!/(?=.*[a-z])/.test(password)) {
      return 'Password must contain at least one lowercase letter'
    }
    if (!/(?=.*[A-Z])/.test(password)) {
      return 'Password must contain at least one uppercase letter'
    }
    if (!/(?=.*\d)/.test(password)) {
      return 'Password must contain at least one number'
    }
    return null
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')
    setMessage('')

    // Validate passwords
    const passwordError = validatePassword(newPassword)
    if (passwordError) {
      setError(passwordError)
      setIsLoading(false)
      return
    }

    if (newPassword !== confirmPassword) {
      setError('Passwords do not match.')
      setIsLoading(false)
      return
    }

    // CAPTCHA validation
    const trimmedInputCaptcha = inputCaptcha.trim().toUpperCase()
    const trimmedCaptcha = captcha.trim().toUpperCase()

    if (trimmedInputCaptcha !== trimmedCaptcha) {
      setError('CAPTCHA verification failed. Please try again.')
      generateCaptcha()
      setInputCaptcha('')
      setIsLoading(false)
      return
    }

    try {
      const response = await fetch('/api/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token,
          newPassword,
          captcha: trimmedCaptcha,
          inputCaptcha: trimmedInputCaptcha,
        }),
      })

      const data = await response.json()

      if (response.ok) {
        setSuccess(true)
        setMessage(
          'Password has been reset successfully! Redirecting to login...',
        )
        setTimeout(() => {
          router.push('/login')
        }, 3000)
      } else {
        setError(data.message || 'Failed to reset password. Please try again.')
        generateCaptcha()
        setInputCaptcha('')
      }
    } catch (error) {
      setError('An error occurred. Please try again.')
      generateCaptcha()
      setInputCaptcha('')
    }

    setIsLoading(false)
  }

  if (!token || !email) {
    return (
      <div className="flex min-h-screen flex-col bg-[repeating-conic-gradient(#fff_0deg,#fff_90deg,#f8f8f8_90deg,#f8f8f8_180deg)] [background-size:20px_20px]">
        <div className="flex flex-1 flex-col items-center justify-center px-4 py-12 sm:px-6 lg:px-8">
          <div className="w-full max-w-md">
            {/* Logo */}
            <Link className="mb-2 flex items-center justify-center" href="/">
              <Logo className="h-16" />
            </Link>

            {/* Error Card */}
            <div className="relative border-4 border-black bg-gray-100 p-8 shadow-[8px_8px_0_0_#064ADA]">
              <h2 className="mb-6 text-center font-mono text-xl font-black uppercase text-black">
                Invalid Access
              </h2>

              {/* Error Message */}
              <div className="mb-4 border-2 border-red-500 bg-red-100 p-3 text-center">
                <p className="font-mono text-sm font-bold text-red-700">
                  Access denied! Use the reset link from your email to access this page.
                </p>
              </div>

              <Link
                href="/forgot-password"
                className="relative w-full border-2 border-black bg-[#064ADA] px-4 py-2 font-mono text-sm font-bold uppercase text-white shadow-[3px_3px_0_0_#000] transition-all hover:shadow-[4px_4px_0px_0px_#000] block text-center"
              >
                Request New Reset Link
              </Link>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!isValidToken) {
    return (
      <div className="flex min-h-screen flex-col bg-[repeating-conic-gradient(#fff_0deg,#fff_90deg,#f8f8f8_90deg,#f8f8f8_180deg)] [background-size:20px_20px]">
        <div className="flex flex-1 flex-col items-center justify-center px-4 py-12 sm:px-6 lg:px-8">
          <div className="w-full max-w-md">
            {/* Logo */}
            <Link className="mb-2 flex items-center justify-center" href="/">
              <Logo className="h-16" />
            </Link>

            {/* Expired Token Card */}
            <div className="relative border-4 border-black bg-gray-100 p-8 shadow-[8px_8px_0_0_#064ADA]">
              <h2 className="mb-6 text-center font-mono text-xl font-black uppercase text-black">
                Link Expired
              </h2>

              {/* Error Message */}
              <div className="mb-4 border-2 border-red-500 bg-red-100 p-3 text-center">
                <p className="font-mono text-sm font-bold text-red-700">
                  {error || 'Password reset link has expired. Please request a new reset link.'}
                </p>
              </div>

              <Link
                href="/forgot-password"
                className="relative w-full border-2 border-black bg-[#064ADA] px-4 py-2 font-mono text-sm font-bold uppercase text-white shadow-[3px_3px_0_0_#000] transition-all hover:shadow-[4px_4px_0px_0px_#000] block text-center"
              >
                Request New Reset Link
              </Link>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen flex-col bg-[repeating-conic-gradient(#fff_0deg,#fff_90deg,#f8f8f8_90deg,#f8f8f8_180deg)] [background-size:20px_20px]">
      <div className="flex flex-1 flex-col items-center justify-center px-4 py-12 sm:px-6 lg:px-8">
        <div className="w-full max-w-md">
          {/* Logo */}
          <Link className="mb-2 flex items-center justify-center" href="/">
            <Logo className="h-16" />
          </Link>

          {/* Reset Password Card */}
          <div className="relative border-4 border-black bg-gray-100 p-8 shadow-[8px_8px_0_0_#064ADA]">
            <h2 className="mb-6 text-center font-mono text-xl font-black uppercase text-black">
              Reset Password
            </h2>

            {/* Account Info */}
            {decodedEmail && (
              <div className="mb-4 border-2 border-[#064ADA] bg-blue-50 p-3 text-center">
                <p className="font-mono text-sm font-bold text-black">
                  Account: <span className="text-[#064ADA]">{decodedEmail}</span>
                </p>
              </div>
            )}

            {/* Error Message */}
            {error && (
              <div className="mb-4 border-2 border-red-500 bg-red-100 p-3 text-center">
                <p className="font-mono text-sm font-bold text-red-700">
                  {error}
                </p>
              </div>
            )}

            {/* Success Message */}
            {message && (
              <div className="mb-4 border-2 border-green-500 bg-green-100 p-3 text-center">
                <p className="font-mono text-sm font-bold text-green-700">
                  {message}
                </p>
              </div>
            )}

            {!success && (
              <form className="space-y-6" onSubmit={handleSubmit}>
                {/* New Password */}
                <div>
                  <label
                    htmlFor="newPassword"
                    className="font-mono text-sm font-bold uppercase text-black"
                  >
                    New Password
                  </label>
                  <div className="relative">
                    <input
                      id="newPassword"
                      name="newPassword"
                      type={showPassword ? 'text' : 'password'}
                      value={newPassword}
                      onChange={(e) => setNewPassword(e.target.value)}
                      required
                      className="mt-2 block w-full border-2 border-black bg-white px-4 py-2.5 pr-10 font-mono text-sm text-black placeholder:text-gray-500 focus:border-[#064ADA] focus:outline-none"
                      placeholder="Enter new password"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute inset-y-0 right-0 flex items-center pr-3 focus:outline-none"
                    >
                      {showPassword ? (
                        <EyeSlashIcon className="h-5 w-5 text-gray-500" />
                      ) : (
                        <EyeIcon className="h-5 w-5 text-gray-500" />
                      )}
                    </button>
                  </div>
                  <p className="mt-1 font-mono text-xs text-gray-600">
                    Min 8 chars, uppercase, lowercase, number
                  </p>
                </div>

                {/* Confirm Password */}
                <div>
                  <label
                    htmlFor="confirmPassword"
                    className="font-mono text-sm font-bold uppercase text-black"
                  >
                    Confirm Password
                  </label>
                  <div className="relative">
                    <input
                      id="confirmPassword"
                      name="confirmPassword"
                      type={showConfirmPassword ? 'text' : 'password'}
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      required
                      className="mt-2 block w-full border-2 border-black bg-white px-4 py-2.5 pr-10 font-mono text-sm text-black placeholder:text-gray-500 focus:border-[#064ADA] focus:outline-none"
                      placeholder="Confirm new password"
                    />
                    <button
                      type="button"
                      onClick={() =>
                        setShowConfirmPassword(!showConfirmPassword)
                      }
                      className="absolute inset-y-0 right-0 flex items-center pr-3 focus:outline-none"
                    >
                      {showConfirmPassword ? (
                        <EyeSlashIcon className="h-5 w-5 text-gray-500" />
                      ) : (
                        <EyeIcon className="h-5 w-5 text-gray-500" />
                      )}
                    </button>
                  </div>
                </div>

                {/* CAPTCHA */}
                <div>
                  <label
                    htmlFor="captcha"
                    className="block font-mono text-sm font-bold uppercase text-black"
                  >
                    CAPTCHA
                  </label>
                  <div className="mt-2 space-y-3">
                    {/* CAPTCHA Display */}
                    <ImageCaptcha 
                      captcha={captcha} 
                      onRefresh={() => {
                        generateCaptcha()
                        setInputCaptcha('')
                      }} 
                    />

                    {/* CAPTCHA Input */}
                    <input
                      id="captcha"
                      name="captcha"
                      type="text"
                      value={inputCaptcha}
                      onChange={(e) => setInputCaptcha(e.target.value)}
                      required
                      className="block w-full border-2 border-black bg-white px-4 py-2.5 font-mono text-sm font-bold uppercase text-black placeholder:text-gray-500 focus:border-[#064ADA] focus:outline-none"
                      placeholder="Enter the code above"
                    />
                  </div>
                </div>

                {/* Submit Button */}
                <div>
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="relative w-full border-2 border-black bg-[#064ADA] px-4 py-2 font-mono text-sm font-bold uppercase text-white shadow-[3px_3px_0_0_#000] transition-all hover:shadow-[4px_4px_0px_0px_#000] disabled:opacity-50"
                  >
                    {isLoading ? (
                      <span className="flex items-center justify-center">
                        <span className="mr-3 h-4 w-4 border-2 border-white border-t-transparent animate-spin rounded-full"></span>
                        Resetting password...
                      </span>
                    ) : (
                      'Reset Password'
                    )}
                  </button>
                </div>
              </form>
            )}

            {/* Navigation */}
            <p className="mt-6 text-center font-mono text-xs font-bold uppercase text-black">
              Remember your password?{' '}
              <Link
                href="/login"
                className="text-[#064ADA] hover:text-blue-700"
              >
                Sign in
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

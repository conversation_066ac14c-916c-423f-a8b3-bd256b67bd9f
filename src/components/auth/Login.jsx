'use client'

import { useState, useEffect, useCallback } from 'react'
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/20/solid'
import Link from 'next/link'
import { Logo } from '@/components/logo/LogoBolt'
import { LogoText } from '@/components/logo/LogoText'
import { ImageCaptcha } from '@/components/common/ImageCaptcha'
// import { Footer } from '@/components/homepage/Footer'

export function Login() {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [rememberMe, setRememberMe] = useState(false)
  const [email, setEmail] = useState('')

  // CAPTCHA states
  const [captcha, setCaptcha] = useState('')
  const [inputCaptcha, setInputCaptcha] = useState('')

  const generateCaptcha = useCallback(() => {
    // Only use uppercase letters and numbers to avoid case sensitivity issues
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
    let result = ''
    for (let i = 0; i < 6; i++) {
      result += characters.charAt(Math.floor(Math.random() * characters.length))
    }
    setCaptcha(result)
  }, [])

  useEffect(() => {
    const rememberedEmail = localStorage.getItem('rememberedEmail')
    if (rememberedEmail) {
      setEmail(rememberedEmail)
      setRememberMe(true)
    }
    generateCaptcha()
  }, [generateCaptcha])

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword)
  }

  const handleRememberMeChange = (e) => {
    setRememberMe(e.target.checked)
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    // Extract form data
    const formData = new FormData(e.target)
    const email = formData.get('email')
    const password = formData.get('password')

    // CAPTCHA validation
    const trimmedInputCaptcha = inputCaptcha.trim().toUpperCase()
    const trimmedCaptcha = captcha.trim().toUpperCase()

    if (trimmedInputCaptcha !== trimmedCaptcha) {
      setError('CAPTCHA verification failed. Please try again.')
      generateCaptcha()
      setInputCaptcha('')
      setIsLoading(false)
      return
    }

    try {
      const response = await fetch('/api/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: email,
          password: password,
          captcha: trimmedCaptcha,
          inputCaptcha: trimmedInputCaptcha,
        }),
      })

      if (response.ok) {
        const data = await response.json()
        console.log('Login successful:', data)
        if (rememberMe) {
          localStorage.setItem('rememberedEmail', email)
        } else {
          localStorage.removeItem('rememberedEmail')
        }
        window.location.href = '/dashboard'
      } else {
        // Handle error response
        const errorData = await response.json()
        setError(errorData.message || 'Login failed. Please try again.')
        generateCaptcha()
        setInputCaptcha('')
      }
    } catch (err) {
      console.error('Login error:', err)
      setError('Network error. Please check your connection and try again.')
      generateCaptcha()
      setInputCaptcha('')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <>
      <div className="flex min-h-screen flex-col bg-[repeating-conic-gradient(#fff_0deg,#fff_90deg,#f8f8f8_90deg,#f8f8f8_180deg)] [background-size:20px_20px]">
        <div className="flex flex-1 flex-col items-center justify-center px-4 py-12 sm:px-6 lg:px-8">
          <div className="w-full max-w-md">
            {/* Logo */}
            <Link className="mb-2 flex items-center justify-center" href="/">
              <Logo className="h-16" />
            </Link>

            {/* Login Card */}
            <div className="relative border-4 border-black bg-gray-100 p-8 shadow-[8px_8px_0_0_#064ADA]">
              <h2 className="mb-6 text-center font-mono text-xl font-black uppercase text-black">
                Sign in to your account
              </h2>

              {/* Error Message */}
              {error && (
                <div className="mb-4 border-2 border-red-500 bg-red-100 p-3 text-center">
                  <p className="font-mono text-sm font-bold text-red-700">
                    {error}
                  </p>
                </div>
              )}

              <form className="space-y-6" onSubmit={handleSubmit}>
                <div>
                  <label
                    htmlFor="email"
                    className="font-mono text-sm font-bold uppercase text-black"
                  >
                    Email address
                  </label>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    autoComplete="email"
                    required
                    className="mt-2 block w-full border-2 border-black bg-white px-4 py-2.5 font-mono text-sm text-black placeholder:text-gray-500 focus:border-[#064ADA] focus:outline-none"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <div className="flex items-center justify-between">
                    <label
                      htmlFor="password"
                      className="font-mono text-sm font-bold uppercase text-black"
                    >
                      Password
                    </label>
                    <Link
                      href="/forgot-password"
                      className="font-mono text-xs font-bold uppercase text-[#064ADA] transition-colors hover:text-blue-700"
                    >
                      Forgot password?
                    </Link>
                  </div>
                  <div className="relative">
                    <input
                      id="password"
                      name="password"
                      type={showPassword ? 'text' : 'password'}
                      autoComplete="current-password"
                      required
                      className="mt-2 block w-full border-2 border-black bg-white px-4 py-2.5 pr-10 font-mono text-sm text-black placeholder:text-gray-500 focus:border-[#064ADA] focus:outline-none"
                      placeholder="••••••••"
                    />
                    <button
                      type="button"
                      onClick={togglePasswordVisibility}
                      className="absolute inset-y-0 right-0 flex items-center pr-3 focus:outline-none"
                    >
                      {showPassword ? (
                        <EyeSlashIcon
                          className="h-5 w-5 text-gray-500"
                          aria-hidden="true"
                        />
                      ) : (
                        <EyeIcon
                          className="h-5 w-5 text-gray-500"
                          aria-hidden="true"
                        />
                      )}
                    </button>
                  </div>
                </div>

                <div className="flex items-center">
                  <input
                    id="remember-me"
                    name="remember-me"
                    type="checkbox"
                    checked={rememberMe}
                    onChange={handleRememberMeChange}
                    className="h-4 w-4 text-[#064ADA] focus:ring-[#064ADA]"
                  />
                  <label
                    htmlFor="remember-me"
                    className="ml-2 block font-mono text-xs font-bold uppercase text-black"
                  >
                    Remember me
                  </label>
                </div>

                {/* CAPTCHA Section */}
                <div>
                  <label
                    htmlFor="captcha"
                    className="block font-mono text-sm font-bold uppercase text-black"
                  >
                    CAPTCHA
                  </label>
                  <div className="mt-2 space-y-3">
                    {/* CAPTCHA Display */}
                    <ImageCaptcha 
                      captcha={captcha} 
                      onRefresh={() => {
                        generateCaptcha()
                        setInputCaptcha('')
                      }} 
                    />

                    {/* CAPTCHA Input */}
                    <input
                      id="captcha"
                      name="captcha"
                      type="text"
                      value={inputCaptcha}
                      onChange={(e) => setInputCaptcha(e.target.value)}
                      required
                      className="block w-full border-2 border-black bg-white px-4 py-2.5 font-mono text-sm font-bold uppercase text-black placeholder:text-gray-500 focus:border-[#064ADA] focus:outline-none"
                      placeholder="Enter the code above"
                    />
                  </div>
                </div>

                <div>
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="relative w-full border-2 border-black bg-[#064ADA] px-4 py-2 font-mono text-sm font-bold uppercase text-white shadow-[3px_3px_0_0_#000] transition-all hover:shadow-[4px_4px_0px_0px_#000] disabled:opacity-50"
                  >
                    {isLoading ? (
                      <span className="flex items-center justify-center">
                        <span className="mr-3 h-4 w-4 border-2 border-white border-t-transparent animate-spin rounded-full"></span>
                        Signing in...
                      </span>
                    ) : (
                      'Sign in'
                    )}
                  </button>
                </div>
                <div>
                  <Link
                    href="/register"
                    className="relative flex w-full items-center justify-center border-2 border-black bg-white px-4 py-2 font-mono text-sm font-bold uppercase text-black shadow-[3px_3px_0_0_#000] transition-all hover:shadow-[4px_4px_0px_0px_#000]"
                  >
                    Sign Up
                  </Link>
                </div>
              </form>

              <div className="mt-6">
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t-2 border-black"></div>
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="bg-gray-100 px-2 font-mono text-xs font-bold uppercase text-black">
                      Or continue with
                    </span>
                  </div>
                </div>

                <div className="mt-6 grid grid-cols-1 gap-3">
                  {/* <button */}
                  {/*   type="button" */}
                  {/*   className="relative flex w-full items-center justify-center border-2 border-black bg-white px-4 py-2 font-mono text-sm font-bold uppercase text-black shadow-[3px_3px_0_0_#000] transition-all hover:shadow-[4px_4px_0px_0px_#000]" */}
                  {/* > */}
                  {/*   <svg */}
                  {/*     className="mr-2 h-5 w-5" */}
                  {/*     fill="currentColor" */}
                  {/*     viewBox="0 0 24 24" */}
                  {/*   > */}
                  {/*     <path d="M12.0003 2C6.47731 2 2.00031 6.477 2.00031 12C2.00031 16.991 5.65731 21.128 10.4383 21.879V14.89H7.89831V12H10.4383V9.797C10.4383 7.291 11.9323 5.907 14.2153 5.907C15.3103 5.907 16.4543 6.102 16.4543 6.102V8.562H15.1923C13.9503 8.562 13.5623 9.333 13.5623 10.124V12H16.3363L15.8933 14.89H13.5623V21.879C18.3433 21.129 22.0003 16.99 22.0003 12C22.0003 6.477 17.5233 2 12.0003 2Z" /> */}
                  {/*   </svg> */}
                  {/*   Facebook */}
                  {/* </button> */}
                  <button
                    type="button"
                    onClick={() => (window.location.href = '/api/loginGoogle')}
                    className="relative flex w-full items-center justify-center border-2 border-black bg-white px-4 py-2 font-mono text-sm font-bold uppercase text-black shadow-[3px_3px_0_0_#000] transition-all hover:shadow-[4px_4px_0px_0px_#000]"
                  >
                    <svg
                      viewBox="0 0 24 24"
                      fill="currentColor"
                      className="mr-2 h-4 w-4"
                    >
                      <path d="M12.24 10.285V14.4h6.806c-.275 1.765-2.056 5.174-6.806 5.174-4.095 0-7.439-3.389-7.439-7.574s3.345-7.574 7.439-7.574c2.33 0 3.891.989 4.785 1.849l3.254-3.138C18.189 1.186 15.479 0 12.24 0c-6.635 0-12 5.365-12 12s5.365 12 12 12c6.926 0 11.52-4.869 11.52-11.726 0-.788-.085-1.39-.189-1.989H12.24z" />
                    </svg>
                    Google
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* <Footer /> */}
    </>
  )
}

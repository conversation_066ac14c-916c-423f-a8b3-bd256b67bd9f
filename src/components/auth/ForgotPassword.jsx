'use client'

import { useState, useEffect, useCallback } from 'react'
import Link from 'next/link'
import { Logo } from '@/components/logo/LogoBolt'
import { LogoText } from '@/components/logo/LogoText'
import { ImageCaptcha } from '@/components/common/ImageCaptcha'

export function ForgotPassword() {
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [error, setError] = useState('')
  const [email, setEmail] = useState('')

  // CAPTCHA states
  const [captcha, setCaptcha] = useState('')
  const [inputCaptcha, setInputCaptcha] = useState('')

  const generateCaptcha = useCallback(() => {
    // Only use uppercase letters and numbers to avoid case sensitivity issues
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
    let result = ''
    for (let i = 0; i < 6; i++) {
      result += characters.charAt(Math.floor(Math.random() * characters.length))
    }
    setCaptcha(result)
  }, [])

  useEffect(() => {
    generateCaptcha()
  }, [generateCaptcha])

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')
    setMessage('')

    const formData = new FormData(e.target)
    const emailValue = formData.get('email')

    // CAPTCHA validation with debugging
    const trimmedInputCaptcha = inputCaptcha.trim().toUpperCase()
    const trimmedCaptcha = captcha.trim().toUpperCase()

    console.log('CAPTCHA Debug:')
    console.log('Generated CAPTCHA:', `"${trimmedCaptcha}"`)
    console.log('User Input:', `"${trimmedInputCaptcha}"`)
    console.log('Are they equal?', trimmedCaptcha === trimmedInputCaptcha)

    if (trimmedInputCaptcha !== trimmedCaptcha) {
      setError('CAPTCHA verification failed. Please try again.')
      generateCaptcha()
      setInputCaptcha('')
      setIsLoading(false)
      return
    }

    try {
      const response = await fetch('/api/portal/forgotpassword', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: emailValue,
          captcha: trimmedCaptcha,
          inputCaptcha: trimmedInputCaptcha,
        }),
      })

      const data = await response.json()

      if (response.ok) {
        setMessage('Password reset link has been sent to your email!')
        setEmail('')
        setInputCaptcha('')
        generateCaptcha()
        e.target.reset()
      } else {
        setError(
          data.message || 'Failed to send reset email. Please try again.',
        )
        generateCaptcha()
        setInputCaptcha('')
      }
    } catch (error) {
      console.error('Forgot password error:', error)
      setError('Network error. Please check your connection and try again.')
      generateCaptcha()
      setInputCaptcha('')
    }

    setIsLoading(false)
  }

  return (
    <>
      <div className="flex min-h-screen flex-col bg-[repeating-conic-gradient(#fff_0deg,#fff_90deg,#f8f8f8_90deg,#f8f8f8_180deg)] [background-size:20px_20px]">
        <div className="flex flex-1 flex-col items-center justify-center px-4 py-12 sm:px-6 lg:px-8">
          <div className="w-full max-w-md">
            {/* Logo */}
            <Link className="mb-2 flex items-center justify-center" href="/">
              <Logo className="h-16" />
            </Link>

            {/* Forgot Password Card */}
            <div className="relative border-4 border-black bg-gray-100 p-8 shadow-[8px_8px_0_0_#064ADA]">
              <h2 className="mb-6 text-center font-mono text-xl font-black uppercase text-black">
                Forgot your password?
              </h2>

              {/* Error Message */}
              {error && (
                <div className="mb-4 border-2 border-red-500 bg-red-100 p-3 shadow-[3px_3px_0_0_#000]">
                  <p className="text-center font-mono text-sm font-bold text-red-700">
                    {error}
                  </p>
                </div>
              )}

              {/* Success Message */}
              {message && (
                <div className="mb-4 border-2 border-green-500 bg-green-100 p-3 shadow-[3px_3px_0_0_#000]">
                  <p className="text-center font-mono text-sm font-bold text-green-700">
                    {message}
                  </p>
                </div>
              )}

              <form className="space-y-6" onSubmit={handleSubmit}>
                <div>
                  <label
                    htmlFor="email"
                    className="font-mono text-sm font-bold uppercase text-black"
                  >
                    Email address
                  </label>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required
                    className="mt-2 block w-full border-2 border-black bg-white px-4 py-2.5 font-mono text-sm text-black placeholder:text-gray-500 focus:border-[#064ADA] focus:outline-none"
                    placeholder="<EMAIL>"
                  />
                </div>

                {/* CAPTCHA Section */}
                <div>
                  <label
                    htmlFor="captcha"
                    className="block font-mono text-sm font-bold uppercase text-black"
                  >
                    CAPTCHA
                  </label>
                  <div className="mt-2 space-y-3">
                    {/* CAPTCHA Display */}
                    <ImageCaptcha 
                      captcha={captcha} 
                      onRefresh={() => {
                        generateCaptcha()
                        setInputCaptcha('')
                      }} 
                    />

                    {/* CAPTCHA Input */}
                    <input
                      id="captcha"
                      name="captcha"
                      type="text"
                      value={inputCaptcha}
                      onChange={(e) => setInputCaptcha(e.target.value)}
                      required
                      className="block w-full border-2 border-black bg-white px-4 py-2.5 font-mono text-sm font-bold uppercase text-black placeholder:text-gray-500 focus:border-[#064ADA] focus:outline-none"
                      placeholder="Enter the code above"
                    />
                  </div>
                </div>

                <div>
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="relative w-full border-2 border-black bg-[#064ADA] px-4 py-2 font-mono text-sm font-bold uppercase text-white shadow-[3px_3px_0_0_#000] transition-all hover:shadow-[4px_4px_0px_0px_#000] disabled:opacity-50"
                  >
                    {isLoading ? (
                      <span className="flex items-center justify-center">
                        <span className="mr-3 h-4 w-4 border-2 border-white border-t-transparent animate-spin rounded-full"></span>
                        Sending...
                      </span>
                    ) : (
                      'Send reset link'
                    )}
                  </button>
                </div>
              </form>

              <div className="mt-6 text-center">
                <Link
                  href="/login"
                  className="font-mono text-sm font-bold uppercase text-[#064ADA] transition-colors hover:text-blue-700"
                >
                  Back to login
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

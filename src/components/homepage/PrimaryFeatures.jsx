'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { DemoProject } from '@/components/demo/DemoProject'
import { DemoKeyword } from '@/components/demo/DemoKeyword'
import { DemoAttachment } from '@/components/demo/DemoAttachment'
import { DemoTemplate } from '@/components/demo/DemoTemplate'

const features = [
  {
    title: 'Projects',
    tagline: 'Organize & Automate',
    description:
      'Manage your content projects efficiently by organizing keywords in one place.',
    demo: <DemoProject />,
    icon: 'pi pi-folder',
  },
  {
    title: 'Keywords',
    tagline: 'Generate & Post',
    description: 'Centralize your target keywords for content generation.',
    demo: <DemoKeyword />,
    icon: 'pi pi-key',
  },
  {
    title: 'Images',
    tagline: 'Grab & Upload',
    description:
      'Control how images are sourced, processed, and integrated into your posts.',
    demo: <DemoAttachment />,
    icon: 'pi pi-image',
  },
  {
    title: 'Templates',
    tagline: 'Structure & Scale',
    description: 'A flexible template management system that guides the AI.',
    demo: <DemoTemplate />,
    icon: 'pi pi-file',
  },
]

export function PrimaryFeatures() {
  const [activeTab, setActiveTab] = useState(0)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  return (
    <section className="relative overflow-hidden bg-[repeating-conic-gradient(#fff_0deg,#fff_90deg,#f8f8f8_90deg,#f8f8f8_180deg)] [background-size:20px_20px] py-24 lg:py-32">
      <div className="relative mx-auto max-w-7xl px-6">
        <div className="mx-auto max-w-2xl text-center">
          <div className="inline-flex items-center gap-2 border-4 border-black bg-[#1E1E1E] px-4 py-2 font-mono text-sm font-bold uppercase text-white shadow-[4px_4px_0_0_#000]">
            Powerful Features
          </div>

          <h2 className="mt-8 font-mono text-4xl font-black uppercase text-black sm:text-5xl">
            The <span className="text-[#064ADA]">Superhuman</span> Way to
            Automate Content
          </h2>

          <p className="mt-4 font-mono text-lg text-black">
            With great content power comes zero responsibility.
          </p>
        </div>

        <div className="mt-16 lg:mt-20">
          <div className="grid gap-8 lg:grid-cols-4">
            {features.map((feature, index) => (
              <button
                key={feature.title}
                onClick={() => setActiveTab(index)}
                className={`group relative border-4 border-black p-6 transition-all ${
                  activeTab === index
                    ? 'bg-[#064ADA] text-white shadow-[8px_8px_0_0_#000]'
                    : 'bg-white text-black shadow-[4px_4px_0_0_#000] hover:shadow-[8px_8px_0_0_#000]'
                }`}
              >
                <div className="relative z-10 space-y-4">
                  <div className="flex items-center gap-3">
                    <div
                      className={`flex h-12 w-12 items-center justify-center border-4 border-black ${
                        activeTab === index ? 'bg-blue-200' : 'bg-white'
                      }`}
                    >
                      <i
                        className={`${feature.icon} text-2xl ${
                          activeTab === index
                            ? 'text-black'
                            : 'text-[#064ADA]'
                        }`}
                      />
                    </div>
                    <div className="text-left">
                      <h3
                        className={`font-mono text-lg font-black uppercase ${
                          activeTab === index ? 'text-white' : 'text-black'
                        }`}
                      >
                        {feature.title}
                      </h3>
                      <p
                        className={`font-mono text-sm font-bold uppercase ${
                          activeTab === index
                            ? 'text-blue-200'
                            : 'text-[#064ADA]'
                        }`}
                      >
                        {feature.tagline}
                      </p>
                    </div>
                  </div>

                  <p
                    className={`font-mono text-sm font-bold uppercase ${
                      activeTab === index ? 'text-white' : 'text-black'
                    }`}
                  >
                    {feature.description}
                  </p>
                </div>
              </button>
            ))}
          </div>

          <div className="mt-8 border-4 border-black bg-[repeating-linear-gradient(90deg,rgba(0,0,0,0.02)_0,rgba(0,0,0,0.02)_1px,transparent_1px,transparent_5px)] [background-size:5px_5px] p-2 shadow-[8px_8px_0_0_#000]">
            <AnimatePresence mode="wait">
              <motion.div
                key={activeTab}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.2 }}
              >
                {features[activeTab].demo}
              </motion.div>
            </AnimatePresence>
          </div>
        </div>
      </div>
    </section>
  )
}

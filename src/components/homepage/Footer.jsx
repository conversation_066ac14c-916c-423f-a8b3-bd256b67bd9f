import Link from 'next/link'
import { Container } from '@/components/common/Container'
import { Logo } from '@/components/logo/LogoBolt'
import { LogoText } from '@/components/logo/LogoText'

const navigation = {
  product: [
    { name: 'Features', href: '#features', icon: 'pi pi-bolt' },
    { name: 'Pricing', href: '#pricing', icon: 'pi pi-tag' },
    { name: 'Showcase', href: '#showcase', icon: 'pi pi-images' },
    { name: 'Roadmap', href: '/roadmap', icon: 'pi pi-map' },
  ],
  resources: [
    { name: 'Documentation', href: '#', icon: 'pi pi-book' },
    { name: 'API Reference', href: '#', icon: 'pi pi-code' },
    { name: 'System Status', href: '#', icon: 'pi pi-server' },
    { name: 'Release Notes', href: '#', icon: 'pi pi-list' },
  ],
  company: [
    { name: 'About', href: '#', icon: 'pi pi-users' },
    { name: 'Blog', href: '#', icon: 'pi pi-pencil' },
    { name: 'Careers', href: '#', icon: 'pi pi-briefcase' },
    { name: 'Contact', href: '#', icon: 'pi pi-envelope' },
  ],
}

export function Footer() {
  return (
    <footer className="relative border-t-4 border-black bg-black py-20 text-white">
      <Container>
        <div className="relative">
          <div className="mb-16 grid gap-16 border-b-4 border-gray-800 pb-16 lg:grid-cols-2 items-center">
            {/* Brand Section */}
            <div className="space-y-8">
              {/* <Link href="/" className="group inline-flex items-center gap-4"> */}
              {/*   <Logo className="h-12 text-[#1E1E1E]" /> */}
              {/* </Link> */}

              <p className="max-w-md font-mono text-gray-300">
                Experience the next evolution of WordPress content creation with
                <span className="text-blue-200">
                  {' '}
                  AI-powered automation
                </span>{' '}
                and
                <span className="text-blue-200">
                  {' '}
                  intelligent optimization
                </span>
                .
              </p>

              {/* Social Links */}
              <div className="flex gap-4">
                {[
                  { name: 'Twitter', icon: 'pi pi-twitter' },
                  { name: 'GitHub', icon: 'pi pi-github' },
                  { name: 'Discord', icon: 'pi pi-discord' },
                ].map((social) => (
                  <a
                    key={social.name}
                    href="#"
                    className="flex h-12 w-12 items-center justify-center border-2 border-gray-700 bg-gray-900 text-gray-300 transition-all hover:text-blue-200"
                  >
                    <i className={`${social.icon} text-xl`} />
                  </a>
                ))}
              </div>
            </div>

            {/* Newsletter Section */}
            <div className="relative border-4 border-black bg-white p-8 text-black shadow-[8px_8px_0_0_#4A5568]">
              <div className="relative">
                <h3 className="font-mono text-xl font-black uppercase text-black">
                  Join the Evolution
                </h3>
                <p className="mt-2 font-mono text-sm text-gray-700">
                  Get weekly insights on AI content creation and WordPress
                  automation.
                </p>

                <form className="mt-6">
                  <div className="flex flex-col gap-3 sm:flex-row">
                    <div className="w-full flex-1">
                      <input
                        type="email"
                        placeholder="<EMAIL>"
                        className="w-full border-2 border-black bg-white px-4 py-2.5 font-mono text-sm text-black placeholder:text-gray-500 focus:border-[#064ADA] focus:outline-none"
                      />
                    </div>
                    <div className="flex-none">
                      <button
                        type="submit"
                        className="w-full border-2 border-black bg-[#064ADA] px-5 py-2.5 font-mono text-sm font-bold uppercase text-white transition-all hover:text-blue-100"
                      >
                        Subscribe
                      </button>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>

          {/* Navigation Grid */}
          <div className="grid gap-8 sm:grid-cols-2 lg:grid-cols-3">
            {Object.entries(navigation).map(([category, items]) => (
              <div key={category} className="space-y-6">
                <h3 className="font-mono text-sm font-bold uppercase tracking-wider text-blue-200">
                  {category}
                </h3>
                <ul className="space-y-4">
                  {items.map((item) => (
                    <li key={item.name}>
                      <Link
                        href={item.href}
                        className="group flex items-center gap-3 font-mono text-sm text-gray-300 transition-colors hover:text-blue-200"
                      >
                        <span className="flex h-6 w-6 items-center justify-center rounded-sm border-2 border-gray-700 bg-gray-900">
                          <i className={`${item.icon} text-xs`} />
                        </span>
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>

          {/* Bottom Bar */}
          <div className="mt-16 flex flex-col items-center justify-between gap-6 border-t-2 border-gray-800 pt-8 text-sm text-gray-500 sm:flex-row">
            <div className="flex items-center">
              <Logo className="h-12" />
              <span className="font-mono">
                {''} © {new Date().getFullYear()}{''}
                . All rights reserved.
              </span>
            </div>
            <div className="flex gap-6 font-mono">
              {[
                'Privacy Policy',
                'Terms of Service',
                'License Agreement',
              ].map((item) => (
                <Link
                  key={item}
                  href="#"
                  className="transition-colors hover:text-blue-200"
                >
                  {item}
                </Link>
              ))}
            </div>
          </div>
        </div>
      </Container>
    </footer>
  )
}


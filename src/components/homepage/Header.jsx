'use client'

import Link from 'next/link'
import { useState, useEffect } from 'react'
import { Logo } from '@/components/logo/LogoBolt'
import { LogoText } from '@/components/logo/LogoText'

function MobileMenu({ isOpen, onClose }) {
  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50">
      <div
        className="absolute inset-0 bg-white/90 backdrop-blur-md"
        onClick={onClose}
      />
      <div className="absolute right-4 top-4 w-64">
        <div className="relative border-4 border-black bg-white shadow-[8px_8px_0_0_#064ADA]">
          <div className="flex h-10 items-center justify-between border-b-4 border-black bg-gray-100 px-4">
            <span className="font-mono text-sm font-bold uppercase text-black">
              Navigation
            </span>
            <button
              onClick={onClose}
              className="text-black transition-colors hover:text-[#064ADA]"
            >
              <i className="pi pi-times text-lg" />
            </button>
          </div>

          <nav className="space-y-2 p-4">
            {['Features', 'Pricing', 'Sign in'].map((item) => (
              <Link
                key={item}
                href={`/${item.toLowerCase().replace(' ', '')}`}
                className="block border-2 border-black bg-gray-300 px-4 py-2 font-mono text-sm font-bold uppercase text-black transition-all hover:bg-[#064ADA] hover:text-white"
                onClick={onClose}
              >
                {item}
              </Link>
            ))}
          </nav>
        </div>
      </div>
    </div>
  )
}

export function Header() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [scrolled, setScrolled] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 50)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  return (
    <header className="fixed inset-x-0 top-0 z-40 border-b-4 border-black bg-black">
      <div
        className={`absolute inset-0 border-b-4 border-black transition-all duration-300 ${
          scrolled ? 'bg-black' : 'bg-black/80 backdrop-blur-md'
        }`}
      />

      <div className="relative mx-auto max-w-7xl px-6">
        <div className="flex h-20 items-center justify-between">
          <Link href="/" className="group flex items-center">
            <Logo className="h-12" />
            <LogoText className="h-6 w-auto text-white" />
          </Link>

          <div className="hidden items-center gap-8 md:flex">
            <nav className="flex items-center gap-6">
              {['Features', 'Pricing'].map((item) => (
                <Link
                  key={item}
                  href={`/${item.toLowerCase()}`}
                  className="font-mono text-sm font-bold uppercase text-white transition-all hover:text-blue-200"
                >
                  {item}
                </Link>
              ))}
            </nav>

            <div className="h-6 w-1 bg-white" />

            <div className="flex items-center gap-6">
              <Link
                href="/login"
                className="font-mono text-sm font-bold uppercase text-white transition-all hover:text-blue-200"
              >
                Login
              </Link>
              <Link
                href="/register"
                className="relative border-2 border-white bg-blue-700 px-4 py-2 font-mono text-sm font-bold uppercase text-white transition-all shadow-[3px_3px_0_0_#FFF] hover:shadow-[4px_4px_0px_0px_#FFF]"
              >
                Download
              </Link>
            </div>
          </div>

          <button
            className="relative border-2 border-white bg-blue-700 px-4 py-2 font-mono text-sm font-bold uppercase text-white transition-all shadow-[3px_3px_0_0_#FFF] hover:shadow-[4px_4px_0px_0px_#FFF] md:hidden"
            onClick={() => setIsMobileMenuOpen(true)}
          >
            Menu
          </button>
        </div>
      </div>

      <MobileMenu
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
      />
    </header>
  )
}

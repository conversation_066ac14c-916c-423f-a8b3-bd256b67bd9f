import { Container } from '@/components/common/Container'

export function CallToAction() {
  return (
    <section className="relative overflow-hidden bg-[#064ADA] py-24 lg:py-32">
      <Container className="relative">
        <div className="mx-auto max-w-3xl">
          <div className="relative border-4 border-black bg-white p-8 shadow-[8px_8px_0_0_#000]">
            <div className="px-8 pt-12 text-center">
              <div className="inline-flex items-center gap-2 border-4 border-black bg-[#1E1E1E] px-3 py-1 font-mono text-sm font-bold uppercase text-white shadow-[4px_4px_0_0_#000]">
                WordPress Automation
              </div>

              <h2 className="mt-6 font-mono text-4xl font-black uppercase text-[#064ADA] sm:text-5xl">
                Supercharge Your Content
              </h2>

              <p className="mt-4 font-mono text-lg text-black">
                Generate, optimize, and publish content at{' '}
                <span className="text-black">superhuman speed</span>
              </p>
            </div>

            <div className="mt-8 grid grid-cols-3 gap-0.5 border-y-4 border-black bg-black p-0.5">
              {[
                ['AI-Powered', 'Intelligent content generation'],
                ['Lightning Fast', '10x faster workflow'],
                ['SEO Ready', 'Optimized for search'],
              ].map(([title, desc]) => (
                <div key={title} className="bg-white p-4 text-center">
                  <div className="font-mono text-sm font-bold uppercase text-[#064ADA]">
                    {title}
                  </div>
                  <div className="mt-1 text-xs font-bold uppercase text-black">
                    {desc}
                  </div>
                </div>
              ))}
            </div>

            <div className="px-8 py-12 text-center">
              <div className="flex flex-col items-center gap-4 sm:flex-row sm:justify-center">
                <a
                  href="/register"
                  className="relative w-full border-4 border-black bg-blue-700 px-8 py-3 font-mono text-sm font-bold uppercase text-white transition-all shadow-[4px_4px_0_0_#000] hover:shadow-[6px_6px_0px_0px_#000] sm:w-auto"
                >
                  Download for Free
                </a>

                <a
                  href="#demo"
                  className="relative w-full border-4 border-black bg-[#1E1E1E] px-8 py-3 font-mono text-sm font-bold uppercase text-white transition-all shadow-[4px_4px_0_0_#000] hover:shadow-[6px_6px_0px_0px_#000] sm:w-auto"
                >
                  Watch Demo
                </a>
              </div>
            </div>
          </div>

          <div className="mt-8 text-center">
            <p className="font-mono text-sm font-bold uppercase text-white">
              Trusted by 10,000+ content creators worldwide
            </p>
          </div>
        </div>
      </Container>
    </section>
  )
}

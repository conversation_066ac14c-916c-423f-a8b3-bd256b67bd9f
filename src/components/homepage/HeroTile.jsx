'use client'

import { Logo } from '@/components/logo/LogoBolt'
import { DemoPosting } from '@/components/demo/DemoPostingAnimation'

export function HeroTile() {
  return (
    <div className="relative overflow-hidden bg-[#064ADA] bg-[radial-gradient(rgba(0,0,0,0.3)_1px,transparent_1px)] [background-size:16px_16px] py-20 sm:py-5">
      <div className="relative mx-auto max-w-7xl px-6">
        <div className="flex min-h-[90vh] flex-col items-center justify-center gap-1 py-20 lg:flex-row lg:justify-between lg:py-32">
          <div className="relative z-10 max-w-4xl pt-10 lg:max-w-md">
            <div className="inline-flex items-center space-x-2 border-4 border-black bg-[#1E1E1E] px-3 py-1 font-mono text-sm font-bold uppercase text-white shadow-[4px_4px_0_0_#000]">
              <span>WordPress Plugin</span>
            </div>

            <h1 className="mt-6 font-mono text-[clamp(2.5rem,5vw,4rem)] font-black leading-tight text-white">
              WordPress <span className="text-white" >Superpowers</span>
            </h1>

            <p className="mt-8 font-mono text-lg leading-relaxed text-white">
              <span className="font-bold">
                Advanced content automation meets simplicity. Discover what your
                WordPress site can achieve with next-generation content
                publishing.
              </span>
            </p>

            <div className="mt-10 flex items-center gap-6">
              <a
                href="#"
                className="relative border-4 border-black bg-blue-200 px-8 py-3 font-mono text-sm font-bold uppercase text-black transition-all shadow-[4px_4px_0_0_#000] hover:shadow-[6px_6px_0px_0px_#000]"
              >
                Get started
              </a>
              <a
                href="#"
                className="relative border-4 border-black bg-[#1E1E1E] px-8 py-3 font-mono text-sm font-bold uppercase text-white transition-all hover:text-white shadow-[4px_4px_0_0_#000] hover:shadow-[6px_6px_0px_0px_#000]"
              >
                learn more
              </a>
            </div>

            {/* <div className="mt-16 flex gap-12 border-t-4 border-black pt-8"> */}
            {/*   {[ */}
            {/*     ['10K+', 'Active users'], */}
            {/*     ['500K+', 'Posts generated'], */}
            {/*   ].map(([stat, label]) => ( */}
            {/*     <div key={stat} className="font-mono"> */}
            {/*       <div className="text-2xl font-black text-black"> */}
            {/*         {stat} */}
            {/*       </div> */}
            {/*       <div className="mt-1 text-sm font-bold uppercase text-white"> */}
            {/*         {label} */}
            {/*       </div> */}
            {/*     </div> */}
            {/*   ))} */}
            {/* </div> */}
          </div>

          <div className="relative mt-6 w-full ps-0 lg:mt-0 lg:ps-6">
            <div className="relative border-4 border-black bg-white p-2 shadow-[8px_8px_0_0_#000]">
              <DemoPosting />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

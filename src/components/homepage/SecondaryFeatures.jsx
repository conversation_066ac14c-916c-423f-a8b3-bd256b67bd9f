'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { DemoPosting } from '@/components/demo/DemoPostingAnimation'
import { DemoGenerateKeyword } from '@/components/demo/DemoGenerateKeyword'
import { DemoUploadImages } from '@/components/demo/DemoUploadImages'
import { Container } from '@/components/common/Container'

const features = [
  {
    name: 'Generate Keywords',
    summary:
      'Generate targeted keywords instantly for SEO and content optimization.',
    description:
      'Streamline your keyword research process with our powerful keyword generator. Simply enter a seed keyword, select your preferred search service, and set the depth level to discover relevant keyword variations and long-tail phrases.',
    demo: <DemoGenerateKeyword />,
    icon: 'pi pi-chart-line',
  },
  {
    name: 'Generate Content',
    summary:
      'Create optimized blog posts and articles with automated content generation.',
    description:
      'Transform your keywords into fully-formed content with our intelligent post generator. The system automatically creates engaging titles, generates comprehensive articles, and handles image uploads - all while maintaining SEO best practices.',
    demo: <DemoPosting />,
    icon: 'pi pi-file-edit',
  },
  {
    name: 'Upload Images',
    summary: 'Effortlessly upload and manage multiple images for your content.',
    description:
      'Upload multiple high-quality images simultaneously, preview them in a clean gallery layout, and select the perfect visuals for your posts. Supports batch uploading, image selection, and seamless integration with your content creation process.',
    demo: <DemoUploadImages />,
    icon: 'pi pi-images',
  },
]

// Static Feature Component
function StaticFeature({ feature, isActive }) {
  return (
    <div className="relative space-y-6">
      <div className="inline-flex h-12 w-12 items-center justify-center border-4 border-black bg-[#1E1E1E] shadow-[4px_4px_0_0_#000]">
        <i className={`${feature.icon} text-xl text-white`} />
      </div>
      <div className="space-y-2">
        <div className="font-mono text-xs text-black">
          { '> feature.description'}
        </div>
        <h3 className="font-mono text-xl text-black">{feature.summary}</h3>
      </div>
      <p className="font-mono text-sm leading-relaxed text-black">
        {feature.description}
      </p>
      <button className="group flex items-center gap-2 font-mono text-sm text-[#064ADA] hover:text-black">
        <span className="h-1 w-1 rounded-full bg-[#064ADA]" />
        learn.more()
        <span className="transition-transform group-hover:translate-x-0.5">
          →
        </span>
      </button>
    </div>
  )
}

export function SecondaryFeatures() {
  const [activeTab, setActiveTab] = useState(0)
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
  }, [])

  // Static render (server-side and initial client-side)
  if (!isMounted) {
    return (
      <section className="relative overflow-hidden bg-black py-24 sm:py-32">
        <Container>
          <div className="relative mx-auto max-w-2xl text-center">
            <div className="inline-flex items-center gap-2 border-4 border-black bg-[#1E1E1E] px-3 py-1 font-mono text-sm font-bold uppercase text-white shadow-[4px_4px_0_0_#000]">
              WordPress Superpowers
            </div>
            <h2 className="mt-8 font-mono text-3xl font-black uppercase text-white">
              <span className="text-[#1E1E1E]">Power</span> Meets Simplicity
            </h2>
            <p className="mt-4 font-mono text-lg text-white">
              Let AI-powered automation handle the complexity, while you enjoy the
              simplicity of WordPress publishing.
            </p>
          </div>

          <div className="mt-16 lg:mt-20">
            <div className="flex justify-center">
              <div className="inline-flex border-4 border-black bg-white p-1 shadow-[4px_4px_0_0_#000]">
                {features.map((feature, index) => (
                  <button
                    key={feature.name}
                    className={`relative border-2 border-black px-6 py-2.5 font-mono text-sm font-bold uppercase transition-all ${
                      activeTab === index
                        ? 'bg-[#064ADA] text-white'
                        : 'text-white hover:bg-[#1E1E1E]'
                    }`}
                  >
                    <span className="relative flex items-center gap-2">
                      <i className={feature.icon} />
                      {feature.name}
                    </span>
                  </button>
                ))}
              </div>
            </div>

            <div className="relative mt-16">
              <div className="grid gap-8 lg:grid-cols-2">
                <StaticFeature feature={features[0]} isActive={true} />
                <div className="relative overflow-hidden border-4 border-black bg-white p-2 shadow-[8px_8px_0_0_#000]">
                  <div className="relative">{features[0].demo}</div>
                </div>
              </div>
            </div>
          </div>
        </Container>
      </section>
    )
  }

  // Client-side render with animations
  return (
    <section className="relative overflow-hidden bg-black py-24 sm:py-32">
      <Container>
        <div className="relative mx-auto max-w-2xl text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="inline-flex items-center gap-2 border-4 border-black bg-[#1E1E1E] px-3 py-1 font-mono text-sm font-bold uppercase text-white shadow-[4px_4px_0_0_#000]"
          >
            WordPress Superpowers
          </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="mt-8 font-mono text-4xl font-black uppercase text-white sm:text-5xl"
          >
            <span className="text-blue-600">Power</span> Meets Simplicity
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="mt-4 font-mono text-lg text-white"
          >
            Let AI-powered automation handle the complexity, while you enjoy the
            simplicity of WordPress publishing.
          </motion.p>
        </div>

        <div className="mt-16 lg:mt-20">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex justify-center"
          >
            <div className="inline-flex border-4 border-black bg-white p-1 shadow-[4px_4px_0_0_#000]">
              {features.map((feature, index) => (
                <button
                  key={feature.name}
                  onClick={() => setActiveTab(index)}
                  className={`relative border-0 border-black px-6 py-2.5 font-mono text-sm font-bold uppercase transition-all ${
                    activeTab === index
                      ? 'bg-[#064ADA] text-white'
                      : 'text-black hover:bg-blue-200'
                  }`}
                >
                  <span className="relative flex items-center gap-2">
                    <i className={feature.icon} />
                    <span className="hidden sm:block">{feature.name}</span>
                  </span>
                </button>
              ))}
            </div>
          </motion.div>

          <div className="relative mt-16">
            <AnimatePresence mode="wait">
              {features.map(
                (feature, index) =>
                  activeTab === index && (
                    <motion.div
                      key={feature.name}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.3 }}
                      className="grid items-center gap-8 lg:grid-cols-2"
                    >
                      <AnimatedFeature feature={feature} />
                      <div className="relative h-max overflow-hidden border-4 border-black bg-white p-2 shadow-[8px_8px_0_0_#000]">
                        <div className="relative">{feature.demo}</div>
                      </div>
                    </motion.div>
                  ),
              )}
            </AnimatePresence>
          </div>
        </div>
      </Container>
    </section>
  )
}

function AnimatedFeature({ feature }) {
  return (
    <div className="relative space-y-6 text-center lg:text-left">
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className="inline-flex h-12 w-12 items-center justify-center border-4 border-black bg-blue-200 shadow-[4px_4px_0_0_#000]"
      >
        <i className={`${feature.icon} text-xl text-black`} />
      </motion.div>
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="space-y-2"
      >
        <h3 className="font-mono text-xl font-black uppercase text-white">
          {feature.summary}
        </h3>
      </motion.div>
      <motion.p
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="font-mono text-sm leading-relaxed text-white"
      >
        {feature.description}
      </motion.p>
    </div>
  )
}

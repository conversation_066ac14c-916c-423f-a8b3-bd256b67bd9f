'use client'

export function HeroTailwind() {
  return (
    <div className="relative -mt-[5.75rem] overflow-hidden pb-16 pt-[5.75rem]">
      <img
        src="/plus/img/<EMAIL>"
        alt=""
        className="absolute -top-[1rem] left-1/2 -ml-[40rem] w-[163.125rem] max-w-none sm:-ml-[67.5rem]"
      />
      <div className="max-w-container relative mx-auto mt-16 grid w-full grid-cols-1 px-4 sm:mt-20 sm:px-6 lg:px-8 xl:mt-32">
        <div
          className="col-start-1 row-start-1 h-7 text-base/7 font-semibold text-sky-500"
          aria-hidden="true"
        >
          By the makers of Tailwind CSS
        </div>
        <h1 className="col-start-1 row-start-2 mt-4 max-w-[36rem] text-4xl font-extrabold tracking-tight text-slate-900 sm:text-7xl xl:max-w-[43.5rem]">
          Build your next idea even faster.
        </h1>
        <p className="col-start-1 row-start-3 mt-4 max-w-lg text-lg text-slate-700">
          Beautifully designed, expertly crafted components and templates, built
          by the makers of Tailwind CSS. The perfect starting point for your
          next project.
        </p>
        <div className="col-start-1 row-start-4 mt-10 flex flex-col space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0">
          <a
            className="inline-flex justify-center rounded-lg bg-slate-900 px-4 py-3 text-sm font-semibold text-white hover:bg-slate-700"
            href="/components"
          >
            <span>
              Browse components
              <span
                aria-hidden="true"
                className="hidden text-slate-400 sm:inline"
              >
                →
              </span>
            </span>
          </a>
          <a
            className="inline-flex justify-center rounded-lg px-4 py-3 text-sm font-semibold text-slate-900 ring-1 ring-slate-900/10 hover:bg-white/25 hover:ring-slate-900/15"
            href="/templates"
          >
            <span>
              Explore templates
              <span
                aria-hidden="true"
                className="hidden text-black/25 sm:inline"
              >
                →
              </span>
            </span>
          </a>
        </div>

        <div className="pointer-events-none col-start-1 row-start-5 flex md:row-span-3 md:row-start-3 lg:row-span-4 lg:row-start-2 xl:row-span-5 xl:row-start-1 xl:justify-end">
          <div className="sm:scale-none -ml-[32rem] mt-12 h-[46.375rem] origin-top scale-[calc(204/299)] select-none sm:-ml-[24rem] sm:-mt-20 sm:h-auto md:-ml-64 md:mt-10 lg:-ml-16 lg:mt-0 xl:-mr-4 xl:ml-0">
            <div className="flex justify-end">
              <div className="relative flex items-end">
                <div className="absolute -inset-x-8 bottom-0 h-px bg-slate-900/15 [mask-image:linear-gradient(to_right,transparent,white_4rem,white_calc(100%-4rem),transparent)]"></div>
                <div className="absolute left-16 top-full -mt-px h-8 overflow-hidden">
                  <div className="-mt-px flex h-[2px] w-56">
                    <div className="blur-xs w-full flex-none [background-image:linear-gradient(90deg,rgba(56,189,248,0)_0%,#0EA5E9_32.29%,rgba(236,72,153,0.3)_67.19%,rgba(236,72,153,0)_100%)]"></div>
                    <div className="-ml-[100%] w-full flex-none blur-[1px] [background-image:linear-gradient(90deg,rgba(56,189,248,0)_0%,#0EA5E9_32.29%,rgba(236,72,153,0.3)_67.19%,rgba(236,72,153,0)_100%)]"></div>
                  </div>
                </div>

                {/* Toggle Switches */}
                <div>
                  <div className="p-4">
                    <div className="pointer-events-auto ml-auto h-6 w-10 rounded-full bg-indigo-600 p-1 ring-1 ring-inset ring-black/20 transition duration-200 ease-in-out">
                      <div className="shadow-xs size-4 translate-x-4 rounded-full bg-white ring-1 ring-slate-700/10 transition duration-200 ease-in-out"></div>
                    </div>
                    <div className="mt-8 flex items-center justify-end">
                      <div className="pointer-events-auto h-6 w-10 rounded-full bg-slate-900/10 p-1 ring-1 ring-inset ring-slate-900/5 transition duration-200 ease-in-out">
                        <div className="shadow-xs size-4 rounded-full bg-white ring-1 ring-slate-700/10 transition duration-200 ease-in-out"></div>
                      </div>
                      <div className="pointer-events-auto ml-8 rounded-md bg-indigo-600 px-3 py-2 text-[0.8125rem]/5 font-semibold text-white hover:bg-indigo-500">
                        Button A
                      </div>
                    </div>
                  </div>

                  {/* Bookmark Section */}
                  <div className="relative z-10 p-4 text-right">
                    <div className="absolute -inset-y-8 left-0 w-px bg-slate-900/15 [mask-image:linear-gradient(to_top,transparent,white_4rem,white_calc(100%-4rem),transparent)]"></div>
                    <div className="absolute -inset-x-8 top-0 h-px bg-slate-900/15 [mask-image:linear-gradient(to_left,transparent,white_4rem,white_calc(100%-4rem),transparent)]"></div>
                    <div className="bg-linear-to-br absolute inset-0 -right-px from-white/0 via-white/25 to-white/0"></div>
                    <div className="shadow-xs pointer-events-auto relative inline-flex rounded-md bg-white text-[0.8125rem]/5 font-medium text-slate-700 ring-1 ring-slate-700/10 hover:bg-slate-50 hover:text-slate-900">
                      <div className="flex px-3 py-2">
                        <svg className="mr-2.5 size-5 flex-none fill-slate-400">
                          <path d="M5 4a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v14l-5-2.5L5 18V4Z"></path>
                        </svg>
                        Bookmark
                      </div>
                      <div className="border-l border-slate-400/20 px-2.5 py-2">
                        12k
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* User Cards Section */}
            <div className="relative z-10 p-4">
              <div className="absolute -inset-y-8 right-0 w-px bg-slate-900/15 [mask-image:linear-gradient(to_top,transparent,white_4rem,white_calc(100%-4rem),transparent)]"></div>
              <div className="absolute -inset-y-8 left-0 w-px bg-slate-900/15 [mask-image:linear-gradient(to_top,transparent,white_4rem,white_calc(100%-4rem),transparent)]"></div>
              <div className="absolute -inset-x-8 top-0 h-px bg-slate-900/15 [mask-image:linear-gradient(to_left,transparent,white_4rem,white_calc(100%-4rem),transparent)]"></div>
              <div className="absolute bottom-full left-40 -mb-px flex h-8 items-end overflow-hidden">
                <div className="-mb-px flex h-[2px] w-56">
                  <div className="blur-xs w-full flex-none [background-image:linear-gradient(90deg,rgba(56,189,248,0)_0%,#0EA5E9_32.29%,rgba(236,72,153,0.3)_67.19%,rgba(236,72,153,0)_100%)]"></div>
                  <div className="-ml-[100%] w-full flex-none blur-[1px] [background-image:linear-gradient(90deg,rgba(56,189,248,0)_0%,#0EA5E9_32.29%,rgba(236,72,153,0.3)_67.19%,rgba(236,72,153,0)_100%)]"></div>
                </div>
              </div>

              {/* User Cards List */}
              <div className="w-[24.5rem] divide-y divide-slate-400/20 rounded-lg bg-white text-[0.8125rem]/5 text-slate-900 shadow-xl shadow-black/5 ring-1 ring-slate-700/10">
                {/* User Card 1 */}
                <div className="flex items-center p-4">
                  <img
                    src="/plus/img/avatar-1.jpg"
                    alt=""
                    className="size-10 flex-none rounded-full"
                  />
                  <div className="ml-4 flex-auto">
                    <div className="font-medium">Leonard Krasner</div>
                    <div className="mt-1 text-slate-700">@leonardkrasner</div>
                  </div>
                  <div className="shadow-xs pointer-events-auto ml-4 flex-none rounded-md px-2 py-[0.3125rem] font-medium text-slate-700 ring-1 ring-slate-700/10 hover:bg-slate-50">
                    View
                  </div>
                </div>

                {/* User Card 2 */}
                <div className="flex items-center p-4">
                  <img
                    src="/plus/img/avatar-2.jpg"
                    alt=""
                    className="size-10 flex-none rounded-full"
                  />
                  <div className="ml-4 flex-auto">
                    <div className="font-medium">Floyd Miles</div>
                    <div className="mt-1 text-slate-700">@floydmiles</div>
                  </div>
                  <div className="shadow-xs pointer-events-auto ml-4 flex-none rounded-md px-2 py-[0.3125rem] font-medium text-slate-700 ring-1 ring-slate-700/10 hover:bg-slate-50">
                    View
                  </div>
                </div>

                {/* User Card 3 */}
                <div className="flex items-center p-4">
                  <img
                    src="/plus/img/avatar-3.jpg"
                    alt=""
                    className="size-10 flex-none rounded-full"
                  />
                  <div className="ml-4 flex-auto">
                    <div className="font-medium">Emily Selman</div>
                    <div className="mt-1 text-slate-700">@emilyselman</div>
                  </div>
                  <div className="shadow-xs pointer-events-auto ml-4 flex-none rounded-md px-2 py-[0.3125rem] font-medium text-slate-700 ring-1 ring-slate-700/10 hover:bg-slate-50">
                    View
                  </div>
                </div>

                {/* View All Button */}
                <div className="p-4">
                  <div className="shadow-xs pointer-events-auto rounded-md px-4 py-2 text-center font-medium ring-1 ring-slate-700/10 hover:bg-slate-50">
                    View all
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="flex justify-end">
          <div className="relative z-10 p-4">
            <div className="flex w-[41rem] rounded-lg bg-white shadow-xl shadow-black/5 ring-1 ring-slate-700/10">
              <div className="flex items-center space-x-4 px-6 py-4">
                <svg className="size-6 flex-none" fill="none">
                  <path
                    d="M6.22 11.03a.75.75 0 1 0 1.06-1.06l-1.06 1.06ZM3 6.75l-.53-.53a.75.75 0 0 0 0 1.06L3 6.75Zm4.28-3.22a.75.75 0 0 0-1.06-1.06l1.06 1.06ZM13.5 18a.75.75 0 0 0 0 1.5V18ZM7.28 9.97 3.53 6.22 2.47 7.28l3.75 3.75 1.06-1.06ZM3.53 7.28l3.75-3.75-1.06-1.06-3.75 3.75 1.06 1.06Zm16.72 5.47c0 2.9-2.35 5.25-5.25 5.25v1.5a6.75 6.75 0 0 0 6.75-6.75h-1.5ZM15 7.5c2.9 0 5.25 2.35 5.25 5.25h1.5A6.75 6.75 0 0 0 15 6v1.5ZM15 6H3v1.5h12V6Zm0 12h-1.5v1.5H15V18Z"
                    fill="#64748B"
                  ></path>
                  <path
                    d="M3 15.75h.75V21"
                    stroke="#64748B"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  ></path>
                  <path
                    d="M9 16.5A.75.75 0 0 0 9 15v1.5Zm-2.25-.75V15a.75.75 0 0 0-.75.75h.75Zm0 2.25H6c0 .414.336.75.75.75V18Zm0 2.25a.75.75 0 0 0 0 1.5v-1.5ZM9 15H6.75v1.5H9V15Zm-3 .75V18h1.5v-2.25H6Zm.75 3h1.5v-1.5h-1.5v1.5Zm1.5 1.5h-1.5v1.5h1.5v-1.5ZM9 19.5a.75.75 0 0 1-.75.75v1.5a2.25 2.25 0 0 0 2.25-2.25H9Zm-.75-.75a.75.75 0 0 1 .75.75h1.5a2.25 2.25 0 0 0-2.25-2.25v1.5Z"
                    fill="#64748B"
                  ></path>
                </svg>
                <svg className="size-10 flex-none" fill="none">
                  <circle cx="20" cy="20" r="20" fill="#0F172A"></circle>
                  <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M13.5 13.653c0-1.427 1.529-2.33 2.779-1.643l11.54 6.347c1.295.712 1.295 2.573 0 3.286L16.28 27.99c-1.25.687-2.779-.217-2.779-1.643V13.653Z"
                    fill="#fff"
                  ></path>
                </svg>
                <svg className="size-6 flex-none" fill="none">
                  <path
                    d="M16.72 9.97a.75.75 0 1 0 1.06 1.06l-1.06-1.06ZM21 6.75l.53.53a.75.75 0 0 0 0-1.06l-.53.53Zm-3.22-4.28a.75.75 0 1 0-1.06 1.06l1.06-1.06ZM10.5 19.5a.75.75 0 0 0 0-1.5v1.5Zm3.75-4.5a.75.75 0 0 0 0 1.5V15Zm.75.75h.75A.75.75 0 0 0 15 15v.75ZM14.25 21a.75.75 0 0 0 1.5 0h-1.5Zm6-4.5a.75.75 0 0 0 0-1.5v1.5ZM18 15.75V15a.75.75 0 0 0-.75.75H18ZM18 18h-.75c0 .414.336.75.75.75V18Zm0 2.25a.75.75 0 0 0 0 1.5v-1.5Zm-.22-9.22 3.75-3.75-1.06-1.06-3.75 3.75 1.06 1.06Zm3.75-4.81-3.75-3.75-1.06 1.06 3.75 3.75 1.06-1.06ZM2.25 12.75A6.75 6.75 0 0 0 9 19.5V18a5.25 5.25 0 0 1-5.25-5.25h-1.5ZM9 6a6.75 6.75 0 0 0-6.75 6.75h1.5C3.75 9.85 6.1 7.5 9 7.5V6Zm0 1.5h12V6H9v1.5Zm0 12h1.5V18H9v1.5Zm5.25-3H15V15h-.75v1.5Zm0-.75V21h1.5v-5.25h-1.5Zm6-.75H18v1.5h2.25V15Zm-3 .75V18h1.5v-2.25h-1.5Zm.75 3h1.5v-1.5H18v1.5Zm1.5 1.5H18v1.5h1.5v-1.5Zm.75-.75a.75.75 0 0 1-.75.75v1.5a2.25 2.25 0 0 0 2.25-2.25h-1.5Zm-.75-.75a.75.75 0 0 1 .75.75h1.5a2.25 2.25 0 0 0-2.25-2.25v1.5Z"
                    fill="#64748B"
                  ></path>
                </svg>
              </div>
              <div className="flex flex-auto items-center border-l border-slate-200/60 pl-6 pr-4 text-[0.8125rem]/5 text-slate-700">
                <div>00:51</div>
                <div className="ml-4 flex flex-auto rounded-full bg-slate-100">
                  <div className="h-2 w-1/3 flex-none rounded-l-full rounded-r-[1px] bg-indigo-600"></div>
                  <div className="-my-[0.3125rem] ml-0.5 h-[1.125rem] w-1 rounded-full bg-indigo-600"></div>
                </div>
                <div className="ml-4">55:43</div>
                <svg className="ml-6 size-6 flex-none" fill="none">
                  <path
                    d="M14 5 9 9H6a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h3l5 4V5Z"
                    fill="#64748B"
                    stroke="#64748B"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  ></path>
                  <path
                    d="M19 12c0-1.5-1-2-1-2v4s1-.5 1-2Z"
                    stroke="#64748B"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  ></path>
                </svg>
                <svg className="ml-6 size-6 flex-none" fill="none">
                  <path
                    d="M12 8v1a1 1 0 0 0 1-1h-1Zm0 0h-1a1 1 0 0 0 1 1V8Zm0 0V7a1 1 0 0 0-1 1h1Zm0 0h1a1 1 0 0 0-1-1v1ZM12 12v1a1 1 0 0 0 1-1h-1Zm0 0h-1a1 1 0 0 0 1 1v-1Zm0 0v-1a1 1 0 0 0-1 1h1Zm0 0h1a1 1 0 0 0-1-1v1ZM12 16v1a1 1 0 0 0 1-1h-1Zm0 0h-1a1 1 0 0 0 1 1v-1Zm0 0v-1a1 1 0 0 0-1 1h1Zm0 0h1a1 1 0 0 0-1-1v1Z"
                    fill="#64748B"
                  ></path>
                </svg>
              </div>
            </div>
          </div>
        </div>

        {/* Theme Selector and Other Controls */}
        <div className="flex items-start justify-end">
          <div>
            <div className="relative flex items-end justify-end">
              <div className="absolute -inset-x-8 bottom-0 h-px bg-slate-900/15 [mask-image:linear-gradient(to_left,transparent,white_4rem,white_calc(100%-4rem),transparent)]"></div>
              <div>
                <div className="flex justify-end p-4">
                  <div className="shadow-xs pointer-events-auto flex divide-x divide-slate-400/20 overflow-hidden rounded-md bg-white text-[0.8125rem]/5 font-medium text-slate-700 ring-1 ring-slate-700/10">
                    <div className="px-4 py-2 hover:bg-slate-50 hover:text-slate-900">
                      Years
                    </div>
                    <div className="px-4 py-2 hover:bg-slate-50 hover:text-slate-900">
                      Months
                    </div>
                    <div className="px-4 py-2 hover:bg-slate-50 hover:text-slate-900">
                      Days
                    </div>
                  </div>
                </div>

                {/* Theme Selector */}
                <div className="relative z-10 p-4">
                  <div className="absolute -inset-x-8 top-0 h-px bg-slate-900/15 [mask-image:linear-gradient(to_left,transparent,white_4rem,white_calc(100%-4rem),transparent)]"></div>
                  <div className="absolute -inset-y-8 left-0 w-px bg-slate-900/15 [mask-image:linear-gradient(to_top,transparent,white_4rem,white_calc(100%-4rem),transparent)]"></div>
                  <div className="absolute -inset-y-8 right-0 w-px bg-slate-900/15 [mask-image:linear-gradient(to_top,transparent,white_4rem,white_calc(100%-4rem),transparent)]"></div>
                  <div className="absolute bottom-full left-16 -mb-px flex h-8 items-end overflow-hidden">
                    <div className="-mb-px flex h-[2px] w-56">
                      <div className="blur-xs w-full flex-none [background-image:linear-gradient(90deg,rgba(56,189,248,0)_0%,#0EA5E9_32.29%,rgba(236,72,153,0.3)_67.19%,rgba(236,72,153,0)_100%)]"></div>
                      <div className="-ml-[100%] w-full flex-none blur-[1px] [background-image:linear-gradient(90deg,rgba(56,189,248,0)_0%,#0EA5E9_32.29%,rgba(236,72,153,0.3)_67.19%,rgba(236,72,153,0)_100%)]"></div>
                    </div>
                  </div>

                  {/* Logo and Version Section */}
                  <div className="flex w-[23.5625rem] items-center rounded-md bg-white p-4 shadow-xl shadow-black/5 ring-1 ring-slate-700/10">
                    {/* Menu Icon */}
                    <svg
                      className="size-6 flex-none stroke-slate-500"
                      strokeWidth="2"
                      strokeLinecap="round"
                      fill="none"
                    >
                      <path d="M4 7h16M4 12h16M4 17h16"></path>
                    </svg>

                    {/* Tailwind Logo */}
                    <svg
                      className="ml-6 size-10 flex-none"
                      viewBox="0 0 40 40"
                      fill="none"
                    >
                      <path
                        fillRule="evenodd"
                        clipRule="evenodd"
                        d="M10.8322 6.12083C11.1486 5.56355 11.74 5.21924 12.3808 5.21924H27.1431C27.7322 5.21924 28.2831 5.51055 28.6148 5.99738L37.4718 18.9974C37.8542 19.5587 37.884 20.2887 37.5487 20.8793L30.1679 33.8793C29.8515 34.4366 29.2601 34.7809 28.6192 34.7809L12.3808 34.7809C11.74 34.7809 11.1486 34.4366 10.8322 33.8793L3.45137 20.8793C3.14178 20.334 3.14178 19.6661 3.45137 19.1208L10.8322 6.12083ZM12.3808 10.607L17.7138 20.0001L12.3808 29.3931L7.04787 20.0001L12.3808 10.607ZM15.4397 31.2192L27.5825 31.2192L32.9411 21.7809H20.7984L15.4397 31.2192ZM20.7984 18.2192H32.6319L26.2015 8.78092H15.4397L20.7984 18.2192Z"
                        fill="#38BDF8"
                      ></path>
                    </svg>

                    {/* Settings and Version */}
                    <div className="ml-auto flex size-6 items-center justify-center rounded-md shadow-sm ring-1 ring-slate-900/10">
                      <svg className="size-4" viewBox="0 0 16 16" fill="none">
                        <path
                          fillRule="evenodd"
                          clipRule="evenodd"
                          d="M7 1C7 0.447715 7.44772 0 8 0C8.55228 0 9 0.447715 9 1V2C9 2.55228 8.55228 3 8 3C7.44772 3 7 2.55228 7 2V1ZM11 8C11 9.65685 9.65685 11 8 11C6.34315 11 5 9.65685 5 8C5 6.34315 6.34315 5 8 5C9.65685 5 11 6.34315 11 8ZM13.6563 2.34285C13.2658 1.95232 12.6326 1.95232 12.2421 2.34285L11.535 3.04996C11.1445 3.44048 11.1445 4.07365 11.535 4.46417C11.9255 4.85469 12.5587 4.85469 12.9492 4.46417L13.6563 3.75706C14.0469 3.36654 14.0469 2.73337 13.6563 2.34285ZM12.242 13.6563L11.5349 12.9492C11.1443 12.5587 11.1443 11.9255 11.5349 11.535C11.9254 11.1445 12.5585 11.1445 12.9491 11.535L13.6562 12.2421C14.0467 12.6326 14.0467 13.2658 13.6562 13.6563C13.2656 14.0468 12.6325 14.0468 12.242 13.6563ZM16 7.99902C16 7.44674 15.5523 6.99902 15 6.99902H14C13.4477 6.99902 13 7.44674 13 7.99902C13 8.55131 13.4477 8.99902 14 8.99902H15C15.5523 8.99902 16 8.55131 16 7.99902ZM7 14C7 13.4477 7.44772 13 8 13C8.55228 13 9 13.4477 9 14V15C9 15.5523 8.55228 16 8 16C7.44772 16 7 15.5523 7 15V14ZM4.46492 11.5352C4.0744 11.1447 3.44123 11.1447 3.05071 11.5352L2.3436 12.2423C1.95307 12.6329 1.95307 13.266 2.3436 13.6566C2.73412 14.0471 3.36729 14.0471 3.75781 13.6566L4.46492 12.9494C4.85544 12.5589 4.85544 11.9258 4.46492 11.5352ZM4.46477 3.04973C4.85529 3.44025 4.85529 4.07342 4.46477 4.46394C4.07424 4.85447 3.44108 4.85447 3.05055 4.46394L2.34345 3.75684C1.95292 3.36631 1.95292 2.73315 2.34345 2.34262C2.73397 1.9521 3.36714 1.9521 3.75766 2.34262L4.46477 3.04973ZM3 8C3 7.44772 2.55228 7 2 7H1C0.447715 7 0 7.44772 0 8C0 8.55228 0.447715 9 1 9H2C2.55228 9 3 8.55228 3 8Z"
                          fill="#38BDF8"
                        ></path>
                      </svg>
                    </div>
                    <div className="ml-6 text-[0.8125rem] font-medium text-slate-700">
                      v3.0
                    </div>
                    <svg className="ml-2 size-1.5 overflow-visible fill-slate-400 stroke-slate-400">
                      <path
                        d="M0 0H6L3 4Z"
                        strokeWidth="1"
                        strokeLinejoin="round"
                      ></path>
                    </svg>
                    <svg
                      viewBox="0 0 16 16"
                      className="ml-6 size-6 fill-slate-400"
                    >
                      <path d="M8 0C3.58 0 0 3.58 0 8C0 11.54 2.29 14.53 5.47 15.59C5.87 15.66 6.02 15.42 6.02 15.21C6.02 15.02 6.01 14.39 6.01 13.72C4 14.09 3.48 13.23 3.32 12.78C3.23 12.55 2.84 11.84 2.5 11.65C2.22 11.5 1.82 11.13 2.49 11.12C3.12 11.11 3.57 11.7 3.72 11.94C4.44 13.15 5.59 12.81 6.05 12.6C6.12 12.08 6.33 11.73 6.56 11.53C4.78 11.33 2.92 10.64 2.92 7.58C2.92 6.71 3.23 5.99 3.74 5.43C3.66 5.23 3.38 4.41 3.82 3.31C3.82 3.31 4.49 3.1 6.02 4.13C6.66 3.95 7.34 3.86 8.02 3.86C8.7 3.86 9.38 3.95 10.02 4.13C11.55 3.09 12.22 3.31 12.22 3.31C12.66 4.41 12.38 5.23 12.3 5.43C12.81 5.99 13.12 6.7 13.12 7.58C13.12 10.65 11.25 11.33 9.47 11.53C9.76 11.78 10.01 12.26 10.01 13.01C10.01 14.08 10 14.94 10 15.21C10 15.42 10.15 15.67 10.55 15.59C13.71 14.53 16 11.53 16 8C16 3.58 12.42 0 8 0Z"></path>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* I'll continue with the rest in the next part */}
      </div>
    </div>
  )
}

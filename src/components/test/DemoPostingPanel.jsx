'use client'
import React, { useRef } from 'react'
import { Panel } from 'primereact/panel'
import { Avatar } from 'primereact/avatar'
import { Menu } from 'primereact/menu'
import { Button } from 'primereact/button'

export function DemoPosting() {
  const headerTemplate = (options) => {
    const className = `${options.className} justify-content-space-between bg-white rounded-none`

    return (
      <div className={className}>
        <div className="align-items-center flex gap-2">
          <h2 className="text-lg font-semibold text-gray-800">
            modern kitchen island
          </h2>
        </div>
        <div>
          <div className="flex items-center gap-2">
            <span className="inline-flex items-center rounded-md bg-blue-100 px-2 font-semibold text-blue-800">
              <i className="pi pi-images me-1" /> 5
            </span>
            <span className="rounded-md bg-blue-100 px-2">
              <i className="pi pi-check align-[-1px] font-semibold text-blue-800" />
            </span>
          </div>
        </div>
      </div>
    )
  }

  const footerTemplate = (options) => {
    const className = `${options.className} flex flex-wrap align-items-center justify-content-between gap-3`

    return (
      <div className={className}>
        <div className="align-items-center flex gap-2">
          <Button icon="pi pi-pencil" text severity="secondary"></Button>
          <Button icon="pi pi-external-link" severity="secondary" text></Button>
        </div>
      </div>
    )
  }

  return (
    <div className="relative mt-8 flex flex-col rounded-lg bg-white shadow-lg lg:mt-0">
      <div className="absolute -inset-x-8 top-0 h-px bg-slate-900/15 [mask-image:linear-gradient(to_left,transparent,white_4rem,white_calc(100%-4rem),transparent)]"></div>
      <div className="absolute -inset-y-8 left-0 w-px bg-slate-900/15 [mask-image:linear-gradient(to_top,transparent,white_4rem,white_calc(100%-4rem),transparent)]"></div>
      <div className="absolute -inset-y-8 right-0 w-px bg-slate-900/15 [mask-image:linear-gradient(to_top,transparent,white_4rem,white_calc(100%-4rem),transparent)]"></div>
      <div className="absolute bottom-full left-16 -mb-px flex h-8 items-end overflow-hidden">
        <div className="-mb-px flex h-[2px] w-56">
          <div className="blur-xs w-full flex-none [background-image:linear-gradient(90deg,rgba(56,189,248,0)_0%,#0EA5E9_32.29%,rgba(236,72,153,0.3)_67.19%,rgba(236,72,153,0)_100%)]"></div>
          <div className="-ml-[100%] w-full flex-none blur-[1px] [background-image:linear-gradient(90deg,rgba(56,189,248,0)_0%,#0EA5E9_32.29%,rgba(236,72,153,0.3)_67.19%,rgba(236,72,153,0)_100%)]"></div>
        </div>
      </div>

      {/* Header */}
      <div className="flex items-center justify-between border-b p-4">
        <div className="flex items-center gap-2">
          <i className="pi pi-send text-lg" />
          <span className="text-lg">Post Keyword</span>
        </div>
        <div className="flex gap-4">
          <i className="pi pi-window-minimize cursor-pointer" />
          <i className="pi pi-times cursor-pointer" />
        </div>
      </div>

      {/* Main Content */}
      <div className="p-3">
        <Panel headerTemplate={headerTemplate} footerTemplate={footerTemplate}>
          <div className="p-1">
            {/* Status messages */}
            <div className="mt-0 space-y-4">
              <div className="font-semibold text-blue-800">
                <i className="pi pi-check-circle me-2" />
                New post (Lighting Strategies That Elevate a Modern Kitchen
                Island) created.
              </div>

              <hr className="border-gray-200" />

              <div className="inline-flex items-center font-semibold text-blue-800">
                <i className="pi pi-check-circle me-2" />
                Images upload finished
                <span className="ml-2 text-nowrap rounded-md bg-blue-100 px-2">
                  <i className="pi pi-images me-1 align-[-1px]" /> 5 / 5
                </span>
              </div>

              <hr className="border-gray-200" />

              <div className="font-semibold text-blue-800">
                <i className="pi pi-check-circle me-2" />
                Post published (1431 words).
              </div>
            </div>
          </div>
        </Panel>
      </div>
    </div>
  )
}

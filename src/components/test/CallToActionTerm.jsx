import { Container } from '@/components/Container'

export function CallToAction() {
  return (
    <section className="relative overflow-hidden bg-gradient-to-b from-gray-900 via-[#111318] to-[#0a0b11] py-24 lg:py-32">
      {/* Enhanced Background */}
      <div className="absolute inset-0 bg-[linear-gradient(to_right,#0066ff08_1px,transparent_1px),linear-gradient(to_bottom,#0066ff08_1px,transparent_1px)] bg-[size:24px_24px]" />
      <div className="animate-pulse-slow absolute left-0 top-0 -z-10 h-96 w-96 rounded-full bg-blue-500/10 blur-[128px]" />
      <div className="animate-pulse-slow absolute bottom-0 right-0 -z-10 h-96 w-96 rounded-full bg-violet-500/10 blur-[128px]" />

      <Container className="relative">
        <div className="mx-auto max-w-3xl">
          {/* Main Content Card */}
          <div className="relative overflow-hidden rounded-lg border border-blue-900/30 bg-[#0c0d12]/80 backdrop-blur-xl">
            {/* Header */}
            <div className="px-8 pt-12 text-center">
              <div className="inline-flex items-center gap-2 rounded-full border border-blue-500/20 bg-blue-500/5 px-3 py-1 font-mono text-sm text-blue-400">
                <span className="h-1.5 w-1.5 animate-pulse rounded-full bg-blue-500" />
                WordPress Automation
              </div>

              <h2 className="mt-6 bg-gradient-to-r from-gray-200 to-white bg-clip-text text-4xl font-semibold text-transparent sm:text-5xl">
                Supercharge Your Content
              </h2>

              <p className="mt-4 text-lg text-gray-400">
                Generate, optimize, and publish content at{' '}
                <span className="text-blue-400">superhuman speed</span>
              </p>
            </div>

            {/* Feature Highlights */}
            <div className="mt-8 grid grid-cols-3 gap-0.5 bg-blue-900/20 p-0.5">
              {[
                ['AI-Powered', 'Intelligent content generation'],
                ['Lightning Fast', '10x faster workflow'],
                ['SEO Ready', 'Optimized for search'],
              ].map(([title, desc]) => (
                <div key={title} className="bg-[#0c0d12] p-4 text-center">
                  <div className="font-mono text-sm font-medium text-blue-400">
                    {title}
                  </div>
                  <div className="mt-1 text-xs text-gray-500">{desc}</div>
                </div>
              ))}
            </div>

            {/* CTA Section */}
            <div className="px-8 py-12 text-center">
              <div className="flex flex-col items-center gap-4 sm:flex-row sm:justify-center">
                {/* Primary CTA */}
                <a
                  href="/register"
                  className="group relative w-full overflow-hidden rounded-lg bg-blue-500 px-8 py-3 text-sm font-medium text-white transition-all hover:bg-blue-400 sm:w-auto"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-blue-500 opacity-0 transition-opacity group-hover:opacity-100" />
                  <span className="relative flex items-center justify-center gap-2">
                    Download for Free
                    <span className="transition-transform group-hover:translate-x-1">
                      →
                    </span>
                  </span>
                </a>

                {/* Secondary CTA */}
                <a
                  href="#demo"
                  className="group flex items-center gap-2 text-sm font-medium text-gray-400 transition-colors hover:text-white"
                >
                  Watch Demo
                </a>
              </div>

              {/* Trust Indicators */}
              {/* <div className="mt-8 flex justify-center gap-8"> */}
              {/*   {[ */}
              {/*     ['14-day free trial', 'pi pi-clock'], */}
              {/*     ['No credit card', 'pi pi-credit-card'], */}
              {/*     ['Cancel anytime', 'pi pi-times'], */}
              {/*   ].map(([text, icon]) => ( */}
              {/*     <div */}
              {/*       key={text} */}
              {/*       className="flex items-center gap-2 text-xs text-gray-500" */}
              {/*     > */}
              {/*       <i className={`${icon} text-gray-400`} /> */}
              {/*       {text} */}
              {/*     </div> */}
              {/*   ))} */}
              {/* </div> */}
            </div>

            {/* Decorative Elements */}
            <div className="absolute left-0 top-0 h-px w-full bg-gradient-to-r from-transparent via-blue-500/50 to-transparent" />
            <div className="absolute bottom-0 left-0 h-px w-full bg-gradient-to-r from-transparent via-blue-500/30 to-transparent" />
          </div>

          {/* Social Proof */}
          <div className="mt-8 text-center">
            <p className="text-sm text-gray-500">
              Trusted by 10,000+ content creators worldwide
            </p>
          </div>
        </div>
      </Container>
    </section>
  )
}

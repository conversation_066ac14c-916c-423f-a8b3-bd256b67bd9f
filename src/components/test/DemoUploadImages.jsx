'use client'

import React, { useState, useEffect } from 'react'
import { Image } from 'primereact/image'

export function DemoUploadImages() {
  const [isMaximized, setIsMaximized] = useState(false)
  const [selectedIndexes, setSelectedIndexes] = useState([])
  const [isSelecting, setIsSelecting] = useState(true)

  useEffect(() => {
    const simulateSelection = async () => {
      // Left to right, top to bottom selection pattern
      const selectionOrder = [
        0,
        1,
        2,
        3,
        4,
        5,
        6, // First row
        7,
        8,
        9,
        10,
        11,
        12,
        13, // Second row
        14,
        15,
        16,
        17,
        18,
        19, // Third row
      ]

      // Selection phase
      for (const index of selectionOrder) {
        if (isSelecting) {
          await new Promise((resolve) =>
            setTimeout(resolve, 100 + Math.random() * 200),
          )
          setSelectedIndexes((prev) => [...prev, index])
        }
      }

      // Pause at full selection
      await new Promise((resolve) => setTimeout(resolve, 1000))
      setIsSelecting(false)

      // Deselection phase
      for (const index of [...selectionOrder].reverse()) {
        await new Promise((resolve) =>
          setTimeout(resolve, 50 + Math.random() * 150),
        )
        setSelectedIndexes((prev) => prev.filter((i) => i !== index))
      }

      // Reset cycle
      await new Promise((resolve) => setTimeout(resolve, 800))
      setIsSelecting(true)
    }

    simulateSelection()
  }, [isSelecting])

  const imagesData = [
    {
      source_url:
        'http://cdn.home-designing.com/wp-content/uploads/2018/06/kitchen-island-pendant-lighting.jpg',
      source_page_url:
        'http://www.home-designing.com/modern-kitchen-island-design-ideas',
      width: 1200,
      height: 1600,
      title: '50 Stunning Modern Kitchen Island Designs',
      thumbnail:
        'https://tse3.mm.bing.net/th?id=OIP.3SGSW_OSle8Su5nr5yS9rQHaJ4&pid=Api',
      source_domain: 'cdn.home-designing.com',
      source_page_domain: 'www.home-designing.com',
      source: 'duckduckgo',
    },
    {
      source_url:
        'http://cdn.home-designing.com/wp-content/uploads/2018/06/Kitchen-island-with-sink-and-stools.jpg',
      source_page_url:
        'http://www.home-designing.com/modern-kitchen-island-design-ideas',
      width: 1200,
      height: 900,
      title: '50 Stunning Modern Kitchen Island Designs',
      thumbnail:
        'https://tse3.mm.bing.net/th?id=OIP.wKL0o6zHGj40pu-vJCK_MwHaFj&pid=Api',
      source_domain: 'cdn.home-designing.com',
      source_page_domain: 'www.home-designing.com',
      source: 'duckduckgo',
    },
    {
      source_url:
        'http://cdn.home-designing.com/wp-content/uploads/2018/06/black-and-white-kitchen-island-with-sink.jpg',
      source_page_url:
        'http://www.home-designing.com/modern-kitchen-island-design-ideas',
      width: 1200,
      height: 1572,
      title: '50 Stunning Modern Kitchen Island Designs',
      thumbnail:
        'https://tse4.mm.bing.net/th?id=OIP.18oSNJt2FtEWjRlYlKFinAHaJs&pid=Api',
      source_domain: 'cdn.home-designing.com',
      source_page_domain: 'www.home-designing.com',
      source: 'duckduckgo',
    },
    {
      source_url:
        'https://hgtvhome.sndimg.com/content/dam/images/hgtv/fullset/2013/9/12/4/DP_Linda-Sullivan-neutral-transitional-kitchen_s4x3.jpg.rend.hgtvcom.1280.960.suffix/1400983531310.jpeg',
      source_page_url:
        'https://www.hgtv.com/design/rooms/kitchens/modern-kitchen-islands',
      width: 1280,
      height: 960,
      title: 'Modern Kitchen Islands: Pictures, Ideas & Tips From HGTV | HGTV',
      thumbnail:
        'https://tse4.mm.bing.net/th?id=OIP.T46kPJ5rvF9q0EdhKRvDiAHaFj&pid=Api',
      source_domain: 'hgtvhome.sndimg.com',
      source_page_domain: 'www.hgtv.com',
      source: 'duckduckgo',
    },
    {
      source_url:
        'https://www.homestratosphere.com/wp-content/uploads/2018/08/kitchen-island-photo2018-08-03-at-8.40.26-AM-40.jpg',
      source_page_url: 'https://www.homestratosphere.com/kitchen-island-ideas/',
      width: 474,
      height: 710,
      title:
        '90 Different Kitchen Island Ideas and Designs (Photos) - Home Stratosphere',
      thumbnail:
        'https://tse4.mm.bing.net/th?id=OIP.xBrwxeBgDk6MP6fwlc4a8wAAAA&pid=Api',
      source_domain: 'www.homestratosphere.com',
      source_page_domain: 'www.homestratosphere.com',
      source: 'duckduckgo',
    },
    {
      source_url:
        'http://cdn.home-designing.com/wp-content/uploads/2018/06/pendant-lighting-for-kitchen-island.jpg',
      source_page_url:
        'http://www.home-designing.com/modern-kitchen-island-design-ideas',
      width: 1200,
      height: 1680,
      title: '50 Stunning Modern Kitchen Island Designs',
      thumbnail:
        'https://tse1.mm.bing.net/th?id=OIP.E3cv8Q_d-JsLbzf7q_ySKwHaKX&pid=Api',
      source_domain: 'cdn.home-designing.com',
      source_page_domain: 'www.home-designing.com',
      source: 'duckduckgo',
    },
    {
      source_url:
        'http://cdn.onekindesign.com/wp-content/uploads/2016/03/Kitchen-Island-Ideas-32-1-Kindesign.jpg',
      source_page_url:
        'http://onekindesign.com/2016/03/18/brilliant-kitchen-island-ideas/',
      width: 1316,
      height: 1390,
      title: '30+ Brilliant kitchen island ideas that make a statement',
      thumbnail:
        'https://tse3.mm.bing.net/th?id=OIP.F0f8WbXgse2VWHwYC59tvwHaH0&pid=Api',
      source_domain: 'cdn.onekindesign.com',
      source_page_domain: 'onekindesign.com',
      source: 'duckduckgo',
    },
    {
      source_url:
        'https://www.impressiveinteriordesign.com/wp-content/uploads/2016/04/Modern-Kitchen-Island-Designs-With-Seating-7.jpg',
      source_page_url:
        'https://www.impressiveinteriordesign.com/modern-kitchen-island-designs-seating/',
      width: 1000,
      height: 1505,
      title: 'Modern Kitchen Island Designs With Seating',
      thumbnail:
        'https://tse1.mm.bing.net/th?id=OIP.XaK7RR8aStCjxAUZLAZF_wHaLJ&pid=Api',
      source_domain: 'www.impressiveinteriordesign.com',
      source_page_domain: 'www.impressiveinteriordesign.com',
      source: 'duckduckgo',
    },
    {
      source_url:
        'http://cdn.home-designing.com/wp-content/uploads/2018/06/black-kitchen-island.jpg',
      source_page_url:
        'http://www.home-designing.com/modern-kitchen-island-design-ideas',
      width: 1200,
      height: 726,
      title: '50 Stunning Modern Kitchen Island Designs',
      thumbnail:
        'https://tse2.explicit.bing.net/th?id=OIP.Paxy6CiGoNB3BQVVsrlo_gHaEe&pid=Api',
      source_domain: 'cdn.home-designing.com',
      source_page_domain: 'www.home-designing.com',
      source: 'duckduckgo',
    },
    {
      source_url:
        'http://cdn.home-designing.com/wp-content/uploads/2018/06/kitchen-island-with-seating.jpg',
      source_page_url:
        'http://www.home-designing.com/modern-kitchen-island-design-ideas',
      width: 1200,
      height: 777,
      title: '50 Stunning Modern Kitchen Island Designs',
      thumbnail:
        'https://tse3.mm.bing.net/th?id=OIP.scJBcKjr0hvj5lD01U0xYwHaEy&pid=Api',
      source_domain: 'cdn.home-designing.com',
      source_page_domain: 'www.home-designing.com',
      source: 'duckduckgo',
    },
    {
      source_url:
        'http://decor-ideas.co.uk/wp-content/uploads/2020/01/kitchen-island-ideas-3.jpg',
      source_page_url:
        'http://decor-ideas.co.uk/6-x-modern-kitchen-island-design-ideas/',
      width: 951,
      height: 960,
      title: '6 x Modern Kitchen Island Design Ideas',
      thumbnail:
        'https://tse4.mm.bing.net/th?id=OIP.nfdWZOhG6Q6tm6xYYsf-yAHaHe&pid=Api',
      source_domain: 'decor-ideas.co.uk',
      source_page_domain: 'decor-ideas.co.uk',
      source: 'duckduckgo',
    },
    {
      source_url:
        'https://i.pinimg.com/originals/ff/75/8d/ff758d185ebd66009d48215d5a960548.jpg',
      source_page_url: 'https://www.pinterest.com/pin/109845678395405384/',
      width: 5715,
      height: 3810,
      title:
        'Modern kitchen with waterfall island countertop | Modern kitchen island ...',
      thumbnail:
        'https://tse4.mm.bing.net/th?id=OIP.UAPHuuJttRbfeq5N8UD1IQHaE8&pid=Api',
      source_domain: 'i.pinimg.com',
      source_page_domain: 'www.pinterest.com',
      source: 'duckduckgo',
    },
    {
      source_url:
        'https://www.architectureartdesigns.com/wp-content/uploads/2016/05/17-78.jpg',
      source_page_url:
        'https://www.architectureartdesigns.com/18-remarkable-kitchen-islands-with-seating-place-that-everyone-will-love/',
      width: 1000,
      height: 1234,
      title:
        '18 Remarkable Kitchen Islands With Seating Place That Everyone Will Love',
      thumbnail:
        'https://tse2.mm.bing.net/th?id=OIP.Hw4Y5ml9hBIhpFr_CR-rSwHaJI&pid=Api',
      source_domain: 'www.architectureartdesigns.com',
      source_page_domain: 'www.architectureartdesigns.com',
      source: 'duckduckgo',
    },
    {
      source_url:
        'https://www.mydomaine.com/thmb/qxjuVGineQr8n7QijV8pI3uxf50=/5323x3936/filters:fill(auto,1)/DesignWorks-baf347a8ce734ebc8d039f07f996743a.jpg',
      source_page_url: 'https://www.mydomaine.com/kitchen-island-ideas-4842299',
      width: 5323,
      height: 3936,
      title: '18 Stylish Kitchen Island Design Ideas',
      thumbnail:
        'https://tse1.mm.bing.net/th?id=OIP.qO6wHbp9sSFFtqJ_GzcdagHaFe&pid=Api',
      source_domain: 'www.mydomaine.com',
      source_page_domain: 'www.mydomaine.com',
      source: 'duckduckgo',
    },
    {
      source_url:
        'https://i1.wp.com/sortingwithstyle.com/wp-content/uploads/2018/12/5.-Extended-Kitchen-Island.jpg?resize=657%2C985&ssl=1',
      source_page_url:
        'https://sortingwithstyle.com/kitchen-island-design-ideas/',
      width: 657,
      height: 985,
      title:
        '40 Awesome Kitchen Island Design Ideas with Modern Decor & Layout',
      thumbnail:
        'https://tse4.mm.bing.net/th?id=OIP.alXd9lYAyMo0aI8l9rHbVwHaLG&pid=Api',
      source_domain: 'i1.wp.com',
      source_page_domain: 'sortingwithstyle.com',
      source: 'duckduckgo',
    },
    {
      source_url:
        'http://cdn.home-designing.com/wp-content/uploads/2018/06/rustic-kitchen-island.jpg',
      source_page_url:
        'http://www.home-designing.com/modern-kitchen-island-design-ideas',
      width: 474,
      height: 682,
      title: '50 Stunning Modern Kitchen Island Designs',
      thumbnail:
        'https://tse1.mm.bing.net/th?id=OIP.i0prDo3tgsk6Jic0yrrC3AAAAA&pid=Api',
      source_domain: 'cdn.home-designing.com',
      source_page_domain: 'www.home-designing.com',
      source: 'duckduckgo',
    },
    {
      source_url:
        'https://www.thespruce.com/thmb/pb0ldANf6EkNZ-OYKlSVtWUSodc=/5134x3423/filters:no_upscale():max_bytes(150000):strip_icc()/KitchenIslandwithSeating-494358561-59a3b217af5d3a001125057e.jpg',
      source_page_url:
        'https://www.thespruce.com/incredible-kitchen-islands-with-seating-1822164',
      width: 5134,
      height: 3423,
      title: '10 Incredible Kitchen Islands With Sinks and Seating',
      thumbnail:
        'https://tse2.mm.bing.net/th?id=OIP.r-DaQAQTBfp31a1MNk3l6gHaE8&pid=Api',
      source_domain: 'www.thespruce.com',
      source_page_domain: 'www.thespruce.com',
      source: 'duckduckgo',
    },
    {
      source_url:
        'https://designingidea.com/wp-content/uploads/2016/02/open-concept-modern-kitchen-design-with-large-island.jpg',
      source_page_url: 'https://designingidea.com/modern-kitchen-islands/',
      width: 850,
      height: 837,
      title: '33 Modern Kitchen Islands (Design Ideas) - Designing Idea',
      thumbnail:
        'https://tse4.mm.bing.net/th?id=OIP.euwy5FTNaPcRocPpmoxDsQHaHS&pid=Api',
      source_domain: 'designingidea.com',
      source_page_domain: 'designingidea.com',
      source: 'duckduckgo',
    },
    {
      source_url:
        'http://3.bp.blogspot.com/-N6uMd0aux2g/UORbUE_gqWI/AAAAAAAAVZ0/5Xwb5g-iFD8/s1600/b31fb__Spectacular-Contemporary-Design-Kitchen-Island-With-Aspirator.jpg',
      source_page_url:
        'http://www.design-laorosa.com/2013/01/moderncontemporary-kitchen-island.html',
      width: 1200,
      height: 946,
      title:
        'LAOROSA | DESIGN-JUNKY: Modern & Contemporary Kitchen Island Designs ...',
      thumbnail:
        'https://tse2.mm.bing.net/th?id=OIP.IC4B0DwFwJqDNbqRid0IBAHaF1&pid=Api',
      source_domain: '3.bp.blogspot.com',
      source_page_domain: 'www.design-laorosa.com',
      source: 'duckduckgo',
    },
    {
      source_url:
        'http://dwellingdecor.com/wp-content/uploads/2015/12/Kitchen-Designs-With-Islands-Ideas.jpg',
      source_page_url:
        'http://www.dwellingdecor.com/30-amazing-kitchen-island-ideas-home/',
      width: 915,
      height: 1023,
      title: '30 Amazing Kitchen Island Ideas For Your Home',
      thumbnail:
        'https://tse4.mm.bing.net/th?id=OIP.pbw_cjVdCuuLDvhh3v_dlAHaIR&pid=Api',
      source_domain: 'dwellingdecor.com',
      source_page_domain: 'www.dwellingdecor.com',
      source: 'duckduckgo',
    },
    {
      source_url:
        'https://www.impressiveinteriordesign.com/wp-content/uploads/2012/11/Modern-And-Traditional-Kitchen-Island-Ideas-You-Should-See-2-889x1024.jpg',
      source_page_url:
        'https://www.impressiveinteriordesign.com/modern-and-traditional-kitchen-island-ideas-you-should-see/',
      width: 889,
      height: 1024,
      title: 'Modern And Traditional Kitchen Island Ideas You Should See',
      thumbnail:
        'https://tse1.mm.bing.net/th?id=OIP.iHWmY4Bg_gfhPRRqO4eefwHaIh&pid=Api',
      source_domain: 'www.impressiveinteriordesign.com',
      source_page_domain: 'www.impressiveinteriordesign.com',
      source: 'duckduckgo',
    },
    {
      source_url:
        'https://www.architectureartdesigns.com/wp-content/uploads/2016/04/16-28.jpg',
      source_page_url:
        'https://www.architectureartdesigns.com/19-irresistible-modern-kitchen-islands-that-will-make-you-say-wow/',
      width: 1200,
      height: 787,
      title:
        '19 Irresistible Modern Kitchen Islands That Will Make You Say Wow',
      thumbnail:
        'https://tse4.mm.bing.net/th?id=OIP.x2rGDAFKN5HJsfiYN-oOBQHaE2&pid=Api',
      source_domain: 'www.architectureartdesigns.com',
      source_page_domain: 'www.architectureartdesigns.com',
      source: 'duckduckgo',
    },
    {
      source_url:
        'http://cdn.onekindesign.com/wp-content/uploads/2017/02/Dream-Kitchen-Islands-04-1-Kindesign.jpg',
      source_page_url:
        'http://onekindesign.com/2017/02/24/dream-kitchen-islands/',
      width: 1000,
      height: 1509,
      title: '25+ Dream kitchen islands that are utterly drool worthy',
      thumbnail:
        'https://tse4.mm.bing.net/th?id=OIP.Ysi4oYjZSpQrc6ZFz7iiHwHaLL&pid=Api',
      source_domain: 'cdn.onekindesign.com',
      source_page_domain: 'onekindesign.com',
      source: 'duckduckgo',
    },
    {
      source_url:
        'https://www.homestratosphere.com/wp-content/uploads/2018/09/modern-kitchen-design-photo2018-09-12-at-2.36.56-PM-8.jpg',
      source_page_url: 'https://www.homestratosphere.com/modern-kitchen-ideas/',
      width: 1236,
      height: 849,
      title: '60 Modern Kitchen Design Ideas (Photos) - Home Stratosphere',
      thumbnail:
        'https://tse1.mm.bing.net/th?id=OIP.NqLE_GkCCNU8eiA6Nz8lugHaFF&pid=Api',
      source_domain: 'www.homestratosphere.com',
      source_page_domain: 'www.homestratosphere.com',
      source: 'duckduckgo',
    },
    {
      source_url:
        'http://cdn.decoist.com/wp-content/uploads/2016/12/Modern-kitchen-island-in-gray.jpg',
      source_page_url:
        'http://www.decoist.com/kitchen-island-cabinets-storage/',
      width: 2000,
      height: 1500,
      title:
        'Storage Solutions: Trendy Kitchen Islands with Space-Savvy Cabinets',
      thumbnail:
        'https://tse4.mm.bing.net/th?id=OIP.ZocgPR7GxEUlnmydsd7AwAHaFj&pid=Api',
      source_domain: 'cdn.decoist.com',
      source_page_domain: 'www.decoist.com',
      source: 'duckduckgo',
    },
    {
      source_url:
        'https://d31eqxppr3nlos.cloudfront.net/wp-content/uploads/2014/04/shutterstock_39799417.jpg',
      source_page_url: 'https://www.homestratosphere.com/kitchen-islands/',
      width: 1000,
      height: 1000,
      title: '64 Deluxe Custom Kitchen Island Designs (BEAUTIFUL)',
      thumbnail:
        'https://tse1.mm.bing.net/th?id=OIP.jpEcnz_ILUiuxG2IMCpHTQHaHa&pid=Api',
      source_domain: 'd31eqxppr3nlos.cloudfront.net',
      source_page_domain: 'www.homestratosphere.com',
      source: 'duckduckgo',
    },
    {
      source_url:
        'https://www.homestratosphere.com/wp-content/uploads/2018/08/kitchen-island-photo2018-08-03-at-8.40.26-AM-35.jpg',
      source_page_url: 'https://www.homestratosphere.com/kitchen-island-ideas/',
      width: 1132,
      height: 1696,
      title:
        '90 Different Kitchen Island Ideas and Designs (Photos) - Home Stratosphere',
      thumbnail:
        'https://tse3.mm.bing.net/th?id=OIP.z76gffaxFtLMQyyFnidspgHaLG&pid=Api',
      source_domain: 'www.homestratosphere.com',
      source_page_domain: 'www.homestratosphere.com',
      source: 'duckduckgo',
    },
    {
      source_url:
        'https://pullcast.eu/blog/wp-content/uploads/2019/10/38-Amazing-Kitchen-Island-with-Built-in-Seating-Decorations-25.jpg',
      source_page_url:
        'https://www.pullcast.eu/blog/kitchen-island-designs-youll-want-for-yourself/',
      width: 1080,
      height: 1625,
      title: 'Kitchen Island Designs That Are Both Perfect And Practical',
      thumbnail:
        'https://tse2.mm.bing.net/th?id=OIP.B5tdzeuaNI-NQCNN6tk3IQHaLJ&pid=Api',
      source_domain: 'pullcast.eu',
      source_page_domain: 'www.pullcast.eu',
      source: 'duckduckgo',
    },
  ]

  return (
    <div className="rounded-lg bg-white shadow-lg">
      {/* Header */}
      <div className="bg-surface-50 sticky top-0 z-[99999] inline-flex w-full items-center justify-between rounded-t-lg border-b pe-3 shadow-sm">
        <div className="inline-flex items-center space-x-2 truncate p-4 text-lg">
          <i className="pi pi-images px-2 text-lg" />
          Upload Images
        </div>
        <div className="flex">
          <i
            className={`pi ${isMaximized ? 'pi-window-minimize' : 'pi-window-maximize'} cursor-pointer px-2 text-sm text-gray-700`}
            onClick={() => setIsMaximized(!isMaximized)}
          />
          <i className="pi pi-times cursor-pointer px-2 text-sm text-gray-700" />
        </div>
      </div>

      <div className="h-[360px] overflow-hidden p-2">
        <div className="mx-1 my-2 columns-6 gap-2 space-y-2">
          {imagesData.slice(0, 21).map((image, index) => (
            <div key={index} className="relative break-inside-avoid">
              <Image
                src={image.thumbnail}
                alt={image.title}
                width="100%"
                className="rounded-lg"
              />
              <div
                className={`absolute right-1.5 top-1.5 h-6 w-6 transform rounded-full ps-1.5 text-white transition-all duration-150 ease-out ${
                  selectedIndexes.includes(index)
                    ? 'scale-100 bg-blue-600 opacity-100'
                    : 'scale-0 opacity-0'
                }`}
              >
                <i className="pi pi-check text-xs" />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

'use client'
import React, { useState, useEffect } from 'react'
import { Panel } from 'primereact/panel'
import { Button } from 'primereact/button'

// Dummy data
const dummyPosts = [
  {
    keyword: 'modern kitchen island',
    title:
      'How to Choose the Perfect Countertop for Your Modern Kitchen Island',
    imageCount: 5,
    wordCount: 1050,
  },
  {
    keyword: 'minimalist living room',
    title: '10 Essential Tips for Designing a Minimalist Living Room',
    imageCount: 7,
    wordCount: 1200,
  },
  {
    keyword: 'small bathroom ideas',
    title: 'Creative Storage Solutions for Small Bathroom Spaces',
    imageCount: 4,
    wordCount: 950,
  },
  {
    keyword: 'outdoor patio design',
    title: 'Transform Your Backyard: Modern Patio Design Ideas',
    imageCount: 6,
    wordCount: 1150,
  },
]

export function DemoPosting() {
  const [isMaximized, setIsMaximized] = useState(false)
  const [currentPost, setCurrentPost] = useState(dummyPosts[0])
  const [status, setStatus] = useState({
    isCreating: true,
    isCreated: false,
    uploadProgress: 0,
    isUploading: false,
    isUploaded: false,
    isGenerating: false,
    isPublished: false,
    wordCount: 0,
  })

  const startProcess = () => {
    // Reset status
    setStatus({
      isCreating: true,
      isCreated: false,
      uploadProgress: 0,
      isUploading: false,
      isUploaded: false,
      isGenerating: false,
      isPublished: false,
      wordCount: 0,
    })

    // Randomly select a new post
    const randomPost = dummyPosts[Math.floor(Math.random() * dummyPosts.length)]
    setCurrentPost(randomPost)

    // Start the animation process
    setTimeout(() => {
      setStatus((prev) => ({
        ...prev,
        isCreating: false,
        isCreated: true,
        isUploading: true,
      }))
    }, 1000)

    const uploadInterval = setInterval(() => {
      setStatus((prev) => {
        if (prev.uploadProgress >= randomPost.imageCount) {
          clearInterval(uploadInterval)
          return {
            ...prev,
            isUploading: false,
            isUploaded: true,
            isGenerating: true,
          }
        }
        return {
          ...prev,
          uploadProgress: prev.uploadProgress + 1,
        }
      })
    }, 800)

    // Add delay before publishing to show generating state
    setTimeout(
      () => {
        setStatus((prev) => ({
          ...prev,
          isGenerating: true,
        }))

        // Add another setTimeout for publishing
        setTimeout(() => {
          setStatus((prev) => ({
            ...prev,
            isGenerating: false,
            isPublished: true,
            wordCount: randomPost.wordCount,
          }))
        }, 3000) // Show generating state for 3 seconds
      },
      randomPost.imageCount * 800 + 1000,
    ) // Start after images upload
  }

  useEffect(() => {
    startProcess()
  }, [])

  const headerTemplate = (options) => {
    const className = `${options.className} flex justify-between bg-white rounded-none`

    return (
      <div className={className}>
        <div className="flex items-center gap-2">
          <h2 className="text-lg font-semibold text-gray-800">
            {currentPost.keyword}
          </h2>
        </div>
        <div>
          <div className="flex items-center gap-2">
            {status.uploadProgress > 0 && (
              <span className="inline-flex items-center rounded-md bg-blue-100 px-2 font-semibold text-blue-800">
                <i className="pi pi-images me-1" /> {status.uploadProgress}
              </span>
            )}
            {status.isPublished ? (
              <span className="rounded-md bg-blue-100 px-2">
                <i className="pi pi-check align-[-1px] font-semibold text-blue-800" />
              </span>
            ) : (
              <span className="rounded-md px-2">
                <i className="pi pi-spinner pi-spin align-[-1px] font-semibold text-blue-800" />
              </span>
            )}
          </div>
        </div>
      </div>
    )
  }

  const fooerTemplate = (options) => {
    const className = `${options.className} flex flex-wrap items-center justify-between gap-3`

    return (
      <div className={className}>
        <div className="align-items-center pointer-events-none flex gap-2">
          {status.isPublished && (
            <>
              <Button icon="pi pi-pencil" text severity="secondary" />
              <Button icon="pi pi-external-link" text severity="secondary" />
            </>
          )}
        </div>
        <Button
          label="Refresh"
          severity="secondary"
          outlined
          onClick={startProcess}
          disabled={!status.isPublished}
        />
      </div>
    )
  }

  return (
    <div className="font-mono text-sm">
      <div className="relative space-y-2 bg-[#0a0b11] p-6 text-[13px] shadow-inner">
        {/* Matrix-style scanline effect */}
        <div className="animate-scan pointer-events-none absolute inset-0 bg-gradient-to-b from-transparent via-blue-500/[0.03] to-transparent bg-[length:100%_3px]" />

        {/* Command line header with enhanced styling */}
        <div className="flex items-center gap-2 font-light">
          <span className="text-emerald-500">⬢</span>
          <span className="text-emerald-500">generate</span>
          <span className="text-nowrap text-gray-500">keyword:</span>
          <span className="rounded border border-blue-900/50 bg-blue-950/50 px-2 py-0.5 text-blue-400">
            {currentPost.keyword}
          </span>
        </div>

        {/* Status outputs with enhanced styling */}
        <div className="space-y-2 pt-2 text-xs">
          {status.isCreating && (
            <div className="flex items-center gap-2 bg-blue-950/20 p-2 text-gray-400">
              <span className="inline-block h-2 w-2 animate-pulse rounded-full bg-blue-500/50 shadow-[0_0_8px_rgba(59,130,246,0.5)]" />
              <span className="text-blue-500/80">[SYSTEM]</span>
              <span className="text-gray-500">Generating Post Title...</span>
            </div>
          )}

          {status.isCreated && (
            <div className="flex items-center gap-2 rounded bg-emerald-500/5 px-2 py-1 text-gray-400">
              <span className="text-emerald-500">
                <i className="pi pi-check"></i>
              </span>
              <span className="text-emerald-500/80">[CREATE]</span>
              <span className="text-blue-400/90">{currentPost.title}</span>
            </div>
          )}

          {status.isUploading && (
            <div className="space-y-1.5 rounded bg-blue-950/20 p-2">
              <div className="flex items-center gap-2 text-gray-400">
                <span className="inline-block h-2 w-2 animate-pulse rounded-full bg-blue-500/50 shadow-[0_0_8px_rgba(59,130,246,0.5)]" />
                <span className="text-blue-500/80">[UPLOAD]</span>
                <span className="text-gray-400">
                  Uploading Images: [{status.uploadProgress}/
                  {currentPost.imageCount}]
                </span>
              </div>
              <div className="flex gap-1 rounded bg-gray-950/50 p-0.5">
                {[...Array(currentPost.imageCount)].map((_, i) => (
                  <div
                    key={i}
                    className={`h-1 flex-1 rounded-sm transition-all duration-300 ${
                      i < status.uploadProgress
                        ? 'bg-gradient-to-r from-blue-500 to-blue-400 shadow-[0_0_8px_rgba(59,130,246,0.5)]'
                        : 'bg-gray-800'
                    }`}
                  />
                ))}
              </div>
            </div>
          )}

          {status.isUploaded && (
            <div className="flex items-center gap-2 rounded bg-emerald-500/5 px-2 py-1 text-gray-400">
              <span className="text-emerald-500">
                <i className="pi pi-check"></i>
              </span>
              <span className="text-emerald-500/80">[UPLOAD]</span>
              <span className="text-gray-400">Images Uploaded</span>
            </div>
          )}

          {status.isGenerating && !status.isPublished && (
            <div className="flex items-center gap-2 bg-blue-950/20 p-2 text-gray-400">
              <span className="inline-block h-2 w-2 animate-pulse rounded-full bg-blue-500/50 shadow-[0_0_8px_rgba(59,130,246,0.5)]" />
              <span className="text-blue-500/80">[GENERATE]</span>
              <span className="text-gray-400">Generating Post Content...</span>
            </div>
          )}

          {status.isPublished && (
            <>
              <div className="flex items-center gap-2 rounded bg-emerald-500/5 px-2 py-1 text-gray-400">
                <span className="text-emerald-500">
                  <i className="pi pi-check"></i>
                </span>
                <span className="text-emerald-500/80">[PUBLISH]</span>
                <span className="text-gray-400">
                  Post Published: {status.wordCount.toLocaleString()} words
                </span>
              </div>
              <div className="mt-4 flex items-center justify-end gap-1 border-t border-blue-950/50 pt-4 text-gray-500">
                <span className="animate-pulse text-blue-500">▶</span>
                <button
                  onClick={startProcess}
                  className="text-blue-400 transition-colors hover:text-blue-300"
                >
                  Generate More
                </button>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Enhanced terminal footer */}
      <div className="flex justify-between border-t border-blue-950/30 bg-[#0a0b11]/90 px-4 py-2 text-[10px]">
        <div className="flex items-center gap-2">
          <span className="h-1.5 w-1.5 rounded-full bg-emerald-500 shadow-[0_0_8px_rgba(16,185,129,0.5)]" />
          <span className="text-emerald-500/70">SYSTEM READY</span>
        </div>
        <div className="flex items-center gap-3 text-blue-500/50">
          <span className="h-1 w-1 rounded-full bg-blue-500/50" />
          <span>v1.0.0</span>
        </div>
      </div>
    </div>
  )
}

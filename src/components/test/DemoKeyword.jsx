'use client'

import { DataTable } from 'primereact/datatable'
import { Column } from 'primereact/column'
import { Button } from 'primereact/button'

export function DemoKeyword() {
  // Dummy data
  const data = [
    {
      keywords: 'contemporary leather sofa recliner',
      postTitle:
        'Unwind in Style: The Ultimate Guide to Contemporary Leather Sofa Recliners for 2023',
      publishDate: '2024-12-14',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'contemporary modular sofa uk',
      postTitle:
        'Unveiling the Ultimate Guide to Contemporary Modular Sofas in the UK: Transform Your Living Space Today!',
      publishDate: '2024-12-14',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'contemporary leather sofa bed',
      postTitle:
        'Unveiling the Comfort and Style: Your Guide to Choosing the Perfect Contemporary Leather Sofa Bed',
      publishDate: '2024-12-14',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'contemporary leather sofa sale',
      postTitle:
        'Unveiling the Art of Comfort: Rediscovering the Timeless Appeal of Leather Sofas',
      publishDate: '2024-12-14',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'contemporary console table',
      postTitle:
        'Unlocking the Secrets of Console Table Styling for Every Home',
      publishDate: '2025-01-08',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'contemporary sofa bed',
      postTitle:
        'Unlock Modern Comfort: Discover the Best Contemporary Sofa Beds for Every Space',
      publishDate: '2024-12-14',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'mid century modern coffee table canada',
      postTitle:
        'Understanding Wood Types and Construction in Canadian Mid Century Modern Coffee Tables',
      publishDate: '2024-12-17',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'modern sofa uk',
      postTitle:
        'Understanding Modern Sofa Construction What Makes a Quality Piece',
      publishDate: '2025-01-08',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'contemporary furniture uk online',
      postTitle:
        'Transform Your UK Home with Contemporary Furniture Found Online',
      publishDate: '2024-12-15',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'contemporary furniture uk discount code',
      postTitle:
        'Transform Your Space While Saving Money on Contemporary Furniture UK',
      publishDate: '2024-12-15',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'modern dining table set',
      postTitle:
        'The Complete Guide to Choosing Modern Dining Table Sets for Your Home',
      publishDate: '2024-12-18',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'scandinavian furniture design',
      postTitle:
        'Exploring the Minimalist Beauty of Scandinavian Furniture Design',
      publishDate: '2024-12-19',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'industrial style furniture',
      postTitle:
        'Industrial Style Furniture: Bringing Urban Edge to Your Living Space',
      publishDate: '2024-12-20',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'velvet accent chair uk',
      postTitle:
        'Luxury and Comfort: Your Guide to Selecting the Perfect Velvet Accent Chair',
      publishDate: '2024-12-21',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'modern bedroom furniture sets',
      postTitle:
        'Creating Your Dream Bedroom with Modern Furniture Sets: A Complete Guide',
      publishDate: '2024-12-22',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'contemporary outdoor furniture',
      postTitle:
        'Designing Your Perfect Outdoor Space with Contemporary Furniture',
      publishDate: '2024-12-23',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'minimalist home office desk',
      postTitle:
        'Maximizing Productivity with Minimalist Home Office Furniture Solutions',
      publishDate: '2024-12-24',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'contemporary wall art decor',
      postTitle:
        'Elevating Your Space: The Ultimate Guide to Contemporary Wall Art',
      publishDate: '2024-12-25',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'modern storage solutions',
      postTitle:
        'Smart Storage Solutions for the Modern Home: A Comprehensive Guide',
      publishDate: '2024-12-26',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'luxury living room furniture',
      postTitle:
        'Creating a Luxurious Living Space: High-End Furniture Selection Guide',
      publishDate: '2024-12-27',
      images: 5,
      status: 'posted',
    },
  ]

  const actionTemplate = () => {
    return (
      <div className="pointer-events-none flex">
        <Button icon="pi pi-send" text severity="secondary" />
        <Button icon="pi pi-refresh" text severity="secondary" />
        <Button icon="pi pi-trash" text severity="danger" />
      </div>
    )
  }

  const postTitleTemplate = (rowData) => {
    return (
      <div>
        <div className="font-semibold text-blue-900">{rowData.postTitle}</div>
        <div className="text-sm text-blue-600">
          <a href="#" className="mr-2">
            Edit
          </a>{' '}
          |
          <a href="#" className="ml-2">
            View
          </a>
        </div>
      </div>
    )
  }

  return (
    <div>
      <div className="flex items-center justify-between border-gray-200 bg-white px-2 py-2">
        <div className="flex min-w-0 items-center space-x-4 py-4">
          <h1 className="ms-5 flex items-center truncate text-2xl font-semibold tracking-tight text-gray-900">
            Keywords Manager
          </h1>
          <div className="flex space-x-2">
            <Button
              label="New"
              icon="pi pi-plus"
              severity="info"
              className="bg-blue-600 px-3 py-2 text-white hover:bg-blue-800"
            />
            <Button
              label="Generate"
              icon="pi pi-bolt"
              severity="info"
              className="bg-blue-600 px-3 py-2 text-white hover:bg-blue-800"
            />
          </div>
        </div>
      </div>

      <DataTable
        value={data}
        showGridlines
        stripedRows
        paginator
        removableSort
        size="normal"
        rows={7}
      >
        <Column
          pt={{
            headerTitle: {
              className: 'py-2 ps-2',
            },
            bodyCell: {
              className: '!ps-6',
            },
          }}
          field="keywords"
          header="Keywords"
          sortable
        />
        <Column
          field="postTitle"
          header="Post Title"
          sortable
          body={postTitleTemplate}
        />
        <Column
          pt={{
            headerContent: {
              className: 'justify-center',
            },
            headerTitle: {
              className: 'py-2 ps-2',
            },
            bodyCell: {
              className: '!px-4 text-center text-nowrap',
            },
          }}
          field="publishDate"
          header="Publish Date"
          sortable
        />
        <Column
          pt={{
            headerContent: {
              className: 'justify-center',
            },
            headerTitle: {
              className: 'py-2 ps-2',
            },
            bodyCell: {
              className: '!px-4 text-center',
            },
          }}
          field="images"
          header="Images"
          sortable
        />
        <Column
          pt={{
            headerContent: {
              className: 'justify-center',
            },
            headerTitle: {
              className: 'py-2 ps-2',
            },
            bodyCell: {
              className: '!px-4 text-center',
            },
          }}
          field="status"
          header="Status"
          sortable
        />
        <Column
          pt={{
            headerContent: {
              className: 'justify-center',
            },
            headerTitle: {
              className: 'py-2 ps-2',
            },
            bodyCell: {
              className: '!px-4 text-center',
            },
          }}
          header="Actions"
          body={actionTemplate}
        />
      </DataTable>
    </div>
  )
}

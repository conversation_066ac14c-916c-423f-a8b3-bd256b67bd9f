'use client'

import { DataTable } from 'primereact/datatable'
import { Column } from 'primereact/column'
import { Button } from 'primereact/button'

export function DemoKeyword() {
  // Dummy data
  const data = [
    {
      keywords: 'contemporary leather sofa recliner',
      postTitle:
        'Unwind in Style: The Ultimate Guide to Contemporary Leather Sofa Recliners for 2023',
      publishDate: '2024-12-14',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'contemporary modular sofa uk',
      postTitle:
        'Unveiling the Ultimate Guide to Contemporary Modular Sofas in the UK: Transform Your Living Space Today!',
      publishDate: '2024-12-14',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'contemporary leather sofa bed',
      postTitle:
        'Unveiling the Comfort and Style: Your Guide to Choosing the Perfect Contemporary Leather Sofa Bed',
      publishDate: '2024-12-14',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'contemporary leather sofa sale',
      postTitle:
        'Unveiling the Art of Comfort: Rediscovering the Timeless Appeal of Leather Sofas',
      publishDate: '2024-12-14',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'contemporary console table',
      postTitle:
        'Unlocking the Secrets of Console Table Styling for Every Home',
      publishDate: '2025-01-08',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'contemporary sofa bed',
      postTitle:
        'Unlock Modern Comfort: Discover the Best Contemporary Sofa Beds for Every Space',
      publishDate: '2024-12-14',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'mid century modern coffee table canada',
      postTitle:
        'Understanding Wood Types and Construction in Canadian Mid Century Modern Coffee Tables',
      publishDate: '2024-12-17',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'modern sofa uk',
      postTitle:
        'Understanding Modern Sofa Construction What Makes a Quality Piece',
      publishDate: '2025-01-08',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'contemporary furniture uk online',
      postTitle:
        'Transform Your UK Home with Contemporary Furniture Found Online',
      publishDate: '2024-12-15',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'contemporary furniture uk discount code',
      postTitle:
        'Transform Your Space While Saving Money on Contemporary Furniture UK',
      publishDate: '2024-12-15',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'modern dining table set',
      postTitle:
        'The Complete Guide to Choosing Modern Dining Table Sets for Your Home',
      publishDate: '2024-12-18',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'scandinavian furniture design',
      postTitle:
        'Exploring the Minimalist Beauty of Scandinavian Furniture Design',
      publishDate: '2024-12-19',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'industrial style furniture',
      postTitle:
        'Industrial Style Furniture: Bringing Urban Edge to Your Living Space',
      publishDate: '2024-12-20',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'velvet accent chair uk',
      postTitle:
        'Luxury and Comfort: Your Guide to Selecting the Perfect Velvet Accent Chair',
      publishDate: '2024-12-21',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'modern bedroom furniture sets',
      postTitle:
        'Creating Your Dream Bedroom with Modern Furniture Sets: A Complete Guide',
      publishDate: '2024-12-22',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'contemporary outdoor furniture',
      postTitle:
        'Designing Your Perfect Outdoor Space with Contemporary Furniture',
      publishDate: '2024-12-23',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'minimalist home office desk',
      postTitle:
        'Maximizing Productivity with Minimalist Home Office Furniture Solutions',
      publishDate: '2024-12-24',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'contemporary wall art decor',
      postTitle:
        'Elevating Your Space: The Ultimate Guide to Contemporary Wall Art',
      publishDate: '2024-12-25',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'modern storage solutions',
      postTitle:
        'Smart Storage Solutions for the Modern Home: A Comprehensive Guide',
      publishDate: '2024-12-26',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'luxury living room furniture',
      postTitle:
        'Creating a Luxurious Living Space: High-End Furniture Selection Guide',
      publishDate: '2024-12-27',
      images: 5,
      status: 'posted',
    },
  ]

  const actionTemplate = () => {
    return (
      <div className="flex justify-center gap-1">
        {[
          { icon: 'pi pi-send', label: 'publish' },
          { icon: 'pi pi-refresh', label: 'regenerate' },
          { icon: 'pi pi-trash', label: 'delete', danger: true },
        ].map((action) => (
          <button
            key={action.label}
            className={`group relative rounded border p-1.5 transition-all ${
              action.danger
                ? 'border-red-900/30 text-red-400 hover:border-red-500/30 hover:text-red-300'
                : 'border-gray-800 text-gray-500 hover:border-blue-500/30 hover:text-blue-400'
            }`}
          >
            <i className={`${action.icon} text-sm`} />
          </button>
        ))}
      </div>
    )
  }

  const postTitleTemplate = (rowData) => {
    return (
      <div className="space-y-1">
        <div className="font-mono text-sm text-gray-300">
          {rowData.postTitle}
        </div>
        <div className="flex items-center gap-2 font-mono text-xs">
          <a href="#" className="text-blue-400 hover:text-blue-300">
            Edit
          </a>
          <span className="text-gray-700">|</span>
          <a href="#" className="text-blue-400 hover:text-blue-300">
            View
          </a>
        </div>
      </div>
    )
  }

  const statusTemplate = (rowData) => {
    return (
      <div className="flex justify-center">
        <div className="flex items-center gap-1.5 rounded-full border border-emerald-500/20 bg-emerald-500/5 px-2 py-0.5 font-mono text-xs text-emerald-400">
          <span className="h-1.5 w-1.5 animate-pulse rounded-full bg-emerald-500" />
          {rowData.status}
        </div>
      </div>
    )
  }

  const dateTemplate = (rowData) => {
    return (
      <div className="font-mono text-xs text-gray-400">
        {new Date(rowData.publishDate).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
        })}
      </div>
    )
  }

  const imagesTemplate = (rowData) => {
    return (
      <div className="text-center font-mono text-xs">
        <span className="text-blue-400">{rowData.images}</span>
        <span className="text-gray-500"> images</span>
      </div>
    )
  }

  const tableStyles = {
    css: `
      .p-datatable {
        background: transparent;
        font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
      }
      .p-datatable .p-datatable-header {
        background: transparent;
        border: none;
        padding: 1rem 1.5rem;
      }
      .p-datatable .p-datatable-thead > tr > th {
        background: transparent;
        border: none;
        border-bottom: 1px solid #1f2937;
        color: #9ca3af;
        font-weight: 500;
        padding: 0.75rem 1rem;
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
      }
      .p-datatable .p-datatable-tbody > tr {
        background: transparent;
        transition: background-color 0.2s;
      }
      .p-datatable .p-datatable-tbody > tr > td {
        border: none;
        border-bottom: 1px solid #1f2937;
        padding: 0.75rem 1rem;
        color: #d1d5db;
      }
      .p-datatable .p-datatable-tbody > tr:hover {
        background: rgba(59, 130, 246, 0.05);
      }
      .p-paginator {
        background: transparent;
        border: none;
        padding: 1rem;
      }
      .p-paginator .p-paginator-element {
        color: #9ca3af;
        min-width: 2rem;
        height: 2rem;
        margin: 0 0.125rem;
        font-size: 0.75rem;
      }
      .p-paginator .p-paginator-element.p-highlight {
        background: #1f2937;
        border-color: #374151;
        color: #d1d5db;
      }
      .p-paginator .p-paginator-element:not(.p-highlight):hover {
        background: rgba(59, 130, 246, 0.05);
        border-color: #374151;
        color: #d1d5db;
      }

      .p-paginator .p-paginator-pages .p-paginator-page:focus {
        box-shadow: none;
      }

      .p-paginator .p-paginator-first:not(.p-disabled):hover,
      .p-paginator .p-paginator-prev:not(.p-disabled):hover,
      .p-paginator .p-paginator-next:not(.p-disabled):hover,
      .p-paginator .p-paginator-last:not(.p-disabled):hover {
        background: rgba(59, 130, 246, 0.05);
        border-color: #374151;
        color: #d1d5db;
      }

      .p-paginator .p-paginator-first.p-disabled,
      .p-paginator .p-paginator-prev.p-disabled,
      .p-paginator .p-paginator-next.p-disabled,
      .p-paginator .p-paginator-last.p-disabled {
        color: #4b5563;
    `,
  }

  return (
    <div className="relative overflow-hidden rounded-sm border border-gray-800 bg-[#0c0d12]">
      <style>{tableStyles.css}</style>

      {/* Enhanced Header */}
      <div className="border-b border-gray-800 bg-[#0a0b11]/80 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <h2 className="font-mono text-sm font-medium text-gray-300">
              Keywords Manager
            </h2>
            <span className="hidden rounded-full border border-blue-500/20 bg-blue-500/5 px-2 py-0.5 font-mono text-xs text-blue-400 md:block">
              {data.length} entries
            </span>
          </div>

          <div className="flex gap-2">
            <button className="group relative overflow-hidden rounded border border-blue-500/30 bg-blue-500/5 px-3 py-1.5 font-mono text-xs text-blue-400 hover:bg-blue-500/10">
              <span className="flex items-center gap-2">
                <i className="pi pi-plus text-[10px]" />
                <span className="hidden sm:block">Add New</span>
              </span>
            </button>
            <button className="group relative rounded border border-blue-500/30 bg-blue-500/5 px-3 py-1.5 font-mono text-xs text-blue-400 hover:bg-blue-500/10">
              <span className="flex items-center gap-2">
                <i className="pi pi-bolt text-[10px]" />
                <span className="hidden sm:block">Generate</span>
              </span>
            </button>
          </div>
        </div>
      </div>

      {/* Enhanced Table */}
      <DataTable
        value={data}
        paginator
        rows={7}
        className="border-none"
        emptyMessage={
          <span className="font-mono text-sm text-gray-500">
            No keywords found
          </span>
        }
      >
        <Column
          field="keywords"
          header="Keyword"
          sortable
          body={(rowData) => (
            <div className="font-mono text-sm text-gray-400">
              {rowData.keywords}
            </div>
          )}
        />
        <Column
          field="postTitle"
          header="Generated Title"
          sortable
          body={postTitleTemplate}
        />
        <Column
          field="publishDate"
          header="Date"
          sortable
          body={dateTemplate}
        />
        <Column
          pt={{
            headerContent: {
              className: 'justify-center',
            },
          }}
          field="images"
          header="Images"
          sortable
          body={imagesTemplate}
        />
        <Column
          pt={{
            headerContent: {
              className: 'justify-center',
            },
          }}
          field="status"
          header="Status"
          sortable
          body={statusTemplate}
        />
        <Column
          pt={{
            headerContent: {
              className: 'justify-center',
            },
          }}
          header="Actions"
          body={actionTemplate}
        />
      </DataTable>

      {/* Enhanced Footer */}
      {/* <div className="border-t border-gray-800 bg-[#0a0b11]/80 px-4 py-2"> */}
      {/*   <div className="flex items-center justify-between font-mono text-xs text-gray-500"> */}
      {/*     <div className="flex items-center gap-2"> */}
      {/*       <span className="h-1.5 w-1.5 animate-pulse rounded-full bg-emerald-500" /> */}
      {/*       ai.engine: active */}
      {/*     </div> */}
      {/*     <span>last_generation: 5m ago</span> */}
      {/*   </div> */}
      {/* </div> */}
    </div>
  )
}

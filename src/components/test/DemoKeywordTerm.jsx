'use client'

import { DataTable } from 'primereact/datatable'
import { Column } from 'primereact/column'
import { Button } from 'primereact/button'

export function DemoKeyword() {
  // Dummy data
  const data = [
    {
      keywords: 'contemporary leather sofa recliner',
      postTitle:
        'Unwind in Style: The Ultimate Guide to Contemporary Leather Sofa Recliners for 2023',
      publishDate: '2024-12-14',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'contemporary modular sofa uk',
      postTitle:
        'Unveiling the Ultimate Guide to Contemporary Modular Sofas in the UK: Transform Your Living Space Today!',
      publishDate: '2024-12-14',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'contemporary leather sofa bed',
      postTitle:
        'Unveiling the Comfort and Style: Your Guide to Choosing the Perfect Contemporary Leather Sofa Bed',
      publishDate: '2024-12-14',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'contemporary leather sofa sale',
      postTitle:
        'Unveiling the Art of Comfort: Rediscovering the Timeless Appeal of Leather Sofas',
      publishDate: '2024-12-14',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'contemporary console table',
      postTitle:
        'Unlocking the Secrets of Console Table Styling for Every Home',
      publishDate: '2025-01-08',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'contemporary sofa bed',
      postTitle:
        'Unlock Modern Comfort: Discover the Best Contemporary Sofa Beds for Every Space',
      publishDate: '2024-12-14',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'mid century modern coffee table canada',
      postTitle:
        'Understanding Wood Types and Construction in Canadian Mid Century Modern Coffee Tables',
      publishDate: '2024-12-17',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'modern sofa uk',
      postTitle:
        'Understanding Modern Sofa Construction What Makes a Quality Piece',
      publishDate: '2025-01-08',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'contemporary furniture uk online',
      postTitle:
        'Transform Your UK Home with Contemporary Furniture Found Online',
      publishDate: '2024-12-15',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'contemporary furniture uk discount code',
      postTitle:
        'Transform Your Space While Saving Money on Contemporary Furniture UK',
      publishDate: '2024-12-15',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'modern dining table set',
      postTitle:
        'The Complete Guide to Choosing Modern Dining Table Sets for Your Home',
      publishDate: '2024-12-18',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'scandinavian furniture design',
      postTitle:
        'Exploring the Minimalist Beauty of Scandinavian Furniture Design',
      publishDate: '2024-12-19',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'industrial style furniture',
      postTitle:
        'Industrial Style Furniture: Bringing Urban Edge to Your Living Space',
      publishDate: '2024-12-20',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'velvet accent chair uk',
      postTitle:
        'Luxury and Comfort: Your Guide to Selecting the Perfect Velvet Accent Chair',
      publishDate: '2024-12-21',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'modern bedroom furniture sets',
      postTitle:
        'Creating Your Dream Bedroom with Modern Furniture Sets: A Complete Guide',
      publishDate: '2024-12-22',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'contemporary outdoor furniture',
      postTitle:
        'Designing Your Perfect Outdoor Space with Contemporary Furniture',
      publishDate: '2024-12-23',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'minimalist home office desk',
      postTitle:
        'Maximizing Productivity with Minimalist Home Office Furniture Solutions',
      publishDate: '2024-12-24',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'contemporary wall art decor',
      postTitle:
        'Elevating Your Space: The Ultimate Guide to Contemporary Wall Art',
      publishDate: '2024-12-25',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'modern storage solutions',
      postTitle:
        'Smart Storage Solutions for the Modern Home: A Comprehensive Guide',
      publishDate: '2024-12-26',
      images: 5,
      status: 'posted',
    },
    {
      keywords: 'luxury living room furniture',
      postTitle:
        'Creating a Luxurious Living Space: High-End Furniture Selection Guide',
      publishDate: '2024-12-27',
      images: 5,
      status: 'posted',
    },
  ]

  const actionTemplate = () => {
    return (
      <div className="flex justify-center gap-1">
        {[
          { icon: 'pi pi-send', label: 'publish' },
          { icon: 'pi pi-refresh', label: 'regenerate' },
          { icon: 'pi pi-trash', label: 'delete' },
        ].map((action) => (
          <button
            key={action.label}
            className="group relative rounded border border-gray-200 p-1.5 text-gray-400 hover:border-blue-500/30 hover:text-blue-500"
          >
            <i className={`${action.icon} text-sm`} />
            <div className="absolute -bottom-8 left-1/2 hidden -translate-x-1/2 rounded bg-gray-900 px-2 py-1 font-mono text-[10px] text-white group-hover:block">
              {action.label}
            </div>
          </button>
        ))}
      </div>
    )
  }

  const postTitleTemplate = (rowData) => {
    return (
      <div className="space-y-1">
        <div className="font-mono text-sm text-gray-900">
          {rowData.postTitle}
        </div>
        <div className="flex items-center gap-2 font-mono text-xs">
          <a href="#" className="text-blue-500 hover:text-blue-600">
            edit.post
          </a>
          <span className="text-gray-300">|</span>
          <a href="#" className="text-blue-500 hover:text-blue-600">
            view.post
          </a>
        </div>
      </div>
    )
  }

  const statusTemplate = (rowData) => {
    return (
      <div className="flex justify-center">
        <div className="flex items-center gap-1.5 rounded-full border border-emerald-500/30 bg-emerald-500/5 px-2 py-0.5 font-mono text-xs text-emerald-600">
          <span className="h-1.5 w-1.5 animate-pulse rounded-full bg-emerald-500" />
          {rowData.status}
        </div>
      </div>
    )
  }

  const dateTemplate = (rowData) => {
    return (
      <div className="font-mono text-xs text-gray-600">
        {new Date(rowData.publishDate).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
        })}
      </div>
    )
  }

  const imagesTemplate = (rowData) => {
    return (
      <div className="font-mono text-xs">
        <span className="text-blue-500">{rowData.images}</span>
        <span className="text-gray-500"> files</span>
      </div>
    )
  }

  return (
    <div className="relative overflow-hidden rounded-sm border border-gray-200 bg-white">
      {/* Enhanced Header */}
      <div className="border-b border-gray-200 bg-gray-50/80 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <span className="font-mono text-xs text-gray-500">{'>'}</span>
            <h2 className="font-mono text-sm font-medium text-gray-900">
              keywords.manager
            </h2>
            <span className="rounded-full border border-blue-500/20 bg-blue-500/5 px-2 py-0.5 font-mono text-xs text-blue-600">
              {data.length} entries
            </span>
          </div>

          <div className="flex gap-2">
            <button className="group relative overflow-hidden rounded border border-blue-500/30 bg-blue-500/5 px-3 py-1.5 font-mono text-xs text-blue-600 hover:bg-blue-500/10">
              <span className="flex items-center gap-2">
                <i className="pi pi-plus text-[10px]" />
                new.keyword
              </span>
            </button>
            <button className="group relative overflow-hidden rounded border border-blue-500/30 bg-blue-500/5 px-3 py-1.5 font-mono text-xs text-blue-600 hover:bg-blue-500/10">
              <span className="flex items-center gap-2">
                <i className="pi pi-bolt text-[10px]" />
                auto.generate
              </span>
            </button>
          </div>
        </div>
      </div>

      {/* Enhanced Table */}
      <DataTable
        value={data}
        paginator
        rows={7}
        className="border-none"
        emptyMessage={
          <span className="font-mono text-sm text-gray-500">
            No keywords found
          </span>
        }
      >
        <Column
          field="keywords"
          header="Keyword"
          sortable
          body={(rowData) => (
            <div className="font-mono text-sm text-gray-600">
              {rowData.keywords}
            </div>
          )}
        />
        <Column
          field="postTitle"
          header="Generated Title"
          sortable
          body={postTitleTemplate}
        />
        <Column
          field="publishDate"
          header="Date"
          sortable
          body={dateTemplate}
        />
        <Column field="images" header="Assets" sortable body={imagesTemplate} />
        <Column field="status" header="Status" sortable body={statusTemplate} />
        <Column header="Actions" body={actionTemplate} />
      </DataTable>

      {/* Enhanced Footer */}
      <div className="border-t border-gray-200 bg-gray-50/80 px-4 py-2">
        <div className="flex items-center justify-between font-mono text-xs text-gray-500">
          <div className="flex items-center gap-2">
            <span className="h-1.5 w-1.5 animate-pulse rounded-full bg-emerald-500" />
            ai.engine: active
          </div>
          <span>last_generation: 5m ago</span>
        </div>
      </div>
    </div>
  )
}

'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { DemoProject } from '@/components/DemoProjectTermDark'
import { DemoKeyword } from '@/components/DemoKeywordTermDark'
import { DemoAttachment } from '@/components/DemoAttachmentTermDark'
import { DemoTemplate } from '@/components/DemoTemplateTermDark'

const features = [
  {
    title: 'Projects',
    tagline: 'Organize & Automate',
    description:
      'Manage your content projects efficiently by organizing keywords in one place.',
    demo: <DemoProject />,
    icon: 'pi pi-folder',
    color: 'from-blue-600 to-cyan-600',
    stats: ['Projects', 'Custom Rules', 'Automation'],
  },
  {
    title: 'Keywords',
    tagline: 'Generate & Post',
    description: 'Centralize your target keywords for content generation.',
    demo: <DemoKeyword />,
    icon: 'pi pi-key',
    color: 'from-violet-600 to-purple-600',
    stats: ['Keywords', 'AI-Powered', 'Create Post', 'Update Post'],
  },
  {
    title: 'Images',
    tagline: 'Grab & Upload',
    description:
      'Control how images are sourced, processed, and integrated into your posts.',
    demo: <DemoAttachment />,
    icon: 'pi pi-image',
    color: 'from-orange-600 to-pink-600',
    stats: ['Auto-Optimize', 'Bulk Upload', 'Automation'],
  },
  {
    title: 'Templates',
    tagline: 'Structure & Scale',
    description: 'A flexible template management system that guides the AI.',
    demo: <DemoTemplate />,
    icon: 'pi pi-file',
    color: 'from-green-600 to-emerald-600',
    stats: ['Templates', 'Custom Rules', 'Post', 'Images'],
  },
]

export function PrimaryFeatures() {
  const [activeTab, setActiveTab] = useState(0)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  return (
    <section className="relative overflow-hidden bg-gradient-to-b from-gray-950 via-[#111318] to-[#151822] py-24 lg:py-32">
      {/* Subtle Background Pattern */}
      <div className="absolute inset-0 bg-[linear-gradient(to_right,#6366f108_1px,transparent_1px),linear-gradient(to_bottom,#6366f108_1px,transparent_1px)] bg-[size:24px_24px]" />

      {/* Ambient Background Effects */}
      <motion.div
        className="absolute left-0 top-0 -z-10 h-[500px] w-[500px] rounded-full blur-[120px]"
        animate={{
          background: `linear-gradient(${features[activeTab].color})`,
          opacity: [0.03, 0.05, 0.03],
        }}
        transition={{ duration: 3, repeat: Infinity }}
      />

      <div className="relative mx-auto max-w-7xl px-6">
        {/* Enhanced Header */}
        <div className="mx-auto max-w-2xl text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="inline-flex items-center gap-2 rounded-full bg-blue-500/10 px-4 py-2 font-mono text-sm text-blue-400 shadow-sm"
          >
            <span className="h-2 w-2 animate-pulse rounded-full bg-blue-500" />
            Powerful Features
          </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="mt-8 font-mono text-4xl font-bold text-gray-100 sm:text-5xl"
          >
            The{' '}
            <span className="bg-gradient-to-r from-blue-400 to-indigo-400 bg-clip-text text-transparent">
              Superhuman
            </span>{' '}
            Way to Automate Content
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="mt-4 font-mono text-lg text-gray-400"
          >
            With great content power comes zero responsibility.
          </motion.p>
        </div>

        {/* Feature Navigation */}
        <div className="mt-16 lg:mt-20">
          <div className="grid gap-4 lg:grid-cols-4">
            {features.map((feature, index) => (
              <motion.button
                key={feature.title}
                onClick={() => setActiveTab(index)}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className={`group relative overflow-hidden rounded-2xl border bg-[#111318]/80 p-6 shadow-sm transition-all hover:shadow-md ${
                  activeTab === index
                    ? 'border-blue-500/20 ring-1 ring-blue-500/10'
                    : 'border-gray-800 hover:border-blue-500/20'
                }`}
              >
                {/* Feature Card Content */}
                <div className="relative z-10 space-y-4">
                  <div className="flex items-center gap-3">
                    <div
                      className={`rounded-xl bg-gradient-to-br ${feature.color} p-2.5 text-white shadow-sm`}
                    >
                      <i className={`${feature.icon} text-lg`} />
                    </div>
                    <div className="text-left">
                      <h3 className="font-mono text-lg font-medium text-gray-200">
                        {feature.title}
                      </h3>
                      <p className="text-sm text-blue-400">{feature.tagline}</p>
                    </div>
                  </div>

                  <p className="line-clamp-2 text-left font-mono text-sm text-gray-400">
                    {feature.description}
                  </p>

                  <div className="flex flex-wrap gap-2">
                    {feature.stats.map((stat, i) => (
                      <span
                        key={i}
                        className="rounded-full bg-blue-500/10 px-3 py-1 text-xs font-medium text-blue-400"
                      >
                        {stat}
                      </span>
                    ))}
                  </div>
                </div>

                {activeTab === index && (
                  <div className="absolute inset-x-0 bottom-0 h-0.5 bg-gradient-to-r from-blue-600 to-indigo-600" />
                )}
              </motion.button>
            ))}
          </div>

          {/* Demo Display */}
          <motion.div
            layout
            className="mt-8 overflow-hidden rounded-2xl border border-gray-800 bg-[#111318]/80 shadow-sm"
          >
            <AnimatePresence mode="wait">
              <motion.div
                key={activeTab}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.2 }}
              >
                {features[activeTab].demo}
              </motion.div>
            </AnimatePresence>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

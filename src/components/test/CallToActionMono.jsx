import { Container } from '@/components/Container'

export function CallToAction() {
  return (
    <section className="relative overflow-hidden bg-gray-900 py-24 sm:py-32">
      {/* Background effects */}
      <div className="absolute inset-0 bg-[linear-gradient(to_right,#8882_1px,transparent_1px),linear-gradient(to_bottom,#8882_1px,transparent_1px)] bg-[size:24px_24px] opacity-50" />

      {/* Gradient overlays */}
      <div className="absolute inset-0 bg-gradient-to-b from-blue-500/10 via-transparent to-transparent" />
      <div className="absolute right-0 top-0 h-96 w-96 bg-blue-500/20 blur-[128px]" />
      <div className="absolute bottom-0 left-0 h-96 w-96 bg-blue-500/10 blur-[128px]" />

      <Container className="relative">
        <div className="mx-auto max-w-2xl">
          {/* Content wrapper */}
          <div className="relative rounded-2xl bg-white/5 p-12 backdrop-blur-sm">
            {/* Corner accents */}
            <div className="absolute left-0 top-0 h-px w-24 bg-gradient-to-r from-transparent via-blue-500 to-transparent" />
            <div className="absolute left-0 top-0 h-24 w-px bg-gradient-to-b from-blue-500 to-transparent" />
            <div className="absolute right-0 top-0 h-px w-24 bg-gradient-to-l from-transparent via-blue-500 to-transparent" />
            <div className="absolute right-0 top-0 h-24 w-px bg-gradient-to-b from-blue-500 to-transparent" />

            {/* Text content */}
            <div className="text-center">
              <h2 className="text-3xl font-medium tracking-tight text-white sm:text-4xl">
                Ready to Get Started?
              </h2>
              <p className="mt-4 text-lg text-gray-300">
                Master content automation at{' '}
                <span className="bg-gradient-to-r from-blue-400 to-blue-200 bg-clip-text text-transparent">
                  superhuman speed
                </span>
              </p>

              {/* CTA buttons */}
              <div className="mt-10 flex flex-col items-center gap-4 sm:flex-row sm:justify-center">
                <a
                  href="/register"
                  className="group relative w-full overflow-hidden rounded-lg bg-blue-500 px-8 py-3 text-center text-sm font-medium text-white transition-all hover:bg-blue-400 sm:w-auto"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-blue-500 opacity-0 transition-opacity group-hover:opacity-100" />
                  <span className="relative flex items-center justify-center gap-2">
                    Download for Free
                    <span className="transition-transform group-hover:translate-x-1">
                      →
                    </span>
                  </span>
                </a>
                <a
                  href="#features"
                  className="w-full text-sm font-medium text-gray-300 transition-colors hover:text-white sm:w-auto"
                >
                  Learn more
                </a>
              </div>
            </div>
          </div>

          {/* Optional: Additional info */}
          <div className="mt-8 flex justify-center gap-8 text-center">
            {[
              ['No credit card required', 'pi pi-credit-card'],
              ['14-day free trial', 'pi pi-clock'],
              ['Cancel anytime', 'pi pi-times'],
            ].map(([text, icon]) => (
              <div
                key={text}
                className="flex items-center gap-2 text-sm text-gray-400"
              >
                <i className={`${icon} text-blue-500`} />
                {text}
              </div>
            ))}
          </div>
        </div>
      </Container>
    </section>
  )
}

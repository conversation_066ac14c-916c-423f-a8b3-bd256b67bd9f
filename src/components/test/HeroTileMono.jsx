'use client'

import { Logo } from '@/components/Logo'
import { DemoPosting } from '@/components/DemoPostingAnimation'

export function HeroTile() {
  return (
    <div className="relative overflow-hidden bg-gray-900">
      {/* Background effects */}
      <div className="absolute inset-0 bg-[linear-gradient(to_right_bottom,rgba(0,0,0,0.8),transparent_60%)]" />
      <div className="absolute inset-y-0 right-0 w-1/2 bg-gradient-to-l from-blue-500/10 via-blue-500/5 to-transparent" />

      <div className="relative mx-auto max-w-7xl px-6">
        <div className="flex min-h-[90vh] flex-col items-center justify-center gap-16 py-20 lg:flex-row lg:justify-between lg:py-32">
          {/* Left Content */}
          <div className="relative z-10 max-w-xl pt-10">
            {/* Small badge */}
            <div className="inline-flex items-center gap-2 rounded-full border border-gray-800 bg-gray-800/50 px-3 py-1 text-sm text-gray-300 backdrop-blur-sm">
              <span className="inline-block h-2 w-2 animate-pulse rounded-full bg-blue-500" />
              Introducing WallPress
            </div>

            <h1 className="mt-6 text-[clamp(2.5rem,5vw,4rem)] font-medium leading-tight text-white">
              WordPress{' '}
              <span className="relative">
                Superpowers
                <div className="absolute -bottom-2 left-0 h-4 w-full bg-blue-500/20 blur-sm" />
                <div className="absolute -bottom-1 left-0 h-[2px] w-full bg-blue-500" />
              </span>
            </h1>

            <p className="mt-8 text-xl leading-relaxed text-gray-300">
              <span className="mt-2 block bg-gradient-to-r from-blue-200 to-blue-400 bg-clip-text text-transparent">
                Advanced content automation meets WordPress simplicity. Discover
                what your WordPress site can achieve with next-generation
                content publishing.
              </span>
            </p>

            <div className="mt-10 flex items-center gap-6">
              <a
                href="#"
                className="group relative overflow-hidden rounded-lg bg-blue-500 px-8 py-3 text-sm font-medium text-white transition-all hover:bg-blue-400"
              >
                <div className="absolute inset-0 flex items-center justify-center bg-blue-600 opacity-0 transition-opacity group-hover:opacity-100">
                  <span className="translate-y-10 transition-transform group-hover:translate-y-0">
                    Get started →
                  </span>
                </div>
                <span className="block transition-transform group-hover:-translate-y-10">
                  Get started
                </span>
              </a>
              <a
                href="#"
                className="text-sm font-medium text-gray-300 transition-colors hover:text-white"
              >
                Learn more
              </a>
            </div>

            {/* Stats */}
            <div className="mt-16 flex gap-12 border-t border-gray-800 pt-8">
              {[
                ['10K+', 'Active users'],
                ['500K+', 'Posts generated'],
              ].map(([stat, label]) => (
                <div key={stat} className="overflow-hidden">
                  <div className="text-2xl font-semibold text-white">
                    {stat}
                  </div>
                  <div className="mt-1 text-sm text-gray-400">{label}</div>
                </div>
              ))}
            </div>
          </div>

          {/* Right Content - Demo */}
          <div className="relative w-full max-w-2xl">
            {/* Glowing effect */}
            <div className="absolute -inset-x-20 -top-20 -z-10 opacity-50">
              <div className="absolute right-0 top-1/2 h-72 w-72 -translate-y-1/2 rounded-full bg-blue-500 blur-3xl" />
            </div>

            {/* Demo wrapper */}
            <div className="relative rounded-lg bg-gray-800/50 p-1 backdrop-blur-xl">
              <div className="relative overflow-hidden rounded-lg bg-white shadow-2xl">
                {/* Browser-like header */}
                <div className="flex h-14 items-center gap-2 border-b bg-gray-900/90 px-4">
                  <div className="flex gap-1.5">
                    {[...Array(3)].map((_, i) => (
                      <div
                        key={i}
                        className="h-3 w-3 rounded-full bg-gray-700"
                      />
                    ))}
                  </div>
                  <div className="flex-1">
                    <div className="mx-auto w-full max-w-sm rounded-full bg-gray-800 px-4 py-1.5 text-center text-xs text-gray-400">
                      wallpress.ai
                    </div>
                  </div>
                </div>

                {/* Demo content */}
                <div className="bg-white p-8">
                  <DemoPosting />
                </div>
              </div>

              {/* Corner accents */}
              <div className="absolute left-0 top-0 h-20 w-px bg-gradient-to-b from-blue-500 to-transparent" />
              <div className="absolute left-0 top-0 h-px w-20 bg-gradient-to-r from-blue-500 to-transparent" />
              <div className="absolute right-0 top-0 h-20 w-px bg-gradient-to-b from-blue-500 to-transparent" />
              <div className="absolute right-0 top-0 h-px w-20 bg-gradient-to-l from-blue-500 to-transparent" />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

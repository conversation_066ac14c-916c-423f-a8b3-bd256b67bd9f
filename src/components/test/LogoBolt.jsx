export function Logo(props) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 32 32"
      className={props.className}
    >
      <defs>
        {/* Cyber glow effect */}
        <filter id="cyberGlow" x="-50%" y="-50%" width="200%" height="200%">
          <feGaussianBlur stdDeviation="1" result="blur1" />
          <feGaussianBlur stdDeviation="2" result="blur2" />
          <feMerge>
            <feMergeNode in="blur1" />
            <feMergeNode in="blur2" />
            <feMergeNode in="SourceGraphic" />
          </feMerge>
        </filter>

        {/* Tech gradient */}
        <linearGradient id="techGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#60A5FA">
            <animate
              attributeName="stop-color"
              values="#60A5FA; #3B82F6; #60A5FA"
              dur="3s"
              repeatCount="indefinite"
            />
          </stop>
          <stop offset="100%" stopColor="#2563EB">
            <animate
              attributeName="stop-color"
              values="#2563EB; #1D4ED8; #2563EB"
              dur="3s"
              repeatCount="indefinite"
            />
          </stop>
        </linearGradient>

        {/* Energy field */}
        <pattern
          id="energyField"
          x="0"
          y="0"
          width="32"
          height="32"
          patternUnits="userSpaceOnUse"
        >
          <path
            d="M2 2l28 28M30 2L2 30"
            stroke="rgba(37, 99, 235, 0.2)"
            strokeWidth="0.5"
            strokeDasharray="1,6"
          >
            <animate
              attributeName="stroke-dashoffset"
              values="0;8"
              dur="2s"
              repeatCount="indefinite"
            />
          </path>
        </pattern>
      </defs>

      {/* Energy field background */}
      <circle cx="16" cy="16" r="15" fill="url(#energyField)" opacity="0.3">
        <animate
          attributeName="r"
          values="14;15;14"
          dur="2s"
          repeatCount="indefinite"
        />
      </circle>

      {/* Energy lines */}
      <g className="energy-lines">
        {[0, 60, 120, 180, 240, 300].map((angle, i) => (
          <line
            key={i}
            x1="16"
            y1="16"
            x2="16"
            y2="4"
            stroke="rgba(37, 99, 235, 0.3)"
            strokeWidth="0.5"
            transform={`rotate(${angle} 16 16)`}
          >
            <animate
              attributeName="opacity"
              values="0.3;0.1;0.3"
              dur={`${1.5 + i * 0.1}s`}
              repeatCount="indefinite"
            />
          </line>
        ))}
      </g>

      {/* Main thunder bolt */}
      <g filter="url(#cyberGlow)">
        <path
          d="M22.727 18.242L4.792 27.208l8.966-8.966l-4.483-4.484l17.933-8.966l-8.966 8.966z"
          fill="url(#techGradient)"
        >
          <animate
            attributeName="opacity"
            values="1;0.8;1"
            dur="2s"
            repeatCount="indefinite"
          />
        </path>
      </g>

      {/* Energy particles */}
      {/* <g className="particles"> */}
      {/*   {[...Array(8)].map((_, i) => ( */}
      {/*     <circle */}
      {/*       key={i} */}
      {/*       cx={16 + Math.cos(i * 45) * 6} */}
      {/*       cy={16 + Math.sin(i * 45) * 6} */}
      {/*       r="0.3" */}
      {/*       fill="#60A5FA" */}
      {/*     > */}
      {/*       <animate */}
      {/*         attributeName="opacity" */}
      {/*         values="0.5;0;0.5" */}
      {/*         dur={`${1 + Math.random()}s`} */}
      {/*         begin={`${Math.random()}s`} */}
      {/*         repeatCount="indefinite" */}
      {/*       /> */}
      {/*     </circle> */}
      {/*   ))} */}
      {/* </g> */}
    </svg>
  )
}

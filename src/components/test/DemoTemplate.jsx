'use client'

import { DataTable } from 'primereact/datatable'
import { Column } from 'primereact/column'
import { Button } from 'primereact/button'
import { Badge } from 'primereact/badge'
import { useState } from 'react'

export function DemoTemplate() {
  const templatesList = [
    {
      name: 'Post Templates',
      note: '',
      type: 'group',
      badge: 5,
      items: [
        {
          name: 'Default Post Template',
          note: 'Default Post Content',
          type: 'item',
        },
        {
          name: 'AI Post Template',
          note: 'AI Content',
          type: 'item',
        },
        {
          name: 'AI Images Post Template',
          note: 'AI Content with Images',
          type: 'item',
        },
        {
          name: 'AI Youtube Post Template',
          note: 'AI Content with Youtube Video',
          type: 'item',
        },
        {
          name: 'Spintax Post Template',
          note: 'Spintax Post Content',
          type: 'item',
        },
      ],
    },
    {
      name: 'Image Caption Templates',
      note: '',
      type: 'group',
      badge: 1,
      items: [
        {
          name: 'Default Caption Template',
          note: 'Default Caption',
          type: 'item',
        },
      ],
    },
    {
      name: 'Attachment Templates',
      note: '',
      type: 'group',
      badge: 1,
      items: [
        {
          name: 'Default Attachment Template',
          note: 'Default Attachment Template',
          type: 'item',
        },
      ],
    },
  ]

  const actionTemplate = (rowData) => {
    if (rowData.type === 'item') {
      return (
        <div className="pointer-events-none flex justify-center gap-2">
          <Button icon="pi pi-cog" text severity="secondary" />
          <Button icon="pi pi-file-edit" text severity="secondary" />
          <Button icon="pi pi-trash" text severity="danger" />
        </div>
      )
    }
  }

  const nameTemplate = (rowData) => {
    if (rowData.type === 'group') {
      return (
        <div className="flex items-center gap-2">
          <i className="pi pi-chevron-right"></i>
          <span className="font-semibold">{rowData.name}</span>
          <Badge value={rowData.badge} />
        </div>
      )
    }
    return <span className="pl-8">{rowData.name}</span>
  }

  // Flatten data to include both groups and items
  const flatData = templatesList.reduce((acc, group) => {
    return [...acc, group, ...group.items]
  }, [])

  return (
    <div>
      <div className="flex items-center justify-between border-gray-200 bg-white px-2 py-2">
        <div className="flex min-w-0 items-center space-x-4 py-4">
          <h1 className="ms-5 flex items-center truncate text-2xl font-semibold tracking-tight text-gray-900">
            Templates Manager
          </h1>
          <div className="flex space-x-2">
            <Button
              label="Add New"
              icon="pi pi-plus"
              severity="info"
              className="bg-blue-600 px-3 py-2 text-white hover:bg-blue-800"
            />
          </div>
        </div>
      </div>

      <DataTable
        value={flatData}
        showGridlines
        stripedRows
        tableStyle={{ minWidth: '50rem' }}
        size="normal"
        className="bg-white"
      >
        <Column field="name" header="Name" body={nameTemplate} />
        <Column field="note" header="Note" />
        <Column
          pt={{
            headerContent: {
              className: 'justify-center',
            },
            headerTitle: {
              className: 'py-2 ps-2',
            },
            bodyCell: {
              className: '!ps-4 text-center',
            },
          }}
          header="Actions"
          body={actionTemplate}
        />
      </DataTable>
    </div>
  )
}

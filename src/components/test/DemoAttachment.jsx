'use client'

import { DataTable } from 'primereact/datatable'
import { Column } from 'primereact/column'
import { Button } from 'primereact/button'
import { Image } from 'primereact/image'

export function DemoAttachment() {
  const attachmentData = [
    {
      title: 'Contemporary Leather Futon Sleeper Sofa with Modern Styling',
      publishDate: '2024-12-14',
      source: 'images.furnituredealer.net',
      preview:
        'https://tse3.mm.bing.net/th?id=OIP.o2vA5Y31tojTTkkCqCBITQHaF1&pid=Api',
    },
    {
      title: 'Versatile Cotton & Linen Sleeper Sofa with Built-in Storage',
      publishDate: '2024-12-14',
      source: 'img1.homary.com',
      preview:
        'https://tse1.mm.bing.net/th?id=OIP.OrXpvOVGIRM4d9rg1lDudgHaHa&pid=Api',
    },
    {
      title: 'Modern Black Sectional Sofa Bed with Adjustable Armrests',
      publishDate: '2024-12-14',
      source: 'i5.walmartimages.com',
      preview:
        'https://tse2.explicit.bing.net/th?id=OIP.esEurjx12v_44DM5Ux4dOgHaHa&pid=Api',
    },
    {
      title: 'Luxury Chaise Lounge Sofa Bed with Fold-Out Design',
      publishDate: '2024-12-14',
      source: 'i5.walmartimages.com',
      preview:
        'https://tse2.mm.bing.net/th?id=OIP.U2JjB61t4YeYUPd-A6AdOwHaHa&pid=Api',
    },
    {
      title: 'Italian Design Contemporary Sofa Bed by Vibieffe',
      publishDate: '2024-12-14',
      source: 'www.gomodern.co.uk',
      preview:
        'https://tse4.mm.bing.net/th?id=OIP.FsBuLMRtvjLPeJpTTau9IwHaFU&pid=Api',
    },
    {
      title: 'Premium Queen Size Convertible Sofa with Storage Space',
      publishDate: '2024-12-14',
      source: 'www.gomodern.co.uk',
      preview:
        'https://tse3.mm.bing.net/th?id=OIP.2Rn72vH8B-0TXoQq3zFQjAHaFU&pid=Api',
    },
    {
      title: 'Modern Linen Fabric Futon with Reclining Feature',
      publishDate: '2024-12-14',
      source: 'i5.walmartimages.com',
      preview:
        'https://tse2.mm.bing.net/th?id=OIP.kPUBAvyNkg7Jc5s-hYeYzAHaHa&pid=Api',
    },
    {
      title: 'Compact Convertible Sleeper Sofa in Khaki Cotton Finish',
      publishDate: '2024-12-14',
      source: 'i.pinimg.com',
      preview:
        'https://tse3.mm.bing.net/th?id=OIP.eKFgc2RqQPM-jwqEEGUy4gHaHa&pid=Api',
    },
    {
      title: 'Sectional Sleeper Sofa with Multi-Position Design',
      publishDate: '2024-12-14',
      source: 'i5.walmartimages.com',
      preview:
        'https://tse4.mm.bing.net/th?id=OIP.oiAq-sktJ2O8NAsOeoOdxwHaF7&pid=Api',
    },
    {
      title: 'Modern Round Arm Tufted Sleeper Sofa in Blue',
      publishDate: '2024-12-14',
      source: 'i5.walmartimages.com',
      preview:
        'https://tse3.mm.bing.net/th?id=OIP.2nzqZth2cVPryxJ_xhb-8QHaHa&pid=Api',
    },
    {
      title: 'Contemporary Metal Leg Sofa Bed with Upholstered Finish',
      publishDate: '2024-12-14',
      source: 'i5.walmartimages.com',
      preview:
        'https://tse1.mm.bing.net/th?id=OIP.tr4UWixeUVAisX8u1ldoHAHaHa&pid=Api',
    },
    {
      title: 'Minimalist Design Sleeper Sofa for Modern Spaces',
      publishDate: '2024-12-14',
      source: 'www.livinginashoebox.com',
      preview:
        'https://tse4.mm.bing.net/th?id=OIP.rnPDy21vePNowmiicL0rmQHaHa&pid=Api',
    },
    {
      title: 'Premium Italian Contemporary Sofa Bed Collection',
      publishDate: '2024-12-14',
      source: 'www.gomodern.co.uk',
      preview:
        'https://tse3.mm.bing.net/th?id=OIP.jdGWp0xF9oNbcOhHnrnDPgHaFP&pid=Api',
    },
    {
      title: 'Classic White Contemporary Sleeper Sofa Design',
      publishDate: '2024-12-14',
      source: 'cdn3.bigcommerce.com',
      preview:
        'https://tse1.mm.bing.net/th?id=OIP.AOHhPhDx2vY448AM66cd4QHaGQ&pid=Api',
    },
    {
      title: 'Compact Folding Sofa Bed for Small Living Spaces',
      publishDate: '2024-12-14',
      source: 'i5.walmartimages.com',
      preview:
        'https://tse1.mm.bing.net/th?id=OIP.JRpb-cNm0ZhhJ8zUPgSInwHaHa&pid=Api',
    },
    {
      title: 'Modern Convertible Futon with Adjustable Armrests',
      publishDate: '2024-12-14',
      source: 'm.media-amazon.com',
      preview:
        'https://tse3.mm.bing.net/th?id=OIP.NtA6Dk7tlJKHkQeDzH64DwHaHa&pid=Api',
    },
    {
      title: 'Elegant Fabric Sleeper Sofa with Wood Accents',
      publishDate: '2024-12-14',
      source: 'i5.walmartimages.com',
      preview:
        'https://tse4.mm.bing.net/th?id=OIP.sJzOC--m17mnjkd9OVgVagHaHa&pid=Api',
    },
    {
      title: 'Sectional Sleeper with Twin Size Pull-out Bed',
      publishDate: '2024-12-14',
      source: 'i5.walmartimages.com',
      preview:
        'https://tse3.mm.bing.net/th?id=OIP.dxk5JP8TRY-2XVFVdBxzoQHaHa&pid=Api',
    },
    {
      title: 'Contemporary Bel Air Designer Sofa Bed Collection',
      publishDate: '2024-12-14',
      source: 'www.gomodern.co.uk',
      preview:
        'https://tse1.mm.bing.net/th?id=OIP.Rju2ZL8fbvL4uuCTUXsw2gHaFU&pid=Api',
    },
    {
      title: 'Full Size Contemporary Sleeper with Storage Options',
      publishDate: '2024-12-14',
      source: 'img1.homary.com',
      preview:
        'https://tse1.mm.bing.net/th?id=OIP.V-BV8dEU8OltM2cZyuPsZwHaHa&pid=Api',
    },
  ]

  const titleBodyTemplate = (rowData) => {
    return (
      <div>
        <div className="font-semibold text-blue-900">{rowData.title}</div>
        <div className="text-sm text-blue-600">
          <a href="#" className="mr-2">
            Edit
          </a>{' '}
          |
          <a href="#" className="ml-2">
            View
          </a>
        </div>
      </div>
    )
  }

  const previewTemplate = (rowData) => {
    return (
      <Image src={rowData.preview} alt={rowData.title} width="150" preview />
    )
  }

  const actionTemplate = () => {
    return (
      <Button
        icon="pi pi-trash"
        className="pointer-events-none"
        rounded
        text
        severity="danger"
      />
    )
  }

  return (
    <div>
      <div className="flex items-center justify-between border-gray-200 bg-white px-2 py-2">
        <div className="flex min-w-0 items-center space-x-4 py-4">
          <h1 className="ms-5 flex items-center truncate text-2xl font-semibold tracking-tight text-gray-900">
            Images Manager
          </h1>
          <div className="flex space-x-2">
            <Button
              label="Add New"
              icon="pi pi-plus"
              severity="info"
              className="bg-blue-600 px-3 py-2 text-white hover:bg-blue-800"
            />
          </div>
        </div>
      </div>

      <DataTable
        value={attachmentData}
        showGridlines
        stripedRows
        removableSort
        paginator
        size="normal"
        rows={5}
        paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport"
        currentPageReportTemplate="({currentPage} of {totalPages})"
      >
        <Column
          field="title"
          header="Attachment Title"
          sortable
          body={titleBodyTemplate}
          pt={{
            headerTitle: {
              className: 'py-2 ps-2',
            },
            bodyCell: {
              className: '!ps-6',
            },
          }}
        />
        <Column
          pt={{
            headerContent: {
              className: 'justify-center',
            },
            headerTitle: {
              className: 'py-2 ps-2',
            },
            bodyCell: {
              className: '!px-4 text-center text-nowrap',
            },
          }}
          field="publishDate"
          header="Publish Date"
          sortable
        />
        <Column
          pt={{
            headerContent: {
              className: 'justify-center',
            },
            headerTitle: {
              className: 'py-2 ps-2',
            },
            bodyCell: {
              className: '!px-4 text-center text-nowrap',
            },
          }}
          field="source"
          header="Source"
          sortable
        />
        <Column
          pt={{
            headerContent: {
              className: 'justify-center',
            },
            headerTitle: {
              className: 'py-2 ps-2',
            },
            bodyCell: {
              className: '!px-4 text-center',
            },
          }}
          header="Preview"
          body={previewTemplate}
        />
        <Column
          pt={{
            headerContent: {
              className: 'justify-center',
            },
            headerTitle: {
              className: 'py-2 ps-2',
            },
            bodyCell: {
              className: '!px-4 text-center',
            },
          }}
          header="Actions"
          body={actionTemplate}
          headerStyle={{ width: '5rem' }}
        />
      </DataTable>
    </div>
  )
}

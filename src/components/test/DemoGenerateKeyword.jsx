'use client'

import { But<PERSON> } from 'primereact/button'
import { InputText } from 'primereact/inputtext'
import { Dropdown } from 'primereact/dropdown'
import { InputNumber } from 'primereact/inputnumber'
import { InputTextarea } from 'primereact/inputtextarea'
import { useState, useRef } from 'react'

export function DemoGenerateKeyword() {
  const [isMaximized, setIsMaximized] = useState(false)
  const [seedKeyword, setSeedKeyword] = useState('modern kitchen island')
  const [selectedService, setSelectedService] = useState('google')
  const [depth, setDepth] = useState(2)
  const [loading, setLoading] = useState(false)
  const [editableKeywords, setEditableKeywords] = useState(
    'modern kitchen island\n' +
      'modern kitchen island lighting\n' +
      'modern kitchen island ideas\n' +
      'modern kitchen island with seating\n' +
      'modern kitchen island pendant lighting\n' +
      'modern kitchen island designs\n' +
      'modern kitchen island stools\n' +
      'modern kitchen island storage\n' +
      'modern kitchen island dimensions\n' +
      'modern kitchen island colors',
  )
  const [sortState, setSortState] = useState('original')
  const [error, setError] = useState('')
  const originalKeywords = useRef([])

  const services = [
    { label: 'Google Search', value: 'google' },
    { label: 'Google News', value: 'google-news' },
    { label: 'Google Images', value: 'google-images' },
    { label: 'Google Books', value: 'google-books' },
    { label: 'Google Shopping', value: 'google-shopping' },
    { label: 'Google Play', value: 'google-play' },
    { label: 'YouTube', value: 'youtube' },
    { label: 'Bing', value: 'bing' },
    { label: 'Amazon', value: 'amazon' },
    { label: 'Yahoo', value: 'yahoo' },
    { label: 'Baidu', value: 'baidu' },
    { label: 'Yandex', value: 'yandex' },
  ]

  const storeOriginalKeywords = (keywords) => {
    originalKeywords.current = [...keywords]
    setSortState('original')
  }

  const cycleSort = () => {
    const currentKeywords = editableKeywords
      .split('\n')
      .filter((keyword) => keyword.trim() !== '')

    // Store original order if not yet stored
    if (originalKeywords.current.length === 0) {
      storeOriginalKeywords(currentKeywords)
    }

    switch (sortState) {
      case 'original':
        // First click: Sort A-Z
        const ascSorted = [...currentKeywords].sort((a, b) =>
          a.localeCompare(b),
        )
        setEditableKeywords(ascSorted.join('\n'))
        setSortState('asc')
        break

      case 'asc':
        // Second click: Sort Z-A
        const descSorted = [...currentKeywords].sort((a, b) =>
          b.localeCompare(a),
        )
        setEditableKeywords(descSorted.join('\n'))
        setSortState('desc')
        break

      case 'desc':
        // Third click: Restore original order
        setEditableKeywords(originalKeywords.current.join('\n'))
        setSortState('original')
        break
    }
  }

  const generateKeywords = async () => {
    if (!seedKeyword) {
      setError('Please enter a keyword')
      return
    }

    if (!selectedService) {
      setError('Please select a service')
      return
    }

    if (!depth || depth < 1 || depth > 5) {
      setError('Depth must be between 1 and 5')
      return
    }

    setLoading(true)
    setError('')

    try {
      const url = new URL('https://keyword.uix.workers.dev/')
      url.searchParams.append('keyword', seedKeyword)
      url.searchParams.append('service', selectedService)
      url.searchParams.append('depth', depth.toString())

      const response = await fetch(url)
      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let collectedKeywords = new Set()

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value)
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.trim()) {
            try {
              const data = JSON.parse(line)
              if (data.newSuggestions && Array.isArray(data.newSuggestions)) {
                data.newSuggestions.forEach((keyword) => {
                  collectedKeywords.add(keyword)
                })
                // Update textarea in real-time
                const keywordsArray = Array.from(collectedKeywords)
                setEditableKeywords(keywordsArray.join('\n'))
                storeOriginalKeywords(keywordsArray)
              }
            } catch (e) {
              // Handle JSON parse errors silently
            }
          }
        }
      }

      if (collectedKeywords.size === 0) {
        setError('No suggestions found for this keyword')
      }
    } catch (error) {
      setError(`Failed to generate keywords: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const getKeywordCount = () => {
    return editableKeywords
      .split('\n')
      .filter((keyword) => keyword.trim() !== '').length
  }

  const getSortLabel = () => {
    switch (sortState) {
      case 'asc':
        return 'Sort A-Z'
      case 'desc':
        return 'Sort Z-A'
      default:
        return 'Sort'
    }
  }

  const getSortIcon = () => {
    switch (sortState) {
      case 'asc':
        return 'pi pi-sort-alpha-down'
      case 'desc':
        return 'pi pi-sort-alpha-up'
      default:
        return 'pi pi-sort-alt'
    }
  }

  const clearResults = () => {
    setEditableKeywords('')
    setSortState('original')
    originalKeywords.current = []
  }

  return (
    <div className="rounded-lg bg-white shadow-lg">
      {/* Header */}
      <div className="bg-surface-50 sticky top-0 z-[99999] inline-flex w-full items-center justify-between rounded-t-lg border-b pe-2 shadow-sm">
        <div className="inline-flex items-center space-x-2 truncate p-4 text-lg">
          <i className="pi pi-bolt px-2 text-lg" />
          Generate Keywords
        </div>
        <div className="flex">
          <i
            className={`pi ${isMaximized ? 'pi-window-minimize' : 'pi-window-maximize'} cursor-pointer px-2 text-sm text-gray-700`}
            onClick={() => setIsMaximized(!isMaximized)}
          />
          <i className="pi pi-times cursor-pointer px-2 text-sm text-gray-700" />
        </div>
      </div>

      {/* Main Content */}
      <div className="bg-surface-0 dark:bg-surface-900 flex flex-col items-center rounded-lg p-8 pt-5">
        <div className="flex w-full flex-col space-y-4">
          {/* Input controls */}
          <div className="flex space-x-2">
            <div className="flex flex-1 flex-col space-y-2">
              <label className="text-sm font-medium">Keyword</label>
              <InputText
                value={seedKeyword}
                onChange={(e) => setSeedKeyword(e.target.value)}
                placeholder="Enter a seed keyword"
              />
            </div>

            <div className="flex w-auto flex-none flex-col space-y-2">
              <label className="text-sm font-medium">Service</label>
              <Dropdown
                value={selectedService}
                onChange={(e) => setSelectedService(e.value)}
                options={services}
                optionLabel="label"
                placeholder="Select a service"
              />
            </div>

            <div className="flex flex-none flex-col space-y-2">
              <label className="text-sm font-medium">Depth</label>
              <InputNumber
                value={depth}
                onValueChange={(e) => setDepth(e.value)}
                size={1}
                min={1}
                max={5}
                decrementButtonIcon="pi pi-minus"
                incrementButtonIcon="pi pi-plus"
              />
            </div>

            <div className="flex flex-none items-end">
              <Button
                icon="pi pi-bolt"
                loading={loading}
                label="Generate"
                className="w-full"
                onClick={generateKeywords}
              />
            </div>
          </div>

          {error && <div className="text-sm text-red-500">{error}</div>}

          {/* Results */}
          <div className="w-full">
            <div className="mb-2 flex items-center justify-between">
              <span className="text-sm text-gray-600">
                {getKeywordCount()} keywords
              </span>
              <div className="flex gap-2">
                <Button
                  label={getSortLabel()}
                  icon={getSortIcon()}
                  severity="secondary"
                  text
                  outlined={sortState === 'original'}
                  className="h-10"
                  onClick={cycleSort}
                />
                <Button
                  label="Clear"
                  icon="pi pi-trash"
                  severity="secondary"
                  text
                  className="h-10"
                  onClick={clearResults}
                />
              </div>
            </div>

            <InputTextarea
              value={editableKeywords}
              onChange={(e) => {
                setEditableKeywords(e.target.value)
                if (sortState === 'original') {
                  storeOriginalKeywords(
                    e.target.value.split('\n').filter((k) => k.trim() !== ''),
                  )
                }
              }}
              rows={5}
              className="my-1 w-full !resize-none font-mono text-sm"
              placeholder="Edit keywords here..."
            />
          </div>
        </div>
      </div>
    </div>
  )
}

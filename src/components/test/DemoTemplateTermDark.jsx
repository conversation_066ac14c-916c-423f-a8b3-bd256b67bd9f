'use client'

import { DataTable } from 'primereact/datatable'
import { Column } from 'primereact/column'
import { Button } from 'primereact/button'
import { Badge } from 'primereact/badge'
import { useState } from 'react'

export function DemoTemplate() {
  const templatesList = [
    {
      name: 'Post Templates',
      note: '',
      type: 'group',
      badge: 5,
      items: [
        {
          name: 'Default Post Template',
          note: 'Default Post Content',
          type: 'item',
        },
        {
          name: 'AI Post Template',
          note: 'AI Content',
          type: 'item',
        },
        {
          name: 'AI Images Post Template',
          note: 'AI Content with Images',
          type: 'item',
        },
        {
          name: 'AI Youtube Post Template',
          note: 'AI Content with Youtube Video',
          type: 'item',
        },
        {
          name: 'Spintax Post Template',
          note: 'Spintax Post Content',
          type: 'item',
        },
      ],
    },
    {
      name: 'Image Caption Templates',
      note: '',
      type: 'group',
      badge: 1,
      items: [
        {
          name: 'Default Caption Template',
          note: 'Default Caption',
          type: 'item',
        },
      ],
    },
    {
      name: 'Attachment Templates',
      note: '',
      type: 'group',
      badge: 1,
      items: [
        {
          name: 'Default Attachment Template',
          note: 'Default Attachment Template',
          type: 'item',
        },
      ],
    },
  ]

  // Flatten data to include both groups and items
  const flatData = templatesList.reduce((acc, group) => {
    return [...acc, group, ...group.items]
  }, [])

  const actionTemplate = (rowData) => {
    if (rowData.type === 'item') {
      return (
        <div className="flex justify-center gap-1">
          {[
            { icon: 'pi pi-cog', label: 'settings' },
            { icon: 'pi pi-file-edit', label: 'edit' },
            { icon: 'pi pi-trash', label: 'delete', danger: true },
          ].map((action) => (
            <button
              key={action.label}
              className={`group relative rounded border p-1.5 transition-all ${
                action.danger
                  ? 'border-red-900/30 text-red-400 hover:border-red-500/30 hover:text-red-300'
                  : 'border-gray-800 text-gray-500 hover:border-blue-500/30 hover:text-blue-400'
              }`}
            >
              <i className={`${action.icon} text-sm`} />
            </button>
          ))}
        </div>
      )
    }
  }

  const nameTemplate = (rowData) => {
    if (rowData.type === 'group') {
      return (
        <div className="flex items-center gap-3 py-2">
          <i className="pi pi-folder text-blue-400" />
          <span className="text-nowrap font-mono text-sm font-medium text-gray-300">
            {rowData.name}
          </span>
          <span className="text-nowrap rounded-full border border-blue-500/20 bg-blue-500/5 px-2 py-0.5 font-mono text-xs text-blue-400">
            {rowData.badge} templates
          </span>
        </div>
      )
    }
    return (
      <div className="flex items-center gap-3 pl-8">
        <i className="pi pi-file-edit text-gray-500" />
        <span className="font-mono text-sm text-gray-400">{rowData.name}</span>
      </div>
    )
  }

  const noteTemplate = (rowData) => {
    if (rowData.type === 'item') {
      return (
        <div className="font-mono text-xs text-gray-500">{rowData.note}</div>
      )
    }
  }

  const tableStyles = {
    css: `
      .p-datatable {
        background: transparent;
        font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
      }
      .p-datatable .p-datatable-header {
        background: transparent;
        border: none;
        padding: 1rem 1.5rem;
      }
      .p-datatable .p-datatable-thead > tr > th {
        background: transparent;
        border: none;
        border-bottom: 1px solid #1f2937;
        color: #9ca3af;
        font-weight: 500;
        padding: 0.75rem 1rem;
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
      }
      .p-datatable .p-datatable-tbody > tr {
        background: transparent;
        transition: background-color 0.2s;
      }
      .p-datatable .p-datatable-tbody > tr > td {
        border: none;
        border-bottom: 1px solid #1f2937;
        padding: 0.75rem 1rem;
        color: #d1d5db;
      }
      .p-datatable .p-datatable-tbody > tr:hover {
        background: rgba(59, 130, 246, 0.05);
      }
    `,
  }

  return (
    <div className="relative overflow-hidden rounded-sm border border-gray-800 bg-[#0c0d12]">
      <style>{tableStyles.css}</style>

      {/* Enhanced Header */}
      <div className="border-b border-gray-800 bg-[#0a0b11]/80 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <h2 className="font-mono text-sm font-medium text-gray-300">
              Templates Manager
            </h2>
            <span className="hidden rounded-full border border-blue-500/20 bg-blue-500/5 px-2 py-0.5 font-mono text-xs text-blue-400 md:block">
              {flatData.filter((item) => item.type === 'item').length} total
            </span>
          </div>

          <button className="group relative overflow-hidden rounded border border-blue-500/30 bg-blue-500/5 px-3 py-1.5 font-mono text-xs text-blue-400 hover:bg-blue-500/10">
            <span className="flex items-center gap-2">
              <i className="pi pi-plus text-[10px]" />
              <span className="hidden sm:block">Add New</span>
            </span>
            <div className="group-hover:animate-shine absolute inset-0 -z-10 translate-x-[-100%] bg-gradient-to-r from-transparent via-blue-500/10 to-transparent" />
          </button>
        </div>
      </div>

      {/* Enhanced Table */}
      <DataTable
        value={flatData}
        className="border-none"
        emptyMessage={
          <span className="font-mono text-sm text-gray-500">
            No templates found
          </span>
        }
      >
        <Column field="name" header="Template" body={nameTemplate} />
        <Column field="note" header="Description" body={noteTemplate} />
        <Column
          pt={{
            headerContent: {
              className: 'justify-center',
            },
          }}
          header="Actions"
          body={actionTemplate}
        />
      </DataTable>

      {/* Enhanced Footer */}
      {/* <div className="border-t border-gray-800 bg-[#0a0b11]/80 px-4 py-2"> */}
      {/*   <div className="flex items-center justify-between font-mono text-xs text-gray-500"> */}
      {/*     <div className="flex items-center gap-2"> */}
      {/*       <span className="h-1.5 w-1.5 animate-pulse rounded-full bg-emerald-500" /> */}
      {/*     </div> */}
      {/*     <span>Last Modified: 2h ago</span> */}
      {/*   </div> */}
      {/* </div> */}
    </div>
  )
}

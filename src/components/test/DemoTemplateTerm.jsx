'use client'

import { DataTable } from 'primereact/datatable'
import { Column } from 'primereact/column'
import { Button } from 'primereact/button'
import { Badge } from 'primereact/badge'
import { useState } from 'react'

export function DemoTemplate() {
  const templatesList = [
    {
      name: 'Post Templates',
      note: '',
      type: 'group',
      badge: 5,
      items: [
        {
          name: 'Default Post Template',
          note: 'Default Post Content',
          type: 'item',
        },
        {
          name: 'AI Post Template',
          note: 'AI Content',
          type: 'item',
        },
        {
          name: 'AI Images Post Template',
          note: 'AI Content with Images',
          type: 'item',
        },
        {
          name: 'AI Youtube Post Template',
          note: 'AI Content with Youtube Video',
          type: 'item',
        },
        {
          name: 'Spintax Post Template',
          note: 'Spintax Post Content',
          type: 'item',
        },
      ],
    },
    {
      name: 'Image Caption Templates',
      note: '',
      type: 'group',
      badge: 1,
      items: [
        {
          name: 'Default Caption Template',
          note: 'Default Caption',
          type: 'item',
        },
      ],
    },
    {
      name: 'Attachment Templates',
      note: '',
      type: 'group',
      badge: 1,
      items: [
        {
          name: 'Default Attachment Template',
          note: 'Default Attachment Template',
          type: 'item',
        },
      ],
    },
  ]

  const actionTemplate = (rowData) => {
    if (rowData.type === 'item') {
      return (
        <div className="flex justify-center gap-1">
          {[
            { icon: 'pi pi-cog', label: 'settings' },
            { icon: 'pi pi-file-edit', label: 'edit' },
            { icon: 'pi pi-trash', label: 'delete', danger: true },
          ].map((action) => (
            <button
              key={action.label}
              className={`group relative rounded border p-1.5 transition-all ${
                action.danger
                  ? 'border-red-200 text-red-400 hover:border-red-500/30 hover:text-red-500'
                  : 'border-gray-200 text-gray-400 hover:border-blue-500/30 hover:text-blue-500'
              }`}
            >
              <i className={`${action.icon} text-sm`} />
              <div className="absolute -bottom-8 left-1/2 hidden -translate-x-1/2 rounded bg-gray-900 px-2 py-1 font-mono text-[10px] text-white group-hover:block">
                {action.label}
              </div>
            </button>
          ))}
        </div>
      )
    }
  }

  const nameTemplate = (rowData) => {
    if (rowData.type === 'group') {
      return (
        <div className="flex items-center gap-3 py-2">
          <i className="pi pi-folder text-blue-500" />
          <span className="font-mono text-sm font-medium text-gray-900">
            {rowData.name}
          </span>
          <span className="rounded-full border border-blue-500/20 bg-blue-500/5 px-2 py-0.5 font-mono text-xs text-blue-600">
            {rowData.badge} templates
          </span>
        </div>
      )
    }
    return (
      <div className="flex items-center gap-3 pl-8">
        <i className="pi pi-file-edit text-gray-400" />
        <span className="font-mono text-sm text-gray-600">{rowData.name}</span>
      </div>
    )
  }

  const noteTemplate = (rowData) => {
    if (rowData.type === 'item') {
      return (
        <div className="font-mono text-xs text-gray-500">{rowData.note}</div>
      )
    }
  }

  // Flatten data to include both groups and items
  const flatData = templatesList.reduce((acc, group) => {
    return [...acc, group, ...group.items]
  }, [])

  return (
    <div className="relative overflow-hidden rounded-sm border border-gray-200 bg-white">
      {/* Enhanced Header */}
      <div className="border-b border-gray-200 bg-gray-50/80 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <span className="font-mono text-xs text-gray-500">{'>'}</span>
            <h2 className="font-mono text-sm font-medium text-gray-900">
              templates.manager
            </h2>
            <span className="rounded-full border border-blue-500/20 bg-blue-500/5 px-2 py-0.5 font-mono text-xs text-blue-600">
              {flatData.filter((item) => item.type === 'item').length} total
            </span>
          </div>

          <button className="group relative overflow-hidden rounded border border-blue-500/30 bg-blue-500/5 px-3 py-1.5 font-mono text-xs text-blue-600 hover:bg-blue-500/10">
            <span className="flex items-center gap-2">
              <i className="pi pi-plus text-[10px]" />
              new.template
            </span>
            <div className="group-hover:animate-shine absolute inset-0 -z-10 translate-x-[-100%] bg-gradient-to-r from-transparent via-blue-500/10 to-transparent" />
          </button>
        </div>
      </div>

      {/* Enhanced Table */}
      <DataTable
        value={flatData}
        className="border-none"
        emptyMessage={
          <span className="font-mono text-sm text-gray-500">
            No templates found
          </span>
        }
      >
        <Column field="name" header="Template" body={nameTemplate} />
        <Column field="note" header="Description" body={noteTemplate} />
        <Column header="Actions" body={actionTemplate} />
      </DataTable>

      {/* Enhanced Footer */}
      <div className="border-t border-gray-200 bg-gray-50/80 px-4 py-2">
        <div className="flex items-center justify-between font-mono text-xs text-gray-500">
          <div className="flex items-center gap-2">
            <span className="h-1.5 w-1.5 animate-pulse rounded-full bg-emerald-500" />
            template.engine: active
          </div>
          <span>last_modified: 2h ago</span>
        </div>
      </div>
    </div>
  )
}

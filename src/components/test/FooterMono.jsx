import Link from 'next/link'
import { Container } from '@/components/Container'
import { Logo } from '@/components/Logo'

const navigation = {
  main: [
    { name: 'Features', href: '#features' },
    { name: 'Pricing', href: '#pricing' },
    { name: 'About', href: '#about' },
    { name: 'Blog', href: '/blog' },
  ],
  support: [
    { name: 'Documentation', href: '#' },
    { name: 'Guides', href: '#' },
    { name: 'API Status', href: '#' },
  ],
  legal: [
    { name: 'Privacy', href: '#' },
    { name: 'Terms', href: '#' },
    { name: 'License', href: '#' },
  ],
}

export function Footer() {
  return (
    <footer className="relative bg-gray-950">
      {/* Subtle grid background */}
      <div className="absolute inset-0 bg-[linear-gradient(to_right,#8882_1px,transparent_1px),linear-gradient(to_bottom,#8882_1px,transparent_1px)] bg-[size:24px_24px] opacity-25" />

      <Container>
        <div className="relative py-20">
          {/* Main footer content */}
          <div className="grid grid-cols-1 gap-16 md:grid-cols-2 lg:grid-cols-4">
            {/* Brand */}
            <div className="space-y-6">
              <Link href="/" className="flex items-center gap-2">
                <Logo className="h-8 w-auto fill-white" />
                <span className="text-xl text-white">
                  Wall<span className="font-semibold">Press</span>
                </span>
              </Link>
              <p className="text-sm leading-relaxed text-gray-400">
                Advanced content automation meets WordPress simplicity.
                Transform your content creation workflow today.
              </p>
              <div className="flex gap-4">
                <Link
                  href="#"
                  className="text-gray-400 transition-colors hover:text-white"
                  aria-label="Twitter"
                >
                  <i className="pi pi-twitter text-xl" />
                </Link>
                <Link
                  href="#"
                  className="text-gray-400 transition-colors hover:text-white"
                  aria-label="GitHub"
                >
                  <i className="pi pi-github text-xl" />
                </Link>
                <Link
                  href="#"
                  className="text-gray-400 transition-colors hover:text-white"
                  aria-label="Discord"
                >
                  <i className="pi pi-discord text-xl" />
                </Link>
              </div>
            </div>

            {/* Navigation */}
            <div>
              <h3 className="text-sm font-semibold uppercase tracking-wider text-gray-400">
                Product
              </h3>
              <ul className="mt-4 space-y-3">
                {navigation.main.map((item) => (
                  <li key={item.name}>
                    <Link
                      href={item.href}
                      className="text-sm text-gray-300 transition-colors hover:text-white"
                    >
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Support */}
            <div>
              <h3 className="text-sm font-semibold uppercase tracking-wider text-gray-400">
                Support
              </h3>
              <ul className="mt-4 space-y-3">
                {navigation.support.map((item) => (
                  <li key={item.name}>
                    <Link
                      href={item.href}
                      className="text-sm text-gray-300 transition-colors hover:text-white"
                    >
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Newsletter */}
            <div>
              <h3 className="text-sm font-semibold uppercase tracking-wider text-gray-400">
                Stay Updated
              </h3>
              <p className="mt-4 text-sm text-gray-300">
                Subscribe to our newsletter for the latest updates.
              </p>
              <form className="mt-4">
                <div className="flex gap-2">
                  <input
                    type="email"
                    placeholder="Enter your email"
                    className="w-full rounded-lg border border-gray-800 bg-gray-800/50 px-4 py-2 text-sm text-white placeholder:text-gray-500 focus:border-blue-500 focus:ring-blue-500"
                  />
                  <button
                    type="submit"
                    className="rounded-lg bg-blue-500 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-blue-400"
                  >
                    →
                  </button>
                </div>
              </form>
            </div>
          </div>

          {/* Bottom bar */}
          <div className="mt-16 border-t border-gray-800 pt-8">
            <div className="flex flex-col items-center justify-between gap-6 sm:flex-row">
              <p className="text-sm text-gray-400">
                © {new Date().getFullYear()} WallPress. All rights reserved.
              </p>
              <div className="flex gap-6">
                {navigation.legal.map((item) => (
                  <Link
                    key={item.name}
                    href={item.href}
                    className="text-sm text-gray-400 transition-colors hover:text-white"
                  >
                    {item.name}
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </div>
      </Container>
    </footer>
  )
}

'use client'

import Link from 'next/link'
import { useState, useEffect } from 'react'
import { Logo } from '@/components/LogoBolt'
import { LogoBrutalish } from '@/components/logo/LogoBrutalish'

function MobileMenu({ isOpen, onClose }) {
  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50">
      <div
        className="absolute inset-0 bg-[#0a0b11]/90 backdrop-blur-md"
        onClick={onClose}
      />
      <div className="absolute right-4 top-4 w-64">
        <div className="relative border border-blue-900/30 bg-[#0a0b11]/95 shadow-[0_0_30px_rgba(0,0,0,0.3)]">
          {/* Enhanced terminal header */}
          <div className="flex h-8 items-center justify-between border-b border-blue-900/30 bg-[#0c0d12] px-3">
            <div className="flex items-center gap-2">
              <span className="h-1.5 w-1.5 animate-pulse rounded-full bg-blue-500/50" />
              <span className="font-mono text-xs text-blue-500/70">
                NAVIGATION MENU
              </span>
            </div>
            <button
              onClick={onClose}
              className="text-gray-500 transition-colors hover:text-blue-400"
            >
              <i className="pi pi-times text-sm" />
            </button>
          </div>

          <nav className="space-y-0.5 p-2">
            {['Features', 'Pricing', 'Sign in'].map((item) => (
              <Link
                key={item}
                href={`/${item.toLowerCase().replace(' ', '')}`}
                className="flex items-center gap-2 px-3 py-2 font-mono text-xs text-gray-400 transition-all hover:translate-x-0.5 hover:bg-blue-500/5 hover:text-blue-400"
                onClick={onClose}
              >
                <span className="h-1 w-1 rounded-full bg-blue-500/50" />
                {`${item}`}
              </Link>
            ))}
          </nav>
        </div>
      </div>
    </div>
  )
}

export function Header() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [scrolled, setScrolled] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 50)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  return (
    <header className="fixed inset-x-0 top-0 z-40">
      {/* Enhanced backdrop */}
      <div
        className={`absolute inset-0 border-b border-blue-900/30 transition-all duration-300 ${
          scrolled ? 'bg-[#0a0b11]' : 'bg-[#0a0b11]/80 backdrop-blur-md'
        }`}
      />
      {/* <div className="absolute inset-0 border-b border-blue-900/30 bg-[#0a0b11] transition-all duration-300" /> */}

      <div className="relative mx-auto max-w-7xl px-6">
        <div className="absolute bottom-0 left-0 h-[1px] w-full bg-gradient-to-r from-transparent via-blue-500/30 to-transparent" />

        <div className="flex h-16 items-center justify-between">
          {/* Enhanced logo */}
          <Link href="/" className="group flex items-center gap-3">
            {/* <div className="relative"> */}
            {/*   <Logo className="h-6 w-auto fill-white transition-transform group-hover:scale-105" /> */}
            {/*   <div className="absolute -inset-2 -z-10 opacity-0 blur transition group-hover:opacity-30"> */}
            {/*     <Logo className="h-10 w-auto fill-blue-500" /> */}
            {/*   </div> */}
            {/* </div> */}

            <Logo className="absolute top-1.5 z-10 h-12" />
            <div className="group relative ms-10 hover:bg-blue-500/10 hover:shadow-[0_0_20px_rgba(0,100,255,0.3)]">
              <LogoBrutalish />
              <div className="group-hover:animate-shine absolute inset-0 -z-10 translate-x-[-100%] bg-gradient-to-r from-transparent via-blue-500/10 to-transparent" />
            </div>
          </Link>

          {/* Enhanced Desktop Navigation */}
          <div className="hidden items-center gap-8 md:flex">
            <nav className="flex items-center gap-6">
              {['Features', 'Pricing'].map((item) => (
                <Link
                  key={item}
                  href={`/${item.toLowerCase()}`}
                  className="font-mono text-xs text-gray-400 transition-all hover:translate-x-0.5 hover:text-blue-400"
                >
                  {`${item}`}
                </Link>
              ))}
            </nav>

            <div className="h-4 w-px bg-blue-900/30" />

            <div className="flex items-center gap-6">
              <Link
                href="/login"
                className="font-mono text-xs text-gray-400 transition-all hover:translate-x-0.5 hover:text-blue-400"
              >
                Login
              </Link>
              <Link
                href="/register"
                className="group relative overflow-hidden border border-blue-500/50 bg-blue-500/5 px-4 py-1.5 font-mono text-xs text-blue-400 transition-all hover:bg-blue-500/10 hover:shadow-[0_0_20px_rgba(0,100,255,0.3)]"
              >
                <span className="flex items-center gap-2">
                  <span className="h-1 w-1 animate-pulse rounded-full bg-blue-500" />
                  Download
                </span>
                <div className="group-hover:animate-shine absolute inset-0 -z-10 translate-x-[-100%] bg-gradient-to-r from-transparent via-blue-500/10 to-transparent" />
              </Link>
            </div>
          </div>

          {/* Enhanced Mobile Menu Button */}
          <button
            className="relative rounded border border-blue-900/30 px-3 py-1.5 font-mono text-xs text-gray-400 transition-all hover:border-blue-500/50 hover:text-blue-400 md:hidden"
            onClick={() => setIsMobileMenuOpen(true)}
          >
            Menu
          </button>
        </div>
      </div>

      <MobileMenu
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
      />
    </header>
  )
}

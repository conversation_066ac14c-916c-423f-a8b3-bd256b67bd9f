'use client'

import { DataTable } from 'primereact/datatable'
import { Column } from 'primereact/column'
import { Button } from 'primereact/button'
import { Image } from 'primereact/image'

export function DemoAttachment() {
  const attachmentData = [
    {
      title: 'Contemporary Leather Futon Sleeper Sofa with Modern Styling',
      publishDate: '2024-12-14',
      source: 'images.furnituredealer.net',
      preview:
        'https://tse3.mm.bing.net/th?id=OIP.o2vA5Y31tojTTkkCqCBITQHaF1&pid=Api',
    },
    {
      title: 'Versatile Cotton & Linen Sleeper Sofa with Built-in Storage',
      publishDate: '2024-12-14',
      source: 'img1.homary.com',
      preview:
        'https://tse1.mm.bing.net/th?id=OIP.OrXpvOVGIRM4d9rg1lDudgHaHa&pid=Api',
    },
    {
      title: 'Modern Black Sectional Sofa Bed with Adjustable Armrests',
      publishDate: '2024-12-14',
      source: 'i5.walmartimages.com',
      preview:
        'https://tse2.explicit.bing.net/th?id=OIP.esEurjx12v_44DM5Ux4dOgHaHa&pid=Api',
    },
    {
      title: 'Luxury Chaise Lounge Sofa Bed with Fold-Out Design',
      publishDate: '2024-12-14',
      source: 'i5.walmartimages.com',
      preview:
        'https://tse2.mm.bing.net/th?id=OIP.U2JjB61t4YeYUPd-A6AdOwHaHa&pid=Api',
    },
    {
      title: 'Italian Design Contemporary Sofa Bed by Vibieffe',
      publishDate: '2024-12-14',
      source: 'www.gomodern.co.uk',
      preview:
        'https://tse4.mm.bing.net/th?id=OIP.FsBuLMRtvjLPeJpTTau9IwHaFU&pid=Api',
    },
    {
      title: 'Premium Queen Size Convertible Sofa with Storage Space',
      publishDate: '2024-12-14',
      source: 'www.gomodern.co.uk',
      preview:
        'https://tse3.mm.bing.net/th?id=OIP.2Rn72vH8B-0TXoQq3zFQjAHaFU&pid=Api',
    },
    {
      title: 'Modern Linen Fabric Futon with Reclining Feature',
      publishDate: '2024-12-14',
      source: 'i5.walmartimages.com',
      preview:
        'https://tse2.mm.bing.net/th?id=OIP.kPUBAvyNkg7Jc5s-hYeYzAHaHa&pid=Api',
    },
    {
      title: 'Compact Convertible Sleeper Sofa in Khaki Cotton Finish',
      publishDate: '2024-12-14',
      source: 'i.pinimg.com',
      preview:
        'https://tse3.mm.bing.net/th?id=OIP.eKFgc2RqQPM-jwqEEGUy4gHaHa&pid=Api',
    },
    {
      title: 'Sectional Sleeper Sofa with Multi-Position Design',
      publishDate: '2024-12-14',
      source: 'i5.walmartimages.com',
      preview:
        'https://tse4.mm.bing.net/th?id=OIP.oiAq-sktJ2O8NAsOeoOdxwHaF7&pid=Api',
    },
    {
      title: 'Modern Round Arm Tufted Sleeper Sofa in Blue',
      publishDate: '2024-12-14',
      source: 'i5.walmartimages.com',
      preview:
        'https://tse3.mm.bing.net/th?id=OIP.2nzqZth2cVPryxJ_xhb-8QHaHa&pid=Api',
    },
    {
      title: 'Contemporary Metal Leg Sofa Bed with Upholstered Finish',
      publishDate: '2024-12-14',
      source: 'i5.walmartimages.com',
      preview:
        'https://tse1.mm.bing.net/th?id=OIP.tr4UWixeUVAisX8u1ldoHAHaHa&pid=Api',
    },
    {
      title: 'Minimalist Design Sleeper Sofa for Modern Spaces',
      publishDate: '2024-12-14',
      source: 'www.livinginashoebox.com',
      preview:
        'https://tse4.mm.bing.net/th?id=OIP.rnPDy21vePNowmiicL0rmQHaHa&pid=Api',
    },
    {
      title: 'Premium Italian Contemporary Sofa Bed Collection',
      publishDate: '2024-12-14',
      source: 'www.gomodern.co.uk',
      preview:
        'https://tse3.mm.bing.net/th?id=OIP.jdGWp0xF9oNbcOhHnrnDPgHaFP&pid=Api',
    },
    {
      title: 'Classic White Contemporary Sleeper Sofa Design',
      publishDate: '2024-12-14',
      source: 'cdn3.bigcommerce.com',
      preview:
        'https://tse1.mm.bing.net/th?id=OIP.AOHhPhDx2vY448AM66cd4QHaGQ&pid=Api',
    },
    {
      title: 'Compact Folding Sofa Bed for Small Living Spaces',
      publishDate: '2024-12-14',
      source: 'i5.walmartimages.com',
      preview:
        'https://tse1.mm.bing.net/th?id=OIP.JRpb-cNm0ZhhJ8zUPgSInwHaHa&pid=Api',
    },
    {
      title: 'Modern Convertible Futon with Adjustable Armrests',
      publishDate: '2024-12-14',
      source: 'm.media-amazon.com',
      preview:
        'https://tse3.mm.bing.net/th?id=OIP.NtA6Dk7tlJKHkQeDzH64DwHaHa&pid=Api',
    },
    {
      title: 'Elegant Fabric Sleeper Sofa with Wood Accents',
      publishDate: '2024-12-14',
      source: 'i5.walmartimages.com',
      preview:
        'https://tse4.mm.bing.net/th?id=OIP.sJzOC--m17mnjkd9OVgVagHaHa&pid=Api',
    },
    {
      title: 'Sectional Sleeper with Twin Size Pull-out Bed',
      publishDate: '2024-12-14',
      source: 'i5.walmartimages.com',
      preview:
        'https://tse3.mm.bing.net/th?id=OIP.dxk5JP8TRY-2XVFVdBxzoQHaHa&pid=Api',
    },
    {
      title: 'Contemporary Bel Air Designer Sofa Bed Collection',
      publishDate: '2024-12-14',
      source: 'www.gomodern.co.uk',
      preview:
        'https://tse1.mm.bing.net/th?id=OIP.Rju2ZL8fbvL4uuCTUXsw2gHaFU&pid=Api',
    },
    {
      title: 'Full Size Contemporary Sleeper with Storage Options',
      publishDate: '2024-12-14',
      source: 'img1.homary.com',
      preview:
        'https://tse1.mm.bing.net/th?id=OIP.V-BV8dEU8OltM2cZyuPsZwHaHa&pid=Api',
    },
  ]

  const titleBodyTemplate = (rowData) => {
    return (
      <div className="space-y-1">
        <div className="line-clamp-1 font-mono text-sm text-gray-300">
          {rowData.title}
        </div>
        <div className="flex items-center gap-2 font-mono text-xs">
          <a href="#" className="text-blue-400 hover:text-blue-300">
            Edit
          </a>
          <span className="text-gray-700">|</span>
          <a href="#" className="text-blue-400 hover:text-blue-300">
            View
          </a>
        </div>
      </div>
    )
  }

  const previewTemplate = (rowData) => {
    return (
      <div className="group relative text-center">
        <Image
          src={rowData.preview}
          alt={rowData.title}
          width="100"
          preview
          className="rounded border border-gray-800 transition-all group-hover:border-blue-500/30"
          pt={{
            image: { className: 'rounded' },
          }}
        />
      </div>
    )
  }

  const sourceTemplate = (rowData) => {
    return (
      <div className="font-mono text-xs">
        <span className="text-blue-400">{rowData.source}</span>
      </div>
    )
  }

  const dateTemplate = (rowData) => {
    return (
      <div className="text-nowrap font-mono text-xs text-gray-400">
        {new Date(rowData.publishDate).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
        })}
      </div>
    )
  }

  const actionTemplate = () => {
    return (
      <div className="text-center">
        <button className="group relative rounded border border-red-900/30 p-1.5 text-red-400 hover:border-red-500/30 hover:text-red-300">
          <i className="pi pi-trash text-sm" />
        </button>
      </div>
    )
  }

  const tableStyles = {
    css: `
      .p-datatable {
        background: transparent;
        font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
      }
      .p-datatable .p-datatable-header {
        background: transparent;
        border: none;
        padding: 1rem 1.5rem;
      }
      .p-datatable .p-datatable-thead > tr > th {
        background: transparent;
        border: none;
        border-bottom: 1px solid #1f2937;
        color: #9ca3af;
        font-weight: 500;
        padding: 0.75rem 1rem;
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
      }
      .p-datatable .p-datatable-tbody > tr {
        background: transparent;
        transition: background-color 0.2s;
      }
      .p-datatable .p-datatable-tbody > tr > td {
        border: none;
        border-bottom: 1px solid #1f2937;
        padding: 0.75rem 1rem;
        color: #d1d5db;
      }
      .p-datatable .p-datatable-tbody > tr:hover {
        background: rgba(59, 130, 246, 0.05);
      }
      .p-paginator {
        background: transparent;
        border: none;
        padding: 1rem;
      }
      .p-paginator .p-paginator-element {
        color: #9ca3af;
        min-width: 2rem;
        height: 2rem;
        margin: 0 0.125rem;
        font-size: 0.75rem;
      }
      .p-paginator .p-paginator-element.p-highlight {
        background: #1f2937;
        border-color: #374151;
        color: #d1d5db;
      }
      .p-paginator .p-paginator-element:not(.p-highlight):hover {
        background: rgba(59, 130, 246, 0.05);
        border-color: #374151;
        color: #d1d5db;
      }

      .p-paginator .p-paginator-pages .p-paginator-page:focus {
        box-shadow: none;
      }

      .p-paginator .p-paginator-first:not(.p-disabled):hover,
      .p-paginator .p-paginator-prev:not(.p-disabled):hover,
      .p-paginator .p-paginator-next:not(.p-disabled):hover,
      .p-paginator .p-paginator-last:not(.p-disabled):hover {
        background: rgba(59, 130, 246, 0.05);
        border-color: #374151;
        color: #d1d5db;
      }

      .p-paginator .p-paginator-first.p-disabled,
      .p-paginator .p-paginator-prev.p-disabled,
      .p-paginator .p-paginator-next.p-disabled,
      .p-paginator .p-paginator-last.p-disabled {
        color: #4b5563;
    `,
  }

  return (
    <div className="relative overflow-hidden rounded-sm border border-gray-800 bg-[#0c0d12]">
      <style>{tableStyles.css}</style>

      {/* Enhanced Header */}
      <div className="border-b border-gray-800 bg-[#0a0b11]/80 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <h2 className="font-mono text-sm font-medium text-gray-300">
              Images Manager
            </h2>
            <span className="hidden rounded-full border border-blue-500/20 bg-blue-500/5 px-2 py-0.5 font-mono text-xs text-blue-400 md:block">
              {attachmentData.length} images
            </span>
          </div>

          <button className="group relative overflow-hidden rounded border border-blue-500/30 bg-blue-500/5 px-3 py-1.5 font-mono text-xs text-blue-400 hover:bg-blue-500/10">
            <span className="flex items-center gap-2">
              <i className="pi pi-plus text-[10px]" />
              <span className="hidden sm:block">Add</span>
            </span>
            <div className="group-hover:animate-shine absolute inset-0 -z-10 translate-x-[-100%] bg-gradient-to-r from-transparent via-blue-500/10 to-transparent" />
          </button>
        </div>
      </div>

      {/* Enhanced Table */}
      <DataTable
        value={attachmentData}
        paginator
        rows={5}
        className="border-none"
        emptyMessage={
          <span className="font-mono text-sm text-gray-500">
            No images found
          </span>
        }
        paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport"
        currentPageReportTemplate="page {currentPage} of {totalPages}"
        paginatorClassName="border-t border-gray-800 bg-[#0a0b11]/80"
      >
        <Column
          field="title"
          header="Title"
          sortable
          body={titleBodyTemplate}
        />
        <Column
          pt={{
            headerContent: {
              className: 'justify-center',
            },
          }}
          field="publishDate"
          header="Date"
          sortable
          body={dateTemplate}
        />
        <Column field="source" header="Source" sortable body={sourceTemplate} />
        <Column
          pt={{
            headerContent: {
              className: 'justify-center',
            },
          }}
          header="Preview"
          body={previewTemplate}
        />
        <Column
          pt={{
            headerContent: {
              className: 'justify-center',
            },
          }}
          header="Actions"
          body={actionTemplate}
        />
      </DataTable>

      {/* Enhanced Footer */}
      {/* <div className="border-t border-gray-800 bg-[#0a0b11]/80 px-4 py-2"> */}
      {/*   <div className="flex items-center justify-between font-mono text-xs text-gray-500"> */}
      {/*     <div className="flex items-center gap-2"> */}
      {/*       <span className="h-1.5 w-1.5 animate-pulse rounded-full bg-emerald-500" /> */}
      {/*       cdn.status: connected */}
      {/*     </div> */}
      {/*     <span>storage.used: 45%</span> */}
      {/*   </div> */}
      {/* </div> */}
    </div>
  )
}

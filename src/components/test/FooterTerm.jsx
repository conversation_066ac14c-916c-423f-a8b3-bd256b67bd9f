import Link from 'next/link'
import { Container } from '@/components/Container'
import { Logo } from '@/components/LogoBolt'
import { LogoBrutalish } from '@/components/logo/LogoBrutalish'

const navigation = {
  product: [
    { name: 'Features', href: '#features', icon: 'pi pi-bolt' },
    { name: 'Pricing', href: '#pricing', icon: 'pi pi-tag' },
    { name: 'Showcase', href: '#showcase', icon: 'pi pi-images' },
    { name: 'Roadmap', href: '/roadmap', icon: 'pi pi-map' },
  ],
  resources: [
    { name: 'Documentation', href: '#', icon: 'pi pi-book' },
    { name: 'API Reference', href: '#', icon: 'pi pi-code' },
    { name: 'System Status', href: '#', icon: 'pi pi-server' },
    { name: 'Release Notes', href: '#', icon: 'pi pi-list' },
  ],
  company: [
    { name: 'About', href: '#', icon: 'pi pi-users' },
    { name: 'Blog', href: '#', icon: 'pi pi-pencil' },
    { name: 'Careers', href: '#', icon: 'pi pi-briefcase' },
    { name: 'Contact', href: '#', icon: 'pi pi-envelope' },
  ],
}

export function Footer() {
  return (
    <footer className="relative bg-gray-950 py-20">
      {/* Enhanced Background Effects */}
      <div className="absolute inset-0 bg-[linear-gradient(to_right,#0066ff08_1px,transparent_1px),linear-gradient(to_bottom,#0066ff08_1px,transparent_1px)] bg-[size:24px_24px]" />

      <Container>
        {/* Main Content */}
        <div className="relative">
          {/* Top Section with Logo and Newsletter */}
          <div className="mb-16 grid gap-16 border-b border-gray-800 pb-16 lg:grid-cols-2">
            {/* Brand Section */}
            <div className="space-y-6">
              <Link href="/" className="group inline-flex items-center gap-3">
                <Logo className="h-12" />
              </Link>

              <p className="max-w-md font-mono text-gray-400">
                Experience the next evolution of WordPress content creation with
                <span className="text-blue-400">
                  {' '}
                  AI-powered automation
                </span>{' '}
                and
                <span className="text-blue-400"> intelligent optimization</span>
                .
              </p>

              {/* Social Links */}
              <div className="flex gap-4">
                {[
                  {
                    name: 'Twitter',
                    icon: 'pi pi-twitter',
                    color: 'hover:bg-[#1DA1F2]',
                  },
                  {
                    name: 'GitHub',
                    icon: 'pi pi-github',
                    color: 'hover:bg-[#2B3137]',
                  },
                  {
                    name: 'Discord',
                    icon: 'pi pi-discord',
                    color: 'hover:bg-[#5865F2]',
                  },
                ].map((social) => (
                  <a
                    key={social.name}
                    href="#"
                    className={`group relative flex h-10 w-10 items-center justify-center rounded-lg border border-gray-800 text-gray-400 transition-all hover:border-transparent hover:text-white ${social.color}`}
                  >
                    <i className={`${social.icon} text-lg`} />
                    <div className="absolute -bottom-10 hidden rounded bg-gray-900 px-2 py-1 text-xs group-hover:block">
                      {social.name}
                    </div>
                  </a>
                ))}
              </div>
            </div>

            {/* Newsletter Section */}
            <div className="relative overflow-hidden rounded-2xl border border-gray-800 bg-gray-900/50 p-8">
              <div className="absolute inset-0 bg-gradient-to-b from-blue-500/5 to-transparent" />

              <div className="relative">
                <h3 className="font-mono text-lg text-gray-200">
                  Join the Evolution
                </h3>
                <p className="mt-2 font-mono text-sm text-gray-400">
                  Get weekly insights on AI content creation and WordPress
                  automation.
                </p>

                <form className="mt-6">
                  <div className="flex flex-col gap-3 md:flex-row">
                    <div className="w-full flex-1">
                      <input
                        type="email"
                        placeholder="Enter your email"
                        className="w-full rounded-lg border border-gray-800 bg-gray-900/50 px-4 py-2.5 font-mono text-sm text-gray-300 placeholder:text-gray-600 focus:border-blue-500/50 focus:ring-blue-500/20"
                      />
                    </div>
                    <div className="flex-none">
                      <button
                        type="submit"
                        className="group relative w-full overflow-hidden rounded-lg border border-blue-500/30 bg-blue-500/10 px-4 py-2.5 font-mono text-sm text-blue-400 transition-all hover:bg-blue-500/20"
                      >
                        <span className="flex items-center justify-center gap-2">
                          <span className="h-1.5 w-1.5 animate-pulse rounded-full bg-blue-500" />
                          Subscribe
                        </span>
                        <div className="group-hover:animate-shine absolute inset-0 -z-10 translate-x-[-100%] bg-gradient-to-r from-transparent via-blue-500/10 to-transparent" />
                      </button>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>

          {/* Navigation Grid */}
          <div className="grid gap-8 sm:grid-cols-2 lg:grid-cols-3">
            {Object.entries(navigation).map(([category, items]) => (
              <div key={category} className="space-y-6">
                <h3 className="font-mono text-sm uppercase tracking-wider text-gray-400">
                  {category}
                </h3>
                <ul className="space-y-4">
                  {items.map((item) => (
                    <li key={item.name}>
                      <Link
                        href={item.href}
                        className="group flex items-center gap-3 font-mono text-sm text-gray-400 transition-colors hover:text-blue-400"
                      >
                        <span className="flex h-6 w-6 items-center justify-center rounded border border-gray-800 group-hover:border-blue-500/30 group-hover:bg-blue-500/5">
                          <i className={`${item.icon} text-xs`} />
                        </span>
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>

          {/* Bottom Bar */}
          <div className="mt-16 flex flex-col items-center justify-between gap-6 border-t border-gray-800 pt-8 font-mono text-sm text-gray-500 sm:flex-row">
            <span>
              © {new Date().getFullYear()} <LogoBrutalish className="text-base" />.
              All rights reserved.
            </span>
            <div className="flex gap-6">
              {['Privacy', 'Terms', 'License'].map((item) => (
                <Link
                  key={item}
                  href="#"
                  className="transition-colors hover:text-blue-400"
                >
                  {item}
                </Link>
              ))}
            </div>
          </div>
        </div>
      </Container>
    </footer>
  )
}

'use client'

import { But<PERSON> } from 'primereact/button'
import { InputText } from 'primereact/inputtext'
import { Dropdown } from 'primereact/dropdown'
import { InputNumber } from 'primereact/inputnumber'
import { InputTextarea } from 'primereact/inputtextarea'
import { useState, useRef } from 'react'

export function DemoGenerateKeyword() {
  const [isMaximized, setIsMaximized] = useState(false)
  const [seedKeyword, setSeedKeyword] = useState('modern kitchen island')
  const [selectedService, setSelectedService] = useState('google')
  const [depth, setDepth] = useState(2)
  const [loading, setLoading] = useState(false)
  const [editableKeywords, setEditableKeywords] = useState(
    'modern kitchen island\n' +
      'modern kitchen island lighting\n' +
      'modern kitchen island ideas\n' +
      'modern kitchen island with seating\n' +
      'modern kitchen island pendant lighting\n' +
      'modern kitchen island designs\n' +
      'modern kitchen island stools\n' +
      'modern kitchen island storage\n' +
      'modern kitchen island dimensions\n' +
      'modern kitchen island colors',
  )
  const [sortState, setSortState] = useState('original')
  const [error, setError] = useState('')
  const originalKeywords = useRef([])

  const services = [
    { label: 'Google Search', value: 'google' },
    { label: 'Google News', value: 'google-news' },
    { label: 'Google Images', value: 'google-images' },
    { label: 'Google Books', value: 'google-books' },
    { label: 'Google Shopping', value: 'google-shopping' },
    { label: 'Google Play', value: 'google-play' },
    { label: 'YouTube', value: 'youtube' },
    { label: 'Bing', value: 'bing' },
    { label: 'Amazon', value: 'amazon' },
    { label: 'Yahoo', value: 'yahoo' },
    { label: 'Baidu', value: 'baidu' },
    { label: 'Yandex', value: 'yandex' },
  ]

  const storeOriginalKeywords = (keywords) => {
    originalKeywords.current = [...keywords]
    setSortState('original')
  }

  const cycleSort = () => {
    const currentKeywords = editableKeywords
      .split('\n')
      .filter((keyword) => keyword.trim() !== '')

    // Store original order if not yet stored
    if (originalKeywords.current.length === 0) {
      storeOriginalKeywords(currentKeywords)
    }

    switch (sortState) {
      case 'original':
        // First click: Sort A-Z
        const ascSorted = [...currentKeywords].sort((a, b) =>
          a.localeCompare(b),
        )
        setEditableKeywords(ascSorted.join('\n'))
        setSortState('asc')
        break

      case 'asc':
        // Second click: Sort Z-A
        const descSorted = [...currentKeywords].sort((a, b) =>
          b.localeCompare(a),
        )
        setEditableKeywords(descSorted.join('\n'))
        setSortState('desc')
        break

      case 'desc':
        // Third click: Restore original order
        setEditableKeywords(originalKeywords.current.join('\n'))
        setSortState('original')
        break
    }
  }

  const generateKeywords = async () => {
    if (!seedKeyword) {
      setError('Please enter a keyword')
      return
    }

    if (!selectedService) {
      setError('Please select a service')
      return
    }

    if (!depth || depth < 1 || depth > 5) {
      setError('Depth must be between 1 and 5')
      return
    }

    setLoading(true)
    setError('')

    try {
      const url = new URL('https://keyword.uix.workers.dev/')
      url.searchParams.append('keyword', seedKeyword)
      url.searchParams.append('service', selectedService)
      url.searchParams.append('depth', depth.toString())

      const response = await fetch(url)
      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let collectedKeywords = new Set()

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value)
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.trim()) {
            try {
              const data = JSON.parse(line)
              if (data.newSuggestions && Array.isArray(data.newSuggestions)) {
                data.newSuggestions.forEach((keyword) => {
                  collectedKeywords.add(keyword)
                })
                // Update textarea in real-time
                const keywordsArray = Array.from(collectedKeywords)
                setEditableKeywords(keywordsArray.join('\n'))
                storeOriginalKeywords(keywordsArray)
              }
            } catch (e) {
              // Handle JSON parse errors silently
            }
          }
        }
      }

      if (collectedKeywords.size === 0) {
        setError('No suggestions found for this keyword')
      }
    } catch (error) {
      setError(`Failed to generate keywords: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const getKeywordCount = () => {
    return editableKeywords
      .split('\n')
      .filter((keyword) => keyword.trim() !== '').length
  }

  const getSortLabel = () => {
    switch (sortState) {
      case 'asc':
        return 'Sort A-Z'
      case 'desc':
        return 'Sort Z-A'
      default:
        return 'Sort'
    }
  }

  const getSortIcon = () => {
    switch (sortState) {
      case 'asc':
        return 'pi pi-sort-alpha-down'
      case 'desc':
        return 'pi pi-sort-alpha-up'
      default:
        return 'pi pi-sort-alt'
    }
  }

  const clearResults = () => {
    setEditableKeywords('')
    setSortState('original')
    originalKeywords.current = []
  }

  return (
    <div className="relative overflow-hidden rounded-lg border border-gray-800 bg-[#0a0b11]">
      {/* Enhanced Header */}
      <div className="border-b border-gray-800 bg-[#0c0d12] px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <i className="pi pi-bolt text-blue-500" />
              <span className="font-mono text-sm text-gray-300">
                Keyword Generator
              </span>
            </div>
            <span className="rounded-full border border-blue-500/20 bg-blue-500/5 px-2 py-0.5 font-mono text-xs text-blue-400">
              v1.0.0
            </span>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={() => setIsMaximized(!isMaximized)}
              className="rounded p-1 text-gray-500 hover:bg-gray-800 hover:text-gray-300"
            >
              <i
                className={`pi ${isMaximized ? 'pi-window-minimize' : 'pi-window-maximize'}`}
              />
            </button>
          </div>
        </div>
      </div>

      {/* Enhanced Main Content */}
      <div className="space-y-2 p-2">
        {/* Input Controls */}
        <div className="flex items-center gap-2">
          <div className="flex-1">
            <InputText
              value={seedKeyword}
              onChange={(e) => setSeedKeyword(e.target.value)}
              placeholder="Enter seed keyword..."
              className="w-full border-gray-800 bg-[#0c0d12] font-mono text-sm text-gray-300 outline-none placeholder:text-gray-600 hover:border-gray-700 focus:border-blue-500/20 focus:ring-1 focus:ring-blue-500/20 focus:ring-offset-0"
            />
          </div>

          {/* <div> */}
          {/*   <label className="mb-1.5 flex items-center gap-2 font-mono text-xs text-gray-500"> */}
          {/*     <span className="text-blue-500">{'>'}</span> */}
          {/*     select.service */}
          {/*   </label> */}
          {/*   <Dropdown */}
          {/*     value={selectedService} */}
          {/*     onChange={(e) => setSelectedService(e.value)} */}
          {/*     options={services} */}
          {/*     optionLabel="label" */}
          {/*     placeholder="Select service" */}
          {/*     className="w-full border-gray-800 bg-[#0c0d12] font-mono text-sm text-gray-300" */}
          {/*   /> */}
          {/* </div> */}

          {/* <div> */}
          {/*   <label className="mb-1.5 flex items-center gap-2 font-mono text-xs text-gray-500"> */}
          {/*     <span className="text-blue-500">{'>'}</span> */}
          {/*     set.depth */}
          {/*   </label> */}
          {/*   <InputNumber */}
          {/*     value={depth} */}
          {/*     onValueChange={(e) => setDepth(e.value)} */}
          {/*     min={1} */}
          {/*     max={5} */}
          {/*     className="w-full border-gray-800 bg-[#0c0d12] font-mono text-sm text-gray-300" */}
          {/*     inputClassName="border-gray-800 bg-[#0c0d12] text-gray-300" */}
          {/*   /> */}
          {/* </div> */}
          {/* Generate Button */}
          <div className="flex-none">
            <Button
              onClick={generateKeywords}
              disabled={loading}
              className={`group relative overflow-hidden border border-blue-500/30 bg-blue-500/5 px-4 py-2 font-mono text-sm text-blue-400 transition-all duration-200 hover:bg-blue-500/10 focus:outline-none focus:ring-2 focus:ring-blue-500/40 focus:ring-offset-2 focus:ring-offset-black active:bg-blue-500/20 disabled:cursor-not-allowed disabled:opacity-70`}
            >
              <span className="flex items-center gap-2">
                {loading ? (
                  <i className="pi pi-spinner animate-spin text-sm" />
                ) : (
                  <span className="h-1.5 w-1.5 animate-pulse rounded-full bg-current" />
                )}
                {loading ? 'Generating...' : 'Generate'}
              </span>
              <div className="group-hover:animate-shine absolute inset-0 -z-10 translate-x-[-100%] bg-gradient-to-r from-transparent via-blue-500/10 to-transparent" />
            </Button>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="rounded-lg border border-red-900/50 bg-red-500/5 p-3 font-mono text-xs text-red-400">
            <div className="flex items-center gap-2">
              <i className="pi pi-exclamation-circle" />
              error: {error}
            </div>
          </div>
        )}

        {/* Results Section */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="ms-1 font-mono text-xs text-gray-500">
              Keywords: {getKeywordCount()}
            </div>
            <div className="flex gap-2">
              <Button
                onClick={cycleSort}
                className="font-mono text-xs text-gray-400 hover:text-blue-400"
                text
                icon={getSortIcon()}
                label={`Sort`}
              />
              <Button
                onClick={clearResults}
                className="font-mono text-xs text-gray-400 hover:text-blue-400"
                text
                icon="pi pi-trash"
                label="Clear"
              />
            </div>
          </div>

          <InputTextarea
            value={editableKeywords}
            onChange={(e) => {
              setEditableKeywords(e.target.value)
              if (sortState === 'original') {
                storeOriginalKeywords(
                  e.target.value.split('\n').filter((k) => k.trim() !== ''),
                )
              }
            }}
            rows={6}
            className="scrollbar-thin scrollbar-track-[#0c0d12] scrollbar-thumb-gray-800 hover:scrollbar-thumb-gray-700 w-full resize-none border-gray-800 bg-[#0c0d12] font-mono text-sm text-gray-300 outline-none focus:border-blue-500/20 focus:ring-1 focus:ring-blue-500/20 focus:ring-offset-0 [&::-webkit-scrollbar-thumb:hover]:bg-gray-700 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-gray-800 [&::-webkit-scrollbar-track]:bg-[#0c0d12] [&::-webkit-scrollbar]:w-2"
            placeholder="// Keywords will appear here..."
          />
        </div>
      </div>

      {/* Status Footer */}
      <div className="border-t border-gray-800 bg-[#0c0d12] px-4 py-2">
        <div className="flex items-center justify-between font-mono text-xs text-gray-500">
          <div className="flex items-center gap-2">
            <span className="h-1.5 w-1.5 animate-pulse rounded-full bg-emerald-500" />
            status: {loading ? 'generating' : 'ready'}
          </div>
          <span>version: 1.0.0</span>
        </div>
      </div>
    </div>
  )
}

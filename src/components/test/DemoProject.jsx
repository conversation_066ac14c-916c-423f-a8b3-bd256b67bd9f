'use client'

import { DataTable } from 'primereact/datatable'
import { Column } from 'primereact/column'
import { But<PERSON> } from 'primereact/button'
import { Tag } from 'primereact/tag'
import { ProgressBar } from 'primereact/progressbar'

export function DemoProject() {
  const projects = [
    {
      name: 'Living Room',
      keywords: 165,
      progress: 79,
      mode: 'Auto',
    },
    {
      name: 'Bedroom Furniture',
      keywords: 142,
      progress: 65,
      mode: 'Manual',
    },
    {
      name: 'Modern Kitchen',
      keywords: 156,
      progress: 45,
      mode: 'Auto',
    },
    {
      name: 'Home Office',
      keywords: 128,
      progress: 82,
      mode: 'Manual',
    },
    {
      name: 'Outdoor Furniture',
      keywords: 98,
      progress: 35,
      mode: 'Auto',
    },
    {
      name: 'Dining Room',
      keywords: 134,
      progress: 58,
      mode: 'Manual',
    },
    {
      name: 'Kids Room',
      keywords: 87,
      progress: 42,
      mode: 'Auto',
    },
    {
      name: 'Storage Solutions',
      keywords: 112,
      progress: 67,
      mode: 'Manual',
    },
    {
      name: 'Wall Decor',
      keywords: 94,
      progress: 51,
      mode: 'Auto',
    },
    {
      name: 'Lighting Fixtures',
      keywords: 76,
      progress: 29,
      mode: 'Manual',
    },
    {
      name: 'Bathroom Design',
      keywords: 108,
      progress: 73,
      mode: 'Auto',
    },
    {
      name: 'Small Spaces',
      keywords: 122,
      progress: 48,
      mode: 'Manual',
    },
    {
      name: 'Entertainment Units',
      keywords: 89,
      progress: 55,
      mode: 'Auto',
    },
    {
      name: 'Home Textiles',
      keywords: 95,
      progress: 61,
      mode: 'Manual',
    },
    {
      name: 'Study Room',
      keywords: 82,
      progress: 38,
      mode: 'Auto',
    },
    {
      name: 'Accent Furniture',
      keywords: 115,
      progress: 44,
      mode: 'Manual',
    },
    {
      name: 'Minimalist Decor',
      keywords: 104,
      progress: 69,
      mode: 'Auto',
    },
    {
      name: 'Guest Room',
      keywords: 78,
      progress: 32,
      mode: 'Manual',
    },
    {
      name: 'Home Entrance',
      keywords: 92,
      progress: 57,
      mode: 'Auto',
    },
    {
      name: 'Window Treatments',
      keywords: 86,
      progress: 41,
      mode: 'Manual',
    },
  ]

  // Custom template for progress bar
  const progressTemplate = (rowData) => {
    return (
      <div className="align-items-center">
        <ProgressBar
          value={rowData.progress}
          showValue={false}
          style={{ height: '9px' }}
        ></ProgressBar>
      </div>
    )
  }

  // Custom template for actions
  const actionsTemplate = () => {
    return (
      <div className="pointer-events-none text-center">
        <Button text icon="pi pi-folder-open" severity="secondary" />
        <Button text icon="pi pi-cog" severity="secondary" />
        <Button text icon="pi pi-file-export" severity="secondary" />
        <Button text icon="pi pi-trash" severity="danger" />
      </div>
    )
  }

  const getModeIcon = (mode) => {
    return `${mode === 'Auto' ? 'pi pi-bolt' : 'pi pi-user'}`
  }

  // Custom template for mode
  const modeTemplate = (rowData) => {
    return (
      <div className={getModeClass(rowData.mode)}>
        <div className="space-x-1 text-nowrap px-2 py-[2px] text-center">
          <i className={getModeIcon(rowData.mode)}></i>
          <span>{rowData.mode}</span>
        </div>
      </div>
    )
  }

  const getModeClass = (mode) => {
    return `text-center rounded-lg py-[2px] ${mode === 'Auto' ? 'text-blue-700 bg-blue-100' : 'bg-gray-200'}`
  }

  return (
    <div>
      <div className="flex items-center justify-between border-gray-200 bg-white px-2 py-2">
        <div className="flex min-w-0 items-center space-x-4 py-4">
          <h1 className="ms-5 flex items-center truncate text-2xl font-semibold tracking-tight text-gray-900">
            Projects Manager
          </h1>
          <Button
            label="New Project"
            icon="pi pi-plus"
            severity="info"
            className="bg-blue-600 px-3 py-2 text-white hover:bg-blue-800"
          />
          {/* <div className="inline-flex cursor-pointer items-center gap-2 text-nowrap rounded-md bg-blue-600 px-3 py-2 font-semibold text-white hover:bg-blue-800"> */}
          {/*   <i className="pi pi-plus" /> */}
          {/*   New Project */}
          {/* </div> */}
        </div>
      </div>
      <DataTable
        value={projects}
        paginator
        showGridlines
        stripedRows
        removableSort
        size="normal"
        rows={10}
      >
        <Column
          field="name"
          header="Name"
          sortable
          className="w-1/3"
          pt={{
            headerTitle: {
              className: 'py-2 ps-2',
            },
            bodyCell: {
              className: '!ps-6',
            },
          }}
          body={(rowData) => (
            <span className="font-semibold text-blue-900 hover:text-blue-950">
              {rowData.name}
            </span>
          )}
        ></Column>
        <Column
          className="text-gray-900"
          field="keywords"
          header="Keywords"
          sortable
          pt={{
            headerContent: {
              className: 'justify-center',
            },
            headerTitle: {
              className: 'py-2 ps-2',
            },
            bodyCell: {
              className: '!px-4 text-center',
            },
          }}
        ></Column>
        <Column
          field="progress"
          header="Progress"
          body={progressTemplate}
          sortable
          pt={{
            headerContent: {
              className: 'justify-center',
            },
            headerTitle: {
              className: 'py-2 ps-2',
            },
          }}
        ></Column>
        <Column
          className="text-gray-900"
          field="mode"
          header="Mode"
          body={modeTemplate}
          sortable
          pt={{
            headerContent: {
              className: 'justify-center',
            },
          }}
        ></Column>
        <Column
          className="text-gray-900"
          header="Actions"
          body={actionsTemplate}
          pt={{
            headerContent: {
              className: 'justify-center',
            },
            headerTitle: {
              className: 'py-2 ps-2',
            },
          }}
        ></Column>
      </DataTable>
    </div>
  )
}

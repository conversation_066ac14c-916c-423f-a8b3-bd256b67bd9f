'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { DemoPosting } from '@/components/DemoPostingAnimationTerm'
import { DemoGenerateKeyword } from '@/components/DemoGenerateKeywordTerm'
import { DemoUploadImages } from '@/components/DemoUploadImagesTerm'
import { Container } from '@/components/Container'

const features = [
  {
    name: 'Generate Keywords',
    summary:
      'Generate targeted keywords instantly for SEO and content optimization.',
    description:
      'Streamline your keyword research process with our powerful keyword generator. Simply enter a seed keyword, select your preferred search service, and set the depth level to discover relevant keyword variations and long-tail phrases.',
    demo: <DemoGenerateKeyword />,
    icon: 'pi pi-chart-line',
    color: 'from-blue-500/20 to-cyan-500/20',
  },
  {
    name: 'Generate Content',
    summary:
      'Create optimized blog posts and articles with automated content generation.',
    description:
      'Transform your keywords into fully-formed content with our intelligent post generator. The system automatically creates engaging titles, generates comprehensive articles, and handles image uploads - all while maintaining SEO best practices.',
    demo: <DemoPosting />,
    icon: 'pi pi-file-edit',
    color: 'from-purple-500/20 to-pink-500/20',
  },
  {
    name: 'Upload Images',
    summary: 'Effortlessly upload and manage multiple images for your content.',
    description:
      'Upload multiple high-quality images simultaneously, preview them in a clean gallery layout, and select the perfect visuals for your posts. Supports batch uploading, image selection, and seamless integration with your content creation process.',
    demo: <DemoUploadImages />,
    icon: 'pi pi-images',
    color: 'from-orange-500/20 to-yellow-500/20',
  },
]

// Static Feature Component
function StaticFeature({ feature, isActive }) {
  return (
    <div className="relative space-y-6">
      <div className="inline-flex h-12 w-12 items-center justify-center rounded-lg border border-blue-500/30 bg-blue-500/5">
        <i className={`${feature.icon} text-xl text-blue-400`} />
      </div>
      <div className="space-y-2">
        <div className="font-mono text-xs text-blue-500">
          {'> feature.description'}
        </div>
        <h3 className="font-mono text-xl text-white">{feature.summary}</h3>
      </div>
      <p className="font-mono text-sm leading-relaxed text-gray-400">
        {feature.description}
      </p>
      <button className="group flex items-center gap-2 font-mono text-sm text-blue-400 hover:text-blue-300">
        <span className="h-1 w-1 rounded-full bg-blue-500" />
        learn.more()
        <span className="transition-transform group-hover:translate-x-0.5">
          →
        </span>
      </button>
    </div>
  )
}

export function SecondaryFeatures() {
  const [activeTab, setActiveTab] = useState(0)
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
  }, [])

  // Static render (server-side and initial client-side)
  if (!isMounted) {
    return (
      <section className="relative overflow-hidden bg-[#0a0b11] py-24 sm:py-32">
        <Container>
          {/* Static Header */}
          <div className="relative mx-auto max-w-2xl text-center">
            <div className="inline-flex items-center gap-2 rounded-full border border-blue-500/20 bg-blue-500/5 px-3 py-1 font-mono text-sm text-blue-400">
              <span className="h-1.5 w-1.5 rounded-full bg-blue-500" />
              system.features.initialize
            </div>
            <h2 className="mt-8 font-mono text-3xl font-medium tracking-tight text-white">
              Power Meets Simplicity
            </h2>
            <p className="mt-4 font-mono text-lg text-gray-400">
              <span className="text-blue-500">{'> '}</span>
              ai.automate(complexity) && user.enjoy(simplicity)
            </p>
          </div>

          {/* Static Tabs */}
          <div className="mt-16 lg:mt-20">
            <div className="flex justify-center">
              <div className="inline-flex rounded-lg border border-blue-900/30 bg-[#0c0d12]/80 p-1 backdrop-blur-sm">
                {features.map((feature, index) => (
                  <button
                    key={feature.name}
                    className={`relative rounded-md px-6 py-2.5 font-mono text-sm transition-all ${
                      index === 0 ? 'text-blue-400' : 'text-gray-400'
                    }`}
                  >
                    <span className="relative flex items-center gap-2">
                      <i className={feature.icon} />
                      {feature.name}
                    </span>
                  </button>
                ))}
              </div>
            </div>

            {/* Static Content */}
            <div className="relative mt-16">
              <div className="grid gap-8 lg:grid-cols-2">
                <StaticFeature feature={features[0]} isActive={true} />
                <div className="relative overflow-hidden rounded-lg border border-blue-900/30 bg-[#0c0d12]/80 p-8 backdrop-blur-sm">
                  <div className="relative">{features[0].demo}</div>
                  <div className="absolute left-0 top-0 h-16 w-[1px] bg-gradient-to-b from-blue-500/50 to-transparent" />
                  <div className="absolute left-0 top-0 h-[1px] w-16 bg-gradient-to-r from-blue-500/50 to-transparent" />
                  <div className="absolute right-0 top-0 h-16 w-[1px] bg-gradient-to-b from-blue-500/50 to-transparent" />
                  <div className="absolute right-0 top-0 h-[1px] w-16 bg-gradient-to-r from-transparent to-blue-500/50" />
                </div>
              </div>
            </div>
          </div>
        </Container>
      </section>
    )
  }

  // Client-side render with animations
  return (
    <section className="relative overflow-hidden bg-[#0a0b11] py-24 sm:py-32">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-[linear-gradient(to_right,#0066ff08_1px,transparent_1px),linear-gradient(to_bottom,#0066ff08_1px,transparent_1px)] bg-[size:24px_24px]" />
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="absolute left-0 top-0 -z-10 h-96 w-96 rounded-full bg-blue-500/10 blur-[128px]"
      />
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="absolute bottom-0 right-0 -z-10 h-96 w-96 rounded-full bg-violet-500/10 blur-[128px]"
      />

      <Container>
        {/* Animated Header */}
        <div className="relative mx-auto max-w-2xl text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="inline-flex items-center gap-2 rounded-full border border-blue-500/20 bg-blue-500/5 px-3 py-1 font-mono text-sm text-blue-400"
          >
            <span className="h-1.5 w-1.5 animate-pulse rounded-full bg-blue-500" />
            WordPress Superpowers
          </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="mt-8 font-mono text-4xl font-bold text-gray-100 sm:text-5xl"
          >
            <span className="bg-gradient-to-r from-blue-400 to-indigo-400 bg-clip-text text-transparent">
              Power
            </span>{' '}
            Meets Simplicity
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="mt-4 font-mono text-lg text-gray-400"
          >
            Let AI-powered automation handle the complexity, while you enjoy the
            simplicity of WordPress publishing.
          </motion.p>
        </div>

        {/* Animated Features */}
        <div className="mt-16 lg:mt-20">
          {/* Animated Tabs */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex justify-center"
          >
            <div className="inline-flex rounded-lg border border-blue-900/30 bg-[#0c0d12]/80 p-1 backdrop-blur-sm">
              {features.map((feature, index) => (
                <button
                  key={feature.name}
                  onClick={() => setActiveTab(index)}
                  className={`relative rounded-md px-6 py-2.5 font-mono text-sm transition-all ${
                    activeTab === index
                      ? 'text-blue-400'
                      : 'text-gray-400 hover:text-blue-400'
                  }`}
                >
                  {activeTab === index && (
                    <motion.div
                      layoutId="activeTab"
                      className="absolute inset-0 rounded-md bg-blue-500/10"
                      transition={{
                        type: 'spring',
                        bounce: 0.2,
                        duration: 0.6,
                      }}
                    />
                  )}
                  <span className="relative flex items-center gap-2">
                    <i className={feature.icon} />
                    <span className="hidden sm:block">{feature.name}</span>
                  </span>
                </button>
              ))}
            </div>
          </motion.div>

          {/* Animated Content */}
          <div className="relative mt-16">
            <AnimatePresence mode="wait">
              {features.map(
                (feature, index) =>
                  activeTab === index && (
                    <motion.div
                      key={feature.name}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.3 }}
                      className="grid items-center gap-8 lg:grid-cols-2"
                    >
                      <AnimatedFeature feature={feature} />
                      <div className="relative h-max overflow-hidden rounded-lg border border-blue-900/30 backdrop-blur-sm">
                        <div className="relative">{feature.demo}</div>
                        <div className="absolute left-0 top-0 h-16 w-[1px] bg-gradient-to-b from-blue-500/50 to-transparent" />
                        <div className="absolute left-0 top-0 h-[1px] w-16 bg-gradient-to-r from-blue-500/50 to-transparent" />
                        <div className="absolute right-0 top-0 h-16 w-[1px] bg-gradient-to-b from-blue-500/50 to-transparent" />
                        <div className="absolute right-0 top-0 h-[1px] w-16 bg-gradient-to-r from-transparent to-blue-500/50" />
                      </div>
                    </motion.div>
                  ),
              )}
            </AnimatePresence>
          </div>
        </div>
      </Container>
    </section>
  )
}

// Animated Feature Component
function AnimatedFeature({ feature }) {
  return (
    <div className="relative space-y-6 text-center lg:text-left">
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className="inline-flex h-12 w-12 items-center justify-center rounded-lg border border-blue-500/30 bg-blue-500/5"
      >
        <i className={`${feature.icon} text-xl text-blue-400`} />
      </motion.div>
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="space-y-2"
      >
        <h3 className="font-mono text-xl text-white">{feature.summary}</h3>
      </motion.div>
      <motion.p
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="font-mono text-sm leading-relaxed text-gray-400"
      >
        {feature.description}
      </motion.p>
      {/* <motion.button */}
      {/*   initial={{ opacity: 0, y: 10 }} */}
      {/*   animate={{ opacity: 1, y: 0 }} */}
      {/*   transition={{ delay: 0.3 }} */}
      {/*   className="group flex items-center gap-2 font-mono text-sm text-blue-400 hover:text-blue-300" */}
      {/* > */}
      {/*   <span className="h-1 w-1 rounded-full bg-blue-500" /> */}
      {/*   learn.more() */}
      {/*   <span className="transition-transform group-hover:translate-x-0.5"> */}
      {/*     → */}
      {/*   </span> */}
      {/* </motion.button> */}
    </div>
  )
}

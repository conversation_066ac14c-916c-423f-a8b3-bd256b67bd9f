'use client'

export function DemoPosting() {
  return (
    <div className="relative mt-8 flex flex-col rounded-lg bg-white shadow-lg lg:mt-0">
      <div className="absolute -inset-x-8 top-0 h-px bg-slate-900/15 [mask-image:linear-gradient(to_left,transparent,white_4rem,white_calc(100%-4rem),transparent)]"></div>
      <div className="absolute -inset-y-8 left-0 w-px bg-slate-900/15 [mask-image:linear-gradient(to_top,transparent,white_4rem,white_calc(100%-4rem),transparent)]"></div>
      <div className="absolute -inset-y-8 right-0 w-px bg-slate-900/15 [mask-image:linear-gradient(to_top,transparent,white_4rem,white_calc(100%-4rem),transparent)]"></div>
      <div className="absolute bottom-full left-16 -mb-px flex h-8 items-end overflow-hidden">
        <div className="-mb-px flex h-[2px] w-56">
          <div className="blur-xs w-full flex-none [background-image:linear-gradient(90deg,rgba(56,189,248,0)_0%,#0EA5E9_32.29%,rgba(236,72,153,0.3)_67.19%,rgba(236,72,153,0)_100%)]"></div>
          <div className="-ml-[100%] w-full flex-none blur-[1px] [background-image:linear-gradient(90deg,rgba(56,189,248,0)_0%,#0EA5E9_32.29%,rgba(236,72,153,0.3)_67.19%,rgba(236,72,153,0)_100%)]"></div>
        </div>
      </div>

      {/* Header */}
      <div className="flex items-center justify-between border-b p-4">
        <div className="flex items-center gap-2">
          <i className="pi pi-send text-lg" />
          <span className="text-lg">Post Keyword</span>
        </div>
        <div className="flex gap-4">
          <i className="pi pi-window-minimize cursor-pointer" />
          <i className="pi pi-times cursor-pointer" />
        </div>
      </div>

      {/* Main Content */}
      <div className="p-6">
        <div className="rounded-lg border shadow-sm">
          {/* Blue progress bar */}
          <div className="h-1 w-full rounded-t-lg bg-blue-600" />

          <div className="p-4">
            {/* Title and badges */}
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-800">
                modern kitchen island
              </h2>
              <div className="flex items-center gap-2">
                <span className="inline-flex items-center rounded-md bg-blue-100 px-2 font-semibold text-blue-800">
                  <i className="pi pi-images me-1" /> 5
                </span>
                <span className="rounded-md bg-blue-100 px-2">
                  <i className="pi pi-check align-[-1px] font-semibold text-blue-800" />
                </span>
                <i className="pi pi-chevron-down cursor-pointer" />
              </div>
            </div>

            {/* Status messages */}
            <div className="mt-4 space-y-4">
              <div className="font-semibold text-blue-800">
                <i className="pi pi-check-circle me-2" />
                New post (Lighting Strategies That Elevate a Modern Kitchen
                Island) created.
              </div>

              <hr className="border-gray-200" />

              <div className="inline-flex items-center font-semibold text-blue-800">
                <i className="pi pi-check-circle me-2" />
                Images upload finished
                <span className="ml-2 text-nowrap rounded-md bg-blue-100 px-2">
                  <i className="pi pi-images me-1 align-[-1px]" /> 5 / 5
                </span>
              </div>

              <hr className="border-gray-200" />

              <div className="font-semibold text-blue-800">
                <i className="pi pi-check-circle me-2" />
                Post published (1431 words).
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="mt-auto border-t p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="cursor-pointer rounded-full px-3 py-2 hover:bg-gray-100">
              <i className="pi pi-pencil text-gray-600" />
            </div>
            <div className="cursor-pointer rounded-full px-3 py-2 hover:bg-gray-100">
              <i className="pi pi-external-link text-gray-600" />
            </div>
          </div>
          <div className="cursor-pointer rounded-lg bg-gray-100 px-4 py-2 hover:bg-gray-200">
            Close
          </div>
        </div>
      </div>
    </div>
  )
}

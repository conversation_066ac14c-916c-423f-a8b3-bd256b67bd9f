'use client'

import { Logo } from '@/components/LogoBolt'
import { DemoPosting } from '@/components/DemoPostingAnimationTerm'

export function HeroTile() {
  return (
    <div className="relative overflow-hidden bg-[#0a0b0f]">
      {' '}
      {/* Darker background */}
      {/* Enhanced cyber grid with glow */}
      <div className="absolute inset-0 bg-[linear-gradient(to_right,#0066ff08_1px,transparent_1px),linear-gradient(to_bottom,#0066ff08_1px,transparent_1px)] bg-[size:24px_24px]" />
      {/* Ambient glow effects */}
      <div className="absolute left-1/4 top-0 h-96 w-96 rounded-full bg-blue-500/10 blur-[128px]" />
      <div className="absolute bottom-0 right-1/4 h-96 w-96 rounded-full bg-indigo-500/10 blur-[128px]" />
      <div className="relative mx-auto max-w-7xl px-6">
        <div className="flex min-h-[90vh] flex-col items-center justify-center gap-1 py-20 lg:flex-row lg:justify-between lg:py-32">
          {/* Left Content */}
          <div className="relative z-10 max-w-4xl pt-10 lg:max-w-md">
            {/* Enhanced terminal badge */}
            <div className="inline-flex items-center space-x-2 rounded-sm border border-blue-500/20 bg-blue-500/5 px-3 py-1 font-mono text-sm text-gray-400">
              <span className="h-1.5 w-1.5 animate-pulse rounded-full bg-blue-500" />
              <span>WordPress Plugin</span>
            </div>

            <h1 className="mt-6 font-mono text-[clamp(2.5rem,5vw,4rem)] font-medium leading-tight text-white">
              WordPress{' '}
              <span className="relative inline-block text-blue-400">
                Superpowers
                <div className="absolute -bottom-1 left-0 h-[1px] w-full bg-gradient-to-r from-transparent via-blue-500 to-transparent" />
                <div className="absolute -bottom-2 left-0 h-[1px] w-full bg-gradient-to-r from-transparent via-blue-500/30 to-transparent" />
              </span>
            </h1>

            <p className="mt-8 font-mono text-lg leading-relaxed text-gray-400">
              <span className="font-light">
                Advanced content automation meets WordPress simplicity. Discover
                what your WordPress site can achieve with next-generation
                content publishing.
              </span>
            </p>

            <div className="mt-10 flex items-center gap-6">
              <a
                href="#"
                className="group relative overflow-hidden border border-blue-500/50 bg-blue-500/10 px-8 py-3 font-mono text-sm text-blue-400 transition-all hover:bg-blue-500/20 hover:shadow-[0_0_20px_rgba(0,100,255,0.3)]"
              >
                <span className="flex items-center gap-2">
                  <span className="h-1 w-1 animate-pulse rounded-full bg-blue-500" />
                  Get started
                </span>
                <div className="group-hover:animate-shine absolute inset-0 -z-10 translate-x-[-100%] bg-gradient-to-r from-transparent via-blue-500/10 to-transparent" />
              </a>
              <a
                href="#"
                className="font-mono text-sm text-gray-500 transition-all hover:translate-x-0.5 hover:text-blue-400"
              >
                learn more
              </a>
            </div>

            {/* Enhanced stats display */}
            <div className="mt-16 flex gap-12 border-t border-blue-900/30 pt-8">
              {[
                ['10K+', 'Active users'],
                ['500K+', 'Posts generated'],
              ].map(([stat, label]) => (
                <div key={stat} className="group font-mono">
                  <div className="text-2xl font-semibold text-blue-400 transition-all group-hover:translate-y-[-2px] group-hover:text-blue-300">
                    {stat}
                  </div>
                  <div className="mt-1 text-sm text-gray-500">{label}</div>
                </div>
              ))}
            </div>
          </div>

          {/* Right Content - Enhanced Terminal */}
          <div className="relative mt-6 w-full ps-0 lg:mt-0 lg:ps-6">
            <div className="absolute -inset-x-20 -top-20 -z-10">
              <div className="animate-pulse-slow absolute right-0 top-1/2 h-72 w-72 -translate-y-1/2 bg-blue-500/10 blur-[100px]" />
            </div>

            {/* Terminal wrapper with enhanced styling */}
            <div className="relative rounded-sm border border-blue-900/30 bg-[#0c0d12]/80 shadow-[0_0_30px_rgba(0,0,0,0.3)] backdrop-blur-xl">
              {/* Terminal header */}
              <div className="flex h-10 items-center justify-between border-b border-blue-900/30 bg-[#0c0d12]/90 px-4">
                <Logo className="h-6" />
                <div className="flex gap-1.5">
                  {[...Array(3)].map((_, i) => (
                    <div
                      key={i}
                      className="h-2 w-2 rounded-full border border-gray-700 transition-colors hover:border-blue-500/50"
                    />
                  ))}
                </div>
              </div>

              <DemoPosting />

              {/* Enhanced corner accents */}
              <div className="absolute left-0 top-0 h-16 w-[1px] bg-gradient-to-b from-blue-500/50 to-transparent" />
              <div className="absolute left-0 top-0 h-[1px] w-16 bg-gradient-to-r from-blue-500/50 to-transparent" />
              <div className="absolute right-0 top-0 h-16 w-[1px] bg-gradient-to-b from-blue-500/50 to-transparent" />
              <div className="absolute right-0 top-0 h-[1px] w-16 bg-gradient-to-r from-transparent to-blue-500/50" />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

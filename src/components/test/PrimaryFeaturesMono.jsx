'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { DemoProject } from '@/components/DemoProjectTerm'
import { DemoKeyword } from '@/components/DemoKeyword'
import { DemoAttachment } from '@/components/DemoAttachment'
import { DemoTemplate } from '@/components/DemoTemplate'

const features = [
  {
    title: 'Projects',
    description:
      'Manage your content projects efficiently by organizing keywords in one place. Each project can have its own unique settings for AI-generated content and automated posting strategies.',
    demo: <DemoProject />,
    icon: 'pi pi-folder',
  },
  {
    title: 'Keywords',
    description:
      'Centralize your target keywords for content generation. Organize and manage the keywords that will be used to generate engaging blog posts.',
    demo: <DemoKeyword />,
    icon: 'pi pi-key',
  },
  {
    title: 'Images',
    description:
      'Control how images are sourced, processed, and integrated into your posts across your WordPress content.',
    demo: <DemoAttachment />,
    icon: 'pi pi-image',
  },
  {
    title: 'Templates',
    description:
      'A flexible template management system that guides the AI in creating structured, consistent content.',
    demo: <DemoTemplate />,
    icon: 'pi pi-file',
  },
]

export function PrimaryFeatures() {
  const [activeTab, setActiveTab] = useState(0)

  return (
    <section className="relative bg-gray-50 py-24">
      {/* Minimal terminal-like grid */}
      <div className="absolute inset-0 bg-[linear-gradient(to_right,#8881_1px,transparent_1px),linear-gradient(to_bottom,#8881_1px,transparent_1px)] bg-[size:40px_40px]" />

      <div className="relative mx-auto max-w-7xl px-6">
        {/* Terminal-style header */}
        <div className="mx-auto max-w-2xl text-center">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="inline-flex items-center space-x-2 font-mono text-xs text-gray-500"
          >
            <span className="h-1 w-1 rounded-full bg-blue-500" />
            <span>system.features.init()</span>
          </motion.div>

          <h2 className="mt-6 font-mono text-3xl font-medium tracking-tight text-gray-900">
            The{' '}
            <span className="relative text-blue-600">
              <span className="relative">Superhuman</span>
              <motion.span
                className="absolute -bottom-1 left-0 h-[1px] w-full bg-blue-500"
                initial={{ scaleX: 0 }}
                animate={{ scaleX: 1 }}
                transition={{ duration: 0.8, delay: 0.2 }}
              />
            </span>{' '}
            Way to Create
          </h2>

          <p className="mt-4 font-mono text-sm text-gray-600">
            $ with great power comes zero responsibility
          </p>
        </div>

        <div className="mt-16">
          {/* Terminal-style tabs */}
          <div className="grid gap-3 lg:grid-cols-4">
            {features.map((feature, index) => (
              <motion.button
                key={feature.title}
                onClick={() => setActiveTab(index)}
                className={`group relative border bg-white px-4 py-3 text-left transition-colors ${
                  activeTab === index
                    ? 'border-blue-500 shadow-[inset_0_0_0_1px_rgb(59,130,246)]'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-3">
                  <span
                    className={`font-mono text-xs ${
                      activeTab === index ? 'text-blue-500' : 'text-gray-400'
                    }`}
                  >
                    {String(index + 1).padStart(2, '0')}
                  </span>
                  <i
                    className={`${feature.icon} text-sm ${
                      activeTab === index ? 'text-blue-500' : 'text-gray-400'
                    }`}
                  />
                  <span
                    className={`font-mono text-sm ${
                      activeTab === index ? 'text-blue-500' : 'text-gray-600'
                    }`}
                  >
                    {feature.title}
                  </span>
                </div>
              </motion.button>
            ))}
          </div>

          {/* Terminal-style content display */}
          <div className="relative mt-6">
            <div className="relative rounded-none border border-gray-200 bg-white">
              {/* Terminal header */}
              <div className="flex h-8 items-center justify-between border-b border-gray-200 bg-gray-50 px-4">
                <div className="flex items-center gap-2">
                  <span className="font-mono text-xs text-gray-400">
                    {`> ${features[activeTab].title}.render()`}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="h-1 w-1 rounded-full bg-blue-500" />
                  <span className="font-mono text-xs text-gray-400">
                    active
                  </span>
                </div>
              </div>

              {/* Content */}
              <AnimatePresence mode="wait">
                <motion.div
                  key={activeTab}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="p-6"
                >
                  {features[activeTab].demo}
                </motion.div>
              </AnimatePresence>
            </div>

            {/* Terminal status line */}
            <div className="mt-2 flex justify-between px-1">
              <span className="font-mono text-xs text-gray-400">
                system:active
              </span>
              <span className="font-mono text-xs text-gray-400">
                {`process: ${activeTab + 1}/4`}
              </span>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

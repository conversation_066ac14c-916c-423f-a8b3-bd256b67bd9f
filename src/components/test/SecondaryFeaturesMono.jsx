'use client'

import { useState } from 'react'
import { DemoPosting } from '@/components/DemoPostingAnimation'
import { DemoGenerateKeyword } from '@/components/DemoGenerateKeyword'
import { DemoUploadImages } from '@/components/DemoUploadImages'

import { Container } from '@/components/Container'

const features = [
  {
    name: 'Generate Keywords',
    summary:
      'Generate targeted keywords instantly for SEO and content optimization.',
    description:
      'Streamline your keyword research process with our powerful keyword generator. Simply enter a seed keyword, select your preferred search service, and set the depth level to discover relevant keyword variations and long-tail phrases.',
    demo: <DemoGenerateKeyword />,
    icon: 'pi pi-chart-line',
    color: 'from-blue-500/20 to-cyan-500/20',
  },
  {
    name: 'Generate Content',
    summary:
      'Create optimized blog posts and articles with automated content generation.',
    description:
      'Transform your keywords into fully-formed content with our intelligent post generator. The system automatically creates engaging titles, generates comprehensive articles, and handles image uploads - all while maintaining SEO best practices.',
    demo: <DemoPosting />,
    icon: 'pi pi-file-edit',
    color: 'from-purple-500/20 to-pink-500/20',
  },
  {
    name: 'Upload Images',
    summary: 'Effortlessly upload and manage multiple images for your content.',
    description:
      'Upload multiple high-quality images simultaneously, preview them in a clean gallery layout, and select the perfect visuals for your posts. Supports batch uploading, image selection, and seamless integration with your content creation process.',
    demo: <DemoUploadImages />,
    icon: 'pi pi-images',
    color: 'from-orange-500/20 to-yellow-500/20',
  },
]

export function SecondaryFeatures() {
  const [activeTab, setActiveTab] = useState(0)

  return (
    <section className="relative overflow-hidden bg-gray-900 py-24 sm:py-32">
      {/* Background effects */}
      <div className="absolute inset-0 bg-[linear-gradient(to_right,#8882_1px,transparent_1px),linear-gradient(to_bottom,#8882_1px,transparent_1px)] bg-[size:24px_24px] opacity-25" />

      <Container>
        {/* Header */}
        <div className="relative mx-auto max-w-2xl text-center">
          <div className="inline-flex items-center rounded-full border border-gray-800 bg-gray-800/50 px-3 py-1 text-sm text-gray-300">
            <span className="mr-2 inline-block h-1.5 w-1.5 rounded-full bg-blue-500" />
            Features
          </div>
          <h2 className="mt-8 text-3xl font-medium tracking-tight text-white sm:text-4xl">
            Power Meets Simplicity
          </h2>
          <p className="mt-4 text-lg text-gray-300">
            Let AI-powered automation handle the complexity, while you enjoy the
            simplicity of WordPress publishing.
          </p>
        </div>

        {/* Features */}
        <div className="mt-16 lg:mt-20">
          {/* Tabs */}
          <div className="flex justify-center">
            <div className="inline-flex rounded-full bg-gray-800/50 p-1 backdrop-blur-sm">
              {features.map((feature, index) => (
                <button
                  key={feature.name}
                  onClick={() => setActiveTab(index)}
                  className={`relative rounded-full px-6 py-2.5 text-sm font-medium transition-all ${
                    activeTab === index
                      ? 'text-white'
                      : 'text-gray-400 hover:text-white'
                  } `}
                >
                  {activeTab === index && (
                    <div className="absolute inset-0 rounded-full bg-gray-800" />
                  )}
                  <span className="relative flex items-center gap-2">
                    <i className={feature.icon} />
                    {feature.name}
                  </span>
                </button>
              ))}
            </div>
          </div>

          {/* Content */}
          <div className="relative mt-16">
            {features.map((feature, index) => (
              <div
                key={feature.name}
                className={`absolute inset-0 transition duration-500 ${
                  activeTab === index
                    ? 'transform-none opacity-100'
                    : 'translate-y-4 opacity-0'
                } `}
                style={{ pointerEvents: activeTab === index ? 'auto' : 'none' }}
              >
                <div className="grid gap-8 lg:grid-cols-2">
                  {/* Description */}
                  <div className="relative">
                    <div
                      className={`absolute -inset-4 rounded-2xl bg-gradient-to-b ${feature.color} opacity-20 blur-2xl`}
                    />
                    <div className="relative space-y-6">
                      <div className="inline-flex h-12 w-12 items-center justify-center rounded-xl bg-gray-800">
                        <i className={`${feature.icon} text-xl text-white`} />
                      </div>
                      <h3 className="text-2xl font-semibold text-white">
                        {feature.summary}
                      </h3>
                      <p className="leading-relaxed text-gray-300">
                        {feature.description}
                      </p>
                      <div className="flex gap-4">
                        <button className="group flex items-center gap-2 text-sm font-medium text-blue-400 hover:text-blue-300">
                          Learn more
                          <span className="transition-transform group-hover:translate-x-0.5">
                            →
                          </span>
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Demo */}
                  <div className="relative rounded-2xl bg-gray-800/50 p-8 backdrop-blur-sm">
                    <div className="absolute inset-0 rounded-2xl bg-gradient-to-b from-gray-800/50 to-transparent" />
                    <div className="relative">{feature.demo}</div>
                    {/* Corner accents */}
                    <div className="absolute left-0 top-0 h-px w-24 bg-gradient-to-r from-blue-500/0 via-blue-500/70 to-blue-500/0" />
                    <div className="absolute left-0 top-0 h-24 w-px bg-gradient-to-b from-blue-500/70 to-transparent" />
                    <div className="absolute right-0 top-0 h-px w-24 bg-gradient-to-l from-blue-500/0 via-blue-500/70 to-blue-500/0" />
                    <div className="absolute right-0 top-0 h-24 w-px bg-gradient-to-b from-blue-500/70 to-transparent" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </Container>
    </section>
  )
}

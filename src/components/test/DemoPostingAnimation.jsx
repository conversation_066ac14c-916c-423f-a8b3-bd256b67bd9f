'use client'
import React, { useState, useEffect } from 'react'
import { Panel } from 'primereact/panel'
import { Button } from 'primereact/button'

// Dummy data
const dummyPosts = [
  {
    keyword: 'modern kitchen island',
    title:
      'How to Choose the Perfect Countertop for Your Modern Kitchen Island',
    imageCount: 5,
    wordCount: 1050,
  },
  {
    keyword: 'minimalist living room',
    title: '10 Essential Tips for Designing a Minimalist Living Room',
    imageCount: 7,
    wordCount: 1200,
  },
  {
    keyword: 'small bathroom ideas',
    title: 'Creative Storage Solutions for Small Bathroom Spaces',
    imageCount: 4,
    wordCount: 950,
  },
  {
    keyword: 'outdoor patio design',
    title: 'Transform Your Backyard: Modern Patio Design Ideas',
    imageCount: 6,
    wordCount: 1150,
  },
]

export function DemoPosting() {
  const [isMaximized, setIsMaximized] = useState(false)
  const [currentPost, setCurrentPost] = useState(dummyPosts[0])
  const [status, setStatus] = useState({
    isCreating: true,
    isCreated: false,
    uploadProgress: 0,
    isUploading: false,
    isUploaded: false,
    isGenerating: false,
    isPublished: false,
    wordCount: 0,
  })

  const startProcess = () => {
    // Reset status
    setStatus({
      isCreating: true,
      isCreated: false,
      uploadProgress: 0,
      isUploading: false,
      isUploaded: false,
      isGenerating: false,
      isPublished: false,
      wordCount: 0,
    })

    // Randomly select a new post
    const randomPost = dummyPosts[Math.floor(Math.random() * dummyPosts.length)]
    setCurrentPost(randomPost)

    // Start the animation process
    setTimeout(() => {
      setStatus((prev) => ({
        ...prev,
        isCreating: false,
        isCreated: true,
        isUploading: true,
      }))
    }, 1000)

    const uploadInterval = setInterval(() => {
      setStatus((prev) => {
        if (prev.uploadProgress >= randomPost.imageCount) {
          clearInterval(uploadInterval)
          return {
            ...prev,
            isUploading: false,
            isUploaded: true,
            isGenerating: true,
          }
        }
        return {
          ...prev,
          uploadProgress: prev.uploadProgress + 1,
        }
      })
    }, 800)

    // Add delay before publishing to show generating state
    setTimeout(
      () => {
        setStatus((prev) => ({
          ...prev,
          isGenerating: true,
        }))

        // Add another setTimeout for publishing
        setTimeout(() => {
          setStatus((prev) => ({
            ...prev,
            isGenerating: false,
            isPublished: true,
            wordCount: randomPost.wordCount,
          }))
        }, 3000) // Show generating state for 3 seconds
      },
      randomPost.imageCount * 800 + 1000,
    ) // Start after images upload
  }

  useEffect(() => {
    startProcess()
  }, [])

  const headerTemplate = (options) => {
    const className = `${options.className} flex justify-between bg-white rounded-none`

    return (
      <div className={className}>
        <div className="flex items-center gap-2">
          <h2 className="text-lg font-semibold text-gray-800">
            {currentPost.keyword}
          </h2>
        </div>
        <div>
          <div className="flex items-center gap-2">
            {status.uploadProgress > 0 && (
              <span className="inline-flex items-center rounded-md bg-blue-100 px-2 font-semibold text-blue-800">
                <i className="pi pi-images me-1" /> {status.uploadProgress}
              </span>
            )}
            {status.isPublished ? (
              <span className="rounded-md bg-blue-100 px-2">
                <i className="pi pi-check align-[-1px] font-semibold text-blue-800" />
              </span>
            ) : (
              <span className="rounded-md px-2">
                <i className="pi pi-spinner pi-spin align-[-1px] font-semibold text-blue-800" />
              </span>
            )}
          </div>
        </div>
      </div>
    )
  }

  const footerTemplate = (options) => {
    const className = `${options.className} flex flex-wrap items-center justify-between gap-3`

    return (
      <div className={className}>
        <div className="align-items-center pointer-events-none flex gap-2">
          {status.isPublished && (
            <>
              <Button icon="pi pi-pencil" text severity="secondary" />
              <Button icon="pi pi-external-link" text severity="secondary" />
            </>
          )}
        </div>
        <Button
          label="Refresh"
          severity="secondary"
          outlined
          onClick={startProcess}
          disabled={!status.isPublished}
        />
      </div>
    )
  }

  return (
    <div className="rounded-lg bg-white shadow-lg">
      {/* Header */}
      <div className="bg-surface-50 sticky top-0 z-[99999] inline-flex w-full items-center justify-between rounded-t-lg border-b pe-3 shadow-sm">
        <div className="inline-flex items-center space-x-2 truncate p-4 text-lg">
          <i className="pi pi-send px-2 text-lg" />
          Post Keywords
        </div>
        <div className="flex">
          <i
            className={`pi ${isMaximized ? 'pi-window-minimize' : 'pi-window-maximize'} cursor-pointer px-2 text-sm text-gray-700`}
            onClick={() => setIsMaximized(!isMaximized)}
          />
          <i className="pi pi-times cursor-pointer px-2 text-sm text-gray-700" />
        </div>
      </div>

      {/* Main Content */}
      <div className="p-3">
        <Panel headerTemplate={headerTemplate} footerTemplate={footerTemplate}>
          <div className="p-1">
            <div className="mt-0 space-y-4">
              {status.isCreating && (
                <div className="font-semibold text-blue-800">
                  <i className="pi pi-spinner pi-spin me-2" />
                  Creating post...
                </div>
              )}

              {status.isCreated && (
                <div className="font-semibold text-blue-800">
                  <i className="pi pi-check-circle me-2" />
                  New post ({currentPost.title}) created.
                </div>
              )}

              {status.isUploading && (
                <>
                  <hr className="border-gray-200" />
                  <div className="font-semibold text-blue-800">
                    <i className="pi pi-spinner pi-spin me-2" />
                    Uploading images
                    <span className="ml-2 text-nowrap rounded-md bg-blue-100 px-2 py-[3px]">
                      <i className="pi pi-images me-1 align-[-1px]" />{' '}
                      {status.uploadProgress} / {currentPost.imageCount}
                    </span>
                  </div>
                </>
              )}

              {status.isUploaded && (
                <>
                  <hr className="border-gray-200" />
                  <div className="font-semibold text-blue-800">
                    <i className="pi pi-check-circle me-2" />
                    Images upload finished
                    <span className="ml-2 text-nowrap rounded-md bg-blue-100 px-2 py-[3px]">
                      <i className="pi pi-images me-1 align-[-1px]" />
                      {currentPost.imageCount} / {currentPost.imageCount}
                    </span>
                  </div>
                </>
              )}

              {/* Modified these conditions to be mutually exclusive */}
              {status.isGenerating && !status.isPublished && (
                <>
                  <hr className="border-gray-200" />
                  <div className="font-semibold text-blue-800">
                    <i className="pi pi-spinner pi-spin me-2" />
                    Generating article...
                  </div>
                </>
              )}

              {status.isPublished && (
                <>
                  <hr className="border-gray-200" />
                  <div className="font-semibold text-blue-800">
                    <i className="pi pi-check-circle me-2" />
                    Post published ({status.wordCount.toLocaleString()} words).
                  </div>
                </>
              )}
            </div>
          </div>
        </Panel>
      </div>
    </div>
  )
}

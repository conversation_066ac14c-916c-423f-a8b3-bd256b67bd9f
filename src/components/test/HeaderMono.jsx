'use client'

import Link from 'next/link'
import { useState, useEffect } from 'react'
import { Logo } from '@/components/Logo'

function MobileMenu({ isOpen, onClose }) {
  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50">
      <div
        className="absolute inset-0 bg-gray-900/60 backdrop-blur-sm"
        onClick={onClose}
      />
      <div className="absolute right-4 top-4 w-64 overflow-hidden">
        <div className="relative bg-gray-900 shadow-2xl">
          <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-blue-500 to-transparent" />
          <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-blue-500 to-transparent" />
          <div className="relative p-6">
            <div className="flex justify-end">
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-white"
              >
                <i className="pi pi-times text-xl" />
              </button>
            </div>
            <nav className="mt-6 space-y-4">
              <Link
                href="/features"
                className="block text-sm font-medium text-gray-300 hover:text-white"
                onClick={onClose}
              >
                Features
              </Link>
              <Link
                href="/pricing"
                className="block text-sm font-medium text-gray-300 hover:text-white"
                onClick={onClose}
              >
                Pricing
              </Link>
              <Link
                href="/login"
                className="block text-sm font-medium text-gray-300 hover:text-white"
                onClick={onClose}
              >
                Sign in
              </Link>
            </nav>
          </div>
        </div>
      </div>
    </div>
  )
}

export function Header() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [scrolled, setScrolled] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 50)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  return (
    <header className="fixed inset-x-0 top-0 z-40">
      {/* Backdrop with transition */}
      <div
        className={`absolute inset-0 transition-colors duration-300 ${
          scrolled
            ? 'bg-gray-900 shadow-lg'
            : 'bg-gradient-to-b from-gray-900/80 via-gray-900/50 to-transparent backdrop-blur-sm'
        } `}
      />

      <div className="relative mx-auto max-w-7xl px-6">
        <div className="flex h-20 items-center justify-between">
          {/* Logo */}
          <Link
            href="/"
            className="group flex items-center gap-3 transition-opacity hover:opacity-80"
          >
            <div className="relative">
              <Logo className="h-8 w-auto fill-white" />
              <div className="absolute -inset-2 -z-10 opacity-0 blur-lg transition-opacity group-hover:opacity-50">
                <Logo className="h-12 w-auto fill-blue-500" />
              </div>
            </div>
            <div className="hidden text-xl text-white md:block">
              Wall<span className="font-semibold">Press</span>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden items-center gap-6 md:flex">
            <nav className="flex items-center gap-6">
              <Link
                href="/features"
                className="text-sm font-medium text-gray-300 transition-colors hover:text-white"
              >
                Features
              </Link>
              <Link
                href="/pricing"
                className="text-sm font-medium text-gray-300 transition-colors hover:text-white"
              >
                Pricing
              </Link>
            </nav>

            <div className="h-5 w-px bg-gray-800" />

            <div className="flex items-center gap-4">
              <Link
                href="/login"
                className="text-sm font-medium text-gray-300 transition-colors hover:text-white"
              >
                Sign in
              </Link>
              <Link
                href="/register"
                className="group relative overflow-hidden rounded-lg bg-blue-500 px-4 py-2 text-sm font-medium text-white"
              >
                <span className="relative z-10 transition-transform duration-200 group-hover:translate-x-1">
                  Download →
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-blue-500 opacity-0 transition-opacity group-hover:opacity-100" />
              </Link>
            </div>
          </div>

          {/* Mobile Menu Button */}
          <button
            className="relative rounded-lg p-2 text-gray-400 hover:text-white md:hidden"
            onClick={() => setIsMobileMenuOpen(true)}
          >
            <div className="absolute inset-0 -z-10 opacity-0 transition-opacity hover:opacity-20">
              <div className="h-full w-full rounded-lg bg-blue-500 blur-lg" />
            </div>
            <i className="pi pi-bars text-xl" />
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      <MobileMenu
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
      />
    </header>
  )
}

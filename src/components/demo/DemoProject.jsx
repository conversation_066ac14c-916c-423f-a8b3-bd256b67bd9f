'use client'

import { DataTable } from 'primereact/datatable'
import { Column } from 'primereact/column'
import { But<PERSON> } from 'primereact/button'
import { Tag } from 'primereact/tag'
import { ProgressBar } from 'primereact/progressbar'

export function DemoProject() {
  const projects = [
    {
      name: 'Living Room',
      keywords: 165,
      progress: 79,
      mode: 'Auto',
    },
    {
      name: 'Bedroom Furniture',
      keywords: 142,
      progress: 65,
      mode: 'Manual',
    },
    {
      name: 'Modern Kitchen',
      keywords: 156,
      progress: 45,
      mode: 'Auto',
    },
    {
      name: 'Home Office',
      keywords: 128,
      progress: 82,
      mode: 'Manual',
    },
    {
      name: 'Outdoor Furniture',
      keywords: 98,
      progress: 35,
      mode: 'Auto',
    },
    {
      name: 'Dining Room',
      keywords: 134,
      progress: 58,
      mode: 'Manual',
    },
    {
      name: 'Kids Room',
      keywords: 87,
      progress: 42,
      mode: 'Auto',
    },
    {
      name: 'Storage Solutions',
      keywords: 112,
      progress: 67,
      mode: 'Manual',
    },
    {
      name: 'Wall Decor',
      keywords: 94,
      progress: 51,
      mode: 'Auto',
    },
    {
      name: 'Lighting Fixtures',
      keywords: 76,
      progress: 29,
      mode: 'Manual',
    },
    {
      name: 'Bathroom Design',
      keywords: 108,
      progress: 73,
      mode: 'Auto',
    },
    {
      name: 'Small Spaces',
      keywords: 122,
      progress: 48,
      mode: 'Manual',
    },
    {
      name: 'Entertainment Units',
      keywords: 89,
      progress: 55,
      mode: 'Auto',
    },
    {
      name: 'Home Textiles',
      keywords: 95,
      progress: 61,
      mode: 'Manual',
    },
    {
      name: 'Study Room',
      keywords: 82,
      progress: 38,
      mode: 'Auto',
    },
    {
      name: 'Accent Furniture',
      keywords: 115,
      progress: 44,
      mode: 'Manual',
    },
    {
      name: 'Minimalist Decor',
      keywords: 104,
      progress: 69,
      mode: 'Auto',
    },
    {
      name: 'Guest Room',
      keywords: 78,
      progress: 32,
      mode: 'Manual',
    },
    {
      name: 'Home Entrance',
      keywords: 92,
      progress: 57,
      mode: 'Auto',
    },
    {
      name: 'Window Treatments',
      keywords: 86,
      progress: 41,
      mode: 'Manual',
    },
  ]

  const progressTemplate = (rowData) => {
    return (
      <div className="flex w-full items-center gap-2 px-2">
        <div className="relative h-4 w-full overflow-hidden border-2 border-black bg-white">
          <div
            className="absolute left-0 top-0 h-full bg-[#064ADA]"
            style={{ width: `${rowData.progress}%` }}
          />
        </div>
        <div className="font-mono text-xs font-bold uppercase text-black">
          {rowData.progress}%
        </div>
      </div>
    )
  }

  const actionsTemplate = () => {
    return (
      <div className="flex justify-center gap-1">
        {[
          { icon: 'pi pi-folder-open', label: 'open' },
          { icon: 'pi pi-cog', label: 'config' },
          { icon: 'pi pi-file-export', label: 'export' },
          { icon: 'pi pi-trash', label: 'delete', danger: true },
        ].map((action) => (
          <button
            key={action.label}
            className={`relative border-0 border-black p-1.5 text-black transition-all ${action.danger ? '!text-red-500' : ''}`}>
            <i className={`${action.icon} text-md`} />
          </button>
        ))}
      </div>
    )
  }

  const modeTemplate = (rowData) => {
    return (
      <div
        className={`flex justify-center font-mono text-xs font-bold uppercase ${
          rowData.mode === 'Auto' ? 'text-[#064ADA]' : 'text-black'
        }`}
      >
        <div className="flex items-center gap-1.5 border-0 border-black px-2 py-0.5 text-black">
          <span
            className={`h-2 w-2 ${
              rowData.mode === 'Auto' ? 'bg-[#064ADA]' : 'bg-black'
            }`}
          />
          {rowData.mode}
        </div>
      </div>
    )
  }

  const tableStyles = {
    css: `
      .p-datatable {
        background: transparent;
        font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
      }
      .p-datatable .p-datatable-header {
        background: transparent;
        border: none;
        padding: 1rem 1.5rem;
      }
      .p-datatable .p-datatable-thead > tr > th {
        background: transparent;
        border: none;
        border-bottom: 4px solid black;
        color: black;
        font-weight: 900;
        padding: 0.75rem 1rem;
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
      }
      .p-datatable .p-datatable-tbody > tr {
        background: transparent;
        transition: background-color 0.2s;
      }
      .p-datatable .p-datatable-tbody > tr > td {
        border: none;
        border-bottom: 2px solid black;
        padding: 0.75rem 1rem;
        color: black;
      }
      .p-datatable .p-datatable-tbody > tr:hover {
        background: #FFFFAA;
      }
      .p-paginator {
        background: transparent;
        border: none;
        padding: 1rem;
      }
      .p-paginator .p-paginator-element {
        color: black;
        min-width: 2rem;
        height: 2rem;
        margin: 0 0.125rem;
        font-size: 0.75rem;
        font-weight: bold;
        text-transform: uppercase;
      }
      .p-paginator .p-paginator-element.p-highlight {
        background: black;
        border-color: black;
        color: white;
      }
      .p-paginator .p-paginator-element:not(.p-highlight):hover {
        background: #FFFFAA;
        border-color: black;
        color: black;
      }

      .p-paginator .p-paginator-pages .p-paginator-page:focus {
        box-shadow: none;
      }

      .p-paginator .p-paginator-first:not(.p-disabled):hover,
      .p-paginator .p-paginator-prev:not(.p-disabled):hover,
      .p-paginator .p-paginator-next:not(.p-disabled):hover,
      .p-paginator .p-paginator-last:not(.p-disabled):hover {
        background: #FFFFAA;
        border-color: black;
        color: black;
      }

      .p-paginator .p-paginator-first.p-disabled,
      .p-paginator .p-paginator-prev.p-disabled,
      .p-paginator .p-paginator-next.p-disabled,
      .p-paginator .p-paginator-last.p-disabled {
        color: #666;
    `,
  }

  return (
    <div className="relative overflow-hidden border-4 border-black bg-white ">
      <style>{tableStyles.css}</style>

      {/* Header */}
      <div className="border-b-4 border-black bg-gray-100 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <h2 className="font-mono text-sm font-bold uppercase text-black">
              Projects Manager
            </h2>
            <span className="hidden border-2 border-black bg-[#1E1E1E] px-2 py-0.5 font-mono text-xs font-bold uppercase text-white md:block">
              {projects.length} active
            </span>
          </div>

          <button className="relative border-4 border-black bg-[#1E1E1E] px-3 py-1.5 font-mono text-xs font-bold uppercase text-white transition-all hover:bg-[#064ADA] hover:text-white ">
            <span className="flex items-center gap-2">
              <i className="pi pi-plus text-[10px]" />
              <span className="hidden sm:block">New Project</span>
            </span>
          </button>
        </div>
      </div>

      {/* Table */}
      <DataTable
        value={projects}
        paginator
        rows={10}
        className="border-none"
        emptyMessage={
          <span className="font-mono text-sm text-black">
            No records found
          </span>
        }
      >
        <Column
          field="name"
          header="Project"
          sortable
          body={(rowData) => (
            <div className="font-mono text-sm">
              <span className="text-black">{rowData.name}</span>
            </div>
          )}
        />
        <Column
          field="keywords"
          header="Keywords"
          sortable
          pt={{
            headerContent: {
              className: 'justify-center',
            },
          }}
          body={(rowData) => (
            <div className="text-center font-mono text-sm text-black">
              {rowData.keywords}
            </div>
          )}
        />
        <Column
          field="progress"
          header="Progress"
          pt={{
            headerContent: {
              className: 'justify-center',
            },
          }}
          body={progressTemplate}
          sortable
        />
        <Column
          pt={{
            headerContent: {
              className: 'justify-center',
            },
          }}
          field="mode"
          header="Mode"
          body={modeTemplate}
          sortable
        />
        <Column
          pt={{
            headerContent: {
              className: 'justify-center',
            },
          }}
          header="Actions"
          body={actionsTemplate}
        />
      </DataTable>

      {/* Footer */}
      {/* <div className="border-t-4 border-black bg-gray-100 px-4 py-2"> */}
      {/*   <div className="flex items-center justify-between font-mono text-xs text-black"> */}
      {/*     <div className="flex items-center gap-2"> */}
      {/*       <span className="h-2 w-2 bg-[#064ADA] shadow-[0_0_8px_rgba(6,74,218,0.5)]" /> */}
      {/*       system.status: operational */}
      {/*     </div> */}
      {/*     <span>last_sync: 2m ago</span> */}
      {/*   </div> */}
      {/* </div> */}
    </div>
  )
}

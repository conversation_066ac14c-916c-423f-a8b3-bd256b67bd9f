'use client'

import { DataTable } from 'primereact/datatable'
import { Column } from 'primereact/column'
import { Button } from 'primereact/button'
import { Badge } from 'primereact/badge'
import { useState } from 'react'

export function DemoTemplate() {
  const templatesList = [
    {
      name: 'Post Templates',
      note: '',
      type: 'group',
      badge: 5,
      items: [
        {
          name: 'Default Post Template',
          note: 'Default Post Content',
          type: 'item',
        },
        {
          name: 'AI Post Template',
          note: 'AI Content',
          type: 'item',
        },
        {
          name: 'AI Images Post Template',
          note: 'AI Content with Images',
          type: 'item',
        },
        {
          name: 'AI Youtube Post Template',
          note: 'AI Content with Youtube Video',
          type: 'item',
        },
        {
          name: 'Spintax Post Template',
          note: 'Spintax Post Content',
          type: 'item',
        },
      ],
    },
    {
      name: 'Image Caption Templates',
      note: '',
      type: 'group',
      badge: 1,
      items: [
        {
          name: 'Default Caption Template',
          note: 'Default Caption',
          type: 'item',
        },
      ],
    },
    {
      name: 'Attachment Templates',
      note: '',
      type: 'group',
      badge: 1,
      items: [
        {
          name: 'Default Attachment Template',
          note: 'Default Attachment Template',
          type: 'item',
        },
      ],
    },
  ]

  // Flatten data to include both groups and items
  const flatData = templatesList.reduce((acc, group) => {
    return [...acc, group, ...group.items]
  }, [])

  const actionTemplate = (rowData) => {
    if (rowData.type === 'item') {
      return (
        <div className="flex justify-center gap-1">
          {[
            { icon: 'pi pi-cog', label: 'settings' },
            { icon: 'pi pi-file-edit', label: 'edit' },
            { icon: 'pi pi-trash', label: 'delete', danger: true },
          ].map((action) => (
            <button
              key={action.label}
              className={`relative p-1.5 text-black transition-all hover:bg-[#064ADA] hover:text-white  ${action.danger ? '!text-red-500' : ''}`}>
              <i className={`${action.icon} text-md`} />
            </button>
          ))}
        </div>
      )
    }
  }

  const nameTemplate = (rowData) => {
    if (rowData.type === 'group') {
      return (
        <div className="flex items-center gap-3 py-2">
          <i className="pi pi-folder text-[#064ADA]" />
          <span className="text-nowrap font-mono text-sm font-bold uppercase text-black">
            {rowData.name}
          </span>
          <span className="text-nowrap border-2 border-black bg-[#1E1E1E] px-2 py-0.5 font-mono text-xs font-bold uppercase text-white">
            {rowData.badge} templates
          </span>
        </div>
      )
    }
    return (
      <div className="flex items-center gap-3 pl-8">
        <i className="pi pi-file-edit text-black" />
        <span className="font-mono text-sm font-bold uppercase text-black">{rowData.name}</span>
      </div>
    )
  }

  const noteTemplate = (rowData) => {
    if (rowData.type === 'item') {
      return (
        <div className="font-mono text-xs font-bold uppercase text-black">{rowData.note}</div>
      )
    }
  }

  const tableStyles = {
    css: `
      .p-datatable {
        background: transparent;
        font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
      }
      .p-datatable .p-datatable-header {
        background: transparent;
        border: none;
        padding: 1rem 1.5rem;
      }
      .p-datatable .p-datatable-thead > tr > th {
        background: transparent;
        border: none;
        border-bottom: 4px solid black;
        color: black;
        font-weight: 900;
        padding: 0.75rem 1rem;
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
      }
      .p-datatable .p-datatable-tbody > tr {
        background: transparent;
        transition: background-color 0.2s;
      }
      .p-datatable .p-datatable-tbody > tr > td {
        border: none;
        border-bottom: 2px solid black;
        padding: 0.75rem 1rem;
        color: black;
      }
      .p-datatable .p-datatable-tbody > tr:hover {
        background: #FFFFAA;
      }
    `,
  }

  return (
    <div className="relative overflow-hidden border-4 border-black bg-white ">
      <style>{tableStyles.css}</style>

      {/* Enhanced Header */}
      <div className="border-b-4 border-black bg-gray-100 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <h2 className="font-mono text-sm font-bold uppercase text-black">
              Templates Manager
            </h2>
            <span className="text-nowrap border-2 border-black bg-[#1E1E1E] px-2 py-0.5 font-mono text-xs font-bold uppercase text-white">
              {flatData.filter((item) => item.type === 'item').length} total
            </span>
          </div>

          <button className="relative border-4 border-black bg-[#1E1E1E] px-3 py-1.5 font-mono text-xs font-bold uppercase text-white transition-all hover:bg-[#064ADA] hover:text-white ">
            <span className="flex items-center gap-2">
              <i className="pi pi-plus text-[10px]" />
              <span className="hidden sm:block">Add New</span>
            </span>
          </button>
        </div>
      </div>

      {/* Enhanced Table */}
      <DataTable
        value={flatData}
        className="border-none"
        emptyMessage={
          <span className="font-mono text-sm text-black">
            No templates found
          </span>
        }
      >
        <Column field="name" header="Template" body={nameTemplate} />
        <Column field="note" header="Description" body={noteTemplate} />
        <Column
          pt={{
            headerContent: {
              className: 'justify-center',
            },
          }}
          header="Actions"
          body={actionTemplate}
        />
      </DataTable>

      {/* Enhanced Footer */}
      {/* <div className="border-t-4 border-black bg-gray-100 px-4 py-2"> */}
      {/*   <div className="flex items-center justify-between font-mono text-xs text-black"> */}
      {/*     <div className="flex items-center gap-2"> */}
      {/*       <span className="h-2 w-2 bg-[#064ADA] shadow-[0_0_8px_rgba(6,74,218,0.5)]" /> */}
      {/*       Last Modified: 2h ago */}
      {/*     </div> */}
      {/*   </div> */}
      {/* </div> */}
    </div>
  )
}

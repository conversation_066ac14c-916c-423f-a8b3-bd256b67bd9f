'use client'
import React, { useState, useEffect } from 'react'
import { Panel } from 'primereact/panel'
import { Button } from 'primereact/button'

// Dummy data
const dummyPosts = [
  {
    keyword: 'modern kitchen island',
    title:
      'How to Choose the Perfect Countertop for Your Modern Kitchen Island',
    imageCount: 5,
    wordCount: 1050,
  },
  {
    keyword: 'minimalist living room',
    title: '10 Essential Tips for Designing a Minimalist Living Room',
    imageCount: 7,
    wordCount: 1200,
  },
  {
    keyword: 'small bathroom ideas',
    title: 'Creative Storage Solutions for Small Bathroom Spaces',
    imageCount: 4,
    wordCount: 950,
  },
  {
    keyword: 'outdoor patio design',
    title: 'Transform Your Backyard: Modern Patio Design Ideas',
    imageCount: 6,
    wordCount: 1150,
  },
]

export function DemoPosting() {
  const [isMaximized, setIsMaximized] = useState(false)
  const [currentPost, setCurrentPost] = useState(dummyPosts[0])
  const [status, setStatus] = useState({
    isCreating: true,
    isCreated: false,
    uploadProgress: 0,
    isUploading: false,
    isUploaded: false,
    isGenerating: false,
    isPublished: false,
    wordCount: 0,
  })

  const startProcess = () => {
    // Reset status
    setStatus({
      isCreating: true,
      isCreated: false,
      uploadProgress: 0,
      isUploading: false,
      isUploaded: false,
      isGenerating: false,
      isPublished: false,
      wordCount: 0,
    })

    // Randomly select a new post
    const randomPost = dummyPosts[Math.floor(Math.random() * dummyPosts.length)]
    setCurrentPost(randomPost)

    // Start the animation process
    setTimeout(() => {
      setStatus((prev) => ({
        ...prev,
        isCreating: false,
        isCreated: true,
        isUploading: true,
      }))
    }, 1000)

    const uploadInterval = setInterval(() => {
      setStatus((prev) => {
        if (prev.uploadProgress >= randomPost.imageCount) {
          clearInterval(uploadInterval)
          return {
            ...prev,
            isUploading: false,
            isUploaded: true,
            isGenerating: true,
          }
        }
        return {
          ...prev,
          uploadProgress: prev.uploadProgress + 1,
        }
      })
    }, 800)

    // Add delay before publishing to show generating state
    setTimeout(
      () => {
        setStatus((prev) => ({
          ...prev,
          isGenerating: true,
        }))

        // Add another setTimeout for publishing
        setTimeout(() => {
          setStatus((prev) => ({
            ...prev,
            isGenerating: false,
            isPublished: true,
            wordCount: randomPost.wordCount,
          }))
        }, 3000) // Show generating state for 3 seconds
      },
      randomPost.imageCount * 800 + 1000,
    ) // Start after images upload
  }

  useEffect(() => {
    startProcess()
  }, [])

  const headerTemplate = (options) => {
    const className = `${options.className} flex justify-between bg-white rounded-none`

    return (
      <div className={className}>
        <div className="flex items-center gap-2">
          <h2 className="text-lg font-semibold text-gray-800">
            {currentPost.keyword}
          </h2>
        </div>
        <div>
          <div className="flex items-center gap-2">
            {status.uploadProgress > 0 && (
              <span className="inline-flex items-center rounded-md bg-blue-100 px-2 font-semibold text-blue-800">
                <i className="pi pi-images me-1" /> {status.uploadProgress}
              </span>
            )}
            {status.isPublished ? (
              <span className="rounded-md bg-blue-100 px-2">
                <i className="pi pi-check align-[-1px] font-semibold text-blue-800" />
              </span>
            ) : (
              <span className="rounded-md px-2">
                <i className="pi pi-spinner pi-spin align-[-1px] font-semibold text-blue-800" />
              </span>
            )}
          </div>
        </div>
      </div>
    )
  }

  const fooerTemplate = (options) => {
    const className = `${options.className} flex flex-wrap items-center justify-between gap-3`

    return (
      <div className={className}>
        <div className="align-items-center pointer-events-none flex gap-2">
          {status.isPublished && (
            <>
              <Button icon="pi pi-pencil" text severity="secondary" />
              <Button icon="pi pi-external-link" text severity="secondary" />
            </>
          )}
        </div>
        <Button
          label="Refresh"
          severity="secondary"
          outlined
          onClick={startProcess}
          disabled={!status.isPublished}
        />
      </div>
    )
  }

  return (
    <div className="relative overflow-hidden border-4 border-black bg-white p-2 ">
      <div className="relative space-y-4 bg-white p-6 text-sm">
        <div className="flex items-center gap-2 font-bold">
          <span className="text-[#064ADA]">▶</span>
          <span className="text-black">generate</span>
          <span className="text-black">keyword:</span>
          <span className="border-2 border-black bg-blue-200 px-2 py-0.5 font-bold uppercase text-black">
            {currentPost.keyword}
          </span>
        </div>

        <div className="space-y-3 pt-2 text-xs">
          {status.isCreating && (
            <div className="flex items-center gap-2 border-2 border-black bg-gray-100 p-2 shadow-[2px_2px_0_0_#000]">
              <span className="h-2 w-2 animate-pulse bg-[#064ADA] shadow-[0_0_8px_rgba(6,74,218,0.5)]" />
              <span className="font-bold uppercase text-black">[SYSTEM]</span>
              <span className="text-black">Generating Post Title...</span>
            </div>
          )}

          {status.isCreated && (
            <div className="flex items-center gap-2 border-2 border-black bg-blue-200 p-2 shadow-[2px_2px_0_0_#000] text-white text-white text-white">
              <span className="text-black">
                <i className="pi pi-check"></i>
              </span>
              <span className="font-bold uppercase text-black">[CREATE]</span>
              <span className="text-black">{currentPost.title}</span>
            </div>
          )}

          {status.isUploading && (
            <div className="space-y-2 border-2 border-black bg-gray-100 p-2 shadow-[2px_2px_0_0_#000]">
              <div className="flex items-center gap-2">
                <span className="h-2 w-2 animate-pulse bg-[#064ADA] shadow-[0_0_8px_rgba(6,74,218,0.5)]" />
                <span className="font-bold uppercase text-black">[UPLOAD]</span>
                <span className="text-black">
                  Uploading Images: [{status.uploadProgress}/
                  {currentPost.imageCount}]
                </span>
              </div>
              <div className="flex gap-1 border-2 border-black bg-white p-0.5">
                {[...Array(currentPost.imageCount)].map((_, i) => (
                  <div
                    key={i}
                    className={`h-2 flex-1 border-2 border-black ${
                      i < status.uploadProgress
                        ? 'bg-[#064ADA]'
                        : 'bg-gray-300'
                    }`}
                  />
                ))}
              </div>
            </div>
          )}

          {status.isUploaded && (
            <div className="flex items-center gap-2 border-2 border-black bg-blue-200 p-2 shadow-[2px_2px_0_0_#000] text-white text-white text-white">
              <span className="text-black">
                <i className="pi pi-check"></i>
              </span>
              <span className="font-bold uppercase text-black">[UPLOAD]</span>
              <span className="text-black">Images Uploaded</span>
            </div>
          )}

          {status.isGenerating && !status.isPublished && (
            <div className="flex items-center gap-2 border-2 border-black bg-gray-100 p-2 shadow-[2px_2px_0_0_#000]">
              <span className="h-2 w-2 animate-pulse bg-[#064ADA] shadow-[0_0_8px_rgba(6,74,218,0.5)]" />
              <span className="font-bold uppercase text-black">[GENERATE]</span>
              <span className="text-black">Generating Post Content...</span>
            </div>
          )}

          {status.isPublished && (
            <>
              <div className="flex items-center gap-2 border-2 border-black bg-blue-200 p-2 shadow-[2px_2px_0_0_#000] text-white text-white text-white">
                <span className="text-black">
                  <i className="pi pi-check"></i>
                </span>
                <span className="font-bold uppercase text-black">[PUBLISH]</span>
                <span className="text-black">
                  Post Published: {status.wordCount.toLocaleString()} words
                </span>
              </div>
              <div className="mt-4 flex items-center justify-end gap-2 border-t-2 border-black pt-4">
                <span className="font-bold uppercase text-[#064ADA]">▶</span>
                <button
                  onClick={startProcess}
                  className="font-bold uppercase text-black hover:text-[#064ADA]"
                >
                  Generate More
                </button>
              </div>
            </>
          )}
        </div>
      </div>

      <div className="flex justify-between border-t-4 border-black bg-gray-100 px-4 py-2">
        <div className="flex items-center gap-2">
          <span className="h-2 w-2 bg-[#064ADA] shadow-[0_0_8px_rgba(6,74,218,0.5)]" />
          <span className="font-mono text-xs font-bold uppercase text-black">
            SYSTEM READY
          </span>
        </div>
        <span className="font-mono text-xs font-bold uppercase text-black">
          v1.0.0
        </span>
      </div>
    </div>
  )
}

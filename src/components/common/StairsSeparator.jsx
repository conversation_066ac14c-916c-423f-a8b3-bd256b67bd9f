'use client'

import { useEffect, useRef, useState } from 'react'

// Ahrefs-inspired animated stairs separator
export function StairsSeparator({ 
  direction = 'down', 
  topBg = 'bg-[#064ADA]', 
  bottomBg = 'bg-white',
  animateFrom = 'left'
}) {
  const [isVisible, setIsVisible] = useState(false)
  const ref = useRef(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold: 0.3 }
    )

    if (ref.current) {
      observer.observe(ref.current)
    }

    return () => observer.disconnect()
  }, [])

  return (
    <div ref={ref} className="relative w-full h-20 overflow-hidden">
      {/* Bottom background layer */}
      <div className={`absolute inset-0 ${bottomBg}`} />
      
      {/* Animated stairs steps */}
      {[0, 1, 2, 3].map((step) => (
        <div
          key={step}
          className={`absolute ${topBg}`}
          style={{
            width: `${100 - step * 25}%`,
            height: '25%',
            left: `${step * 25}%`,
            top: direction === 'down' ? `${step * 25}%` : `${(3 - step) * 25}%`,
            transform: isVisible 
              ? 'translateX(0) scaleX(1)' 
              : animateFrom === 'left' 
                ? 'translateX(-100%) scaleX(0)'
                : 'translateX(100%) scaleX(0)',
            transformOrigin: animateFrom === 'left' ? 'left' : 'right',
            transition: `transform 0.8s cubic-bezier(0.4, 0, 0.2, 1) ${animateFrom === 'left' ? step * 0.15 : (3 - step) * 0.15}s`,
            backfaceVisibility: 'hidden',
            WebkitBackfaceVisibility: 'hidden'
          }}
        />
      ))}
    </div>
  )
}

// Alternative animated stairs separator with smaller height
export function CleanStairsSeparator({ 
  direction = 'down', 
  topBg = 'bg-[#064ADA]', 
  bottomBg = 'bg-white',
  animateFrom = 'left'
}) {
  const [isVisible, setIsVisible] = useState(false)
  const ref = useRef(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold: 0.3 }
    )

    if (ref.current) {
      observer.observe(ref.current)
    }

    return () => observer.disconnect()
  }, [])

  return (
    <div ref={ref} className="relative w-full h-16 overflow-hidden">
      {/* Bottom background layer */}
      <div className={`absolute inset-0 ${bottomBg}`} />
      
      {/* Animated stairs steps */}
      {[0, 1, 2, 3].map((step) => (
        <div
          key={step}
          className={`absolute ${topBg}`}
          style={{
            width: `${100 - step * 25}%`,
            height: '25%',
            left: `${step * 25}%`,
            top: direction === 'down' ? `${step * 25}%` : `${(3 - step) * 25}%`,
            transform: isVisible 
              ? 'translateX(0) scaleX(1)' 
              : animateFrom === 'left' 
                ? 'translateX(-100%) scaleX(0)'
                : 'translateX(100%) scaleX(0)',
            transformOrigin: animateFrom === 'left' ? 'left' : 'right',
            transition: `transform 0.8s cubic-bezier(0.4, 0, 0.2, 1) ${animateFrom === 'left' ? step * 0.15 : (3 - step) * 0.15}s`,
            backfaceVisibility: 'hidden',
            WebkitBackfaceVisibility: 'hidden'
          }}
        />
      ))}
    </div>
  )
}
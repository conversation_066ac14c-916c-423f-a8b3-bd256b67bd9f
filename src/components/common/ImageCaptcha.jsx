'use client'

import { useEffect, useRef, useCallback } from 'react'

export function ImageCaptcha({ captcha, onRefresh }) {
  const canvasRef = useRef(null)

  const drawCaptcha = useCallback(() => {
    const canvas = canvasRef.current
    if (!canvas || !captcha) return

    const ctx = canvas.getContext('2d')
    const width = canvas.width
    const height = canvas.height

    // Clear canvas
    ctx.clearRect(0, 0, width, height)

    // Background with gradient
    const gradient = ctx.createLinearGradient(0, 0, width, height)
    gradient.addColorStop(0, '#f8f9fa')
    gradient.addColorStop(1, '#e9ecef')
    ctx.fillStyle = gradient
    ctx.fillRect(0, 0, width, height)

    // Add noise lines
    ctx.strokeStyle = '#6c757d'
    ctx.lineWidth = 1
    for (let i = 0; i < 8; i++) {
      ctx.beginPath()
      ctx.moveTo(Math.random() * width, Math.random() * height)
      ctx.lineTo(Math.random() * width, Math.random() * height)
      ctx.stroke()
    }

    // Add noise dots
    ctx.fillStyle = '#adb5bd'
    for (let i = 0; i < 50; i++) {
      ctx.beginPath()
      ctx.arc(Math.random() * width, Math.random() * height, 1, 0, 2 * Math.PI)
      ctx.fill()
    }

    // Draw CAPTCHA text
    const chars = captcha.split('')
    const charWidth = width / chars.length
    
    chars.forEach((char, index) => {
      // Random font size and style
      const fontSize = 24 + Math.random() * 8
      ctx.font = `bold ${fontSize}px monospace`
      
      // Random color (dark colors)
      const colors = ['#000000', '#1a1a1a', '#333333', '#2c3e50', '#34495e']
      ctx.fillStyle = colors[Math.floor(Math.random() * colors.length)]
      
      // Random position with slight rotation
      const x = charWidth * index + charWidth / 2 - 8 + (Math.random() - 0.5) * 10
      const y = height / 2 + 8 + (Math.random() - 0.5) * 10
      
      ctx.save()
      ctx.translate(x, y)
      ctx.rotate((Math.random() - 0.5) * 0.4) // Random rotation
      ctx.fillText(char, 0, 0)
      ctx.restore()
    })

    // Add more interference lines on top
    ctx.strokeStyle = 'rgba(108, 117, 125, 0.3)'
    ctx.lineWidth = 2
    for (let i = 0; i < 3; i++) {
      ctx.beginPath()
      ctx.moveTo(Math.random() * width, Math.random() * height)
      ctx.lineTo(Math.random() * width, Math.random() * height)
      ctx.stroke()
    }
  }, [captcha])

  useEffect(() => {
    drawCaptcha()
  }, [drawCaptcha])

  return (
    <div className="flex items-center justify-between">
      <div className="border-2 border-black bg-white shadow-[2px_2px_0_0_#000]">
        <canvas
          ref={canvasRef}
          width={150}
          height={50}
          className="block"
          style={{ imageRendering: 'pixelated' }}
        />
      </div>
      <button
        type="button"
        onClick={onRefresh}
        className="border-2 border-black bg-gray-200 px-2 py-1 font-mono text-xs font-bold uppercase text-black shadow-[1px_1px_0_0_#000] transition-all hover:shadow-[2px_2px_0_0_#000]"
        title="Generate new CAPTCHA"
      >
        🔄
      </button>
    </div>
  )
}
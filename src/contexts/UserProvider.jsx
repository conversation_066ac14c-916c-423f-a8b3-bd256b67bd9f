// contexts/UserProvider.jsx
import { createContext, useContext } from 'react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useUser } from '@/hooks/useUser'

const UserContext = createContext()

const queryClient = new QueryClient()

export function UserProvider({ children }) {
  return (
    <QueryClientProvider client={queryClient}>
      <UserDataProvider>{children}</UserDataProvider>
    </QueryClientProvider>
  )
}

function UserDataProvider({ children }) {
  const { data: user, isLoading, error } = useUser()

  return (
    <UserContext.Provider value={{ user, isLoading, error }}>
      {children}
    </UserContext.Provider>
  )
}

export const useUserContext = () => useContext(UserContext)

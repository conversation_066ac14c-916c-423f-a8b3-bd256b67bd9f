import { Inter, Lexend } from 'next/font/google'
import clsx from 'clsx'
import { ThemeProvider } from '@/contexts/ThemeProvider'
import { PrimeReactProvider } from 'primereact/api'
import '@/styles/tailwind.css'
import 'primereact/resources/themes/lara-light-blue/theme.css'
import 'primeicons/primeicons.css'

export const metadata = {
  title: {
    template: '%s - SuperSense',
    default: 'SuperSense - AI-Powered WordPress Content Automation',
  },
  description:
    'Transform your WordPress site with automated content generation. Create SEO-optimized posts, manage keywords, and schedule content with AI precision.',
}

const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
})

const lexend = Lexend({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-lexend',
})

export default function RootLayout({ children }) {
  return (
    <html
      lang="en"
      className={clsx(
        'h-full scroll-smooth antialiased',
        inter.variable,
        lexend.variable,
      )}
    >
      <body className="flex h-full flex-col">
        <ThemeProvider><PrimeReactProvider>{children}</PrimeReactProvider></ThemeProvider>
      </body>
    </html>
  )
}

'use client'

import { motion } from 'framer-motion'
import { Logo } from '@/components/logo/LogoBolt'
import Link from 'next/link'

export default function NotFound() {
  return (
    <div className="relative min-h-screen overflow-hidden bg-[#0a0b0f]">
      {/* Background grid */}
      <div className="absolute inset-0 bg-[linear-gradient(to_right,#0066ff08_1px,transparent_1px),linear-gradient(to_bottom,#0066ff08_1px,transparent_1px)] bg-[size:24px_24px]" />

      {/* Ambient glow effects */}
      <div className="absolute left-1/4 top-0 h-96 w-96 rounded-full bg-blue-500/10 blur-[128px]" />
      <div className="absolute bottom-0 right-1/4 h-96 w-96 rounded-full bg-indigo-500/10 blur-[128px]" />

      <div className="relative mx-auto max-w-7xl px-6">
        <div className="flex min-h-screen flex-col items-center justify-center">
          {/* Terminal-style error box */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="relative w-full max-w-2xl rounded-sm border border-blue-900/30 bg-[#0c0d12]/80 shadow-[0_0_30px_rgba(0,0,0,0.3)] backdrop-blur-xl"
          >
            {/* Terminal header */}
            <div className="flex h-10 items-center justify-between border-b border-blue-900/30 bg-[#0c0d12]/90 px-4">
              <Logo className="h-6" />
              <div className="flex gap-1.5">
                {[...Array(3)].map((_, i) => (
                  <div
                    key={i}
                    className="h-2 w-2 rounded-full border border-gray-700 transition-colors hover:border-blue-500/50"
                  />
                ))}
              </div>
            </div>

            {/* Error content */}
            <div className="space-y-6 p-8">
              <div className="space-y-2">
                <div className="font-mono text-sm text-blue-500">
                  {'> error.status'}
                </div>
                <h1 className="font-mono text-6xl font-bold text-white">404</h1>
              </div>

              <div className="space-y-2">
                <div className="font-mono text-sm text-blue-500">
                  {'> error.message'}
                </div>
                <p className="font-mono text-xl text-gray-400">
                  Page not found in the matrix
                </p>
              </div>

              <div className="space-y-2">
                <div className="font-mono text-sm text-blue-500">
                  {'> system.suggestion'}
                </div>
                <p className="font-mono text-sm leading-relaxed text-gray-400">
                  The page you're looking for doesn't exist or has been moved to
                  another dimension. Check the URL or head back home.
                </p>
              </div>

              <Link
                href="/"
                className="group relative inline-flex items-center gap-2 overflow-hidden border border-blue-500/50 bg-blue-500/10 px-6 py-3 font-mono text-sm text-blue-400 transition-all hover:bg-blue-500/20 hover:shadow-[0_0_20px_rgba(0,100,255,0.3)]"
              >
                <span className="flex items-center gap-2">
                  <span className="h-1 w-1 animate-pulse rounded-full bg-blue-500" />
                  Return.home()
                </span>
                <div className="group-hover:animate-shine absolute inset-0 -z-10 translate-x-[-100%] bg-gradient-to-r from-transparent via-blue-500/10 to-transparent" />
              </Link>
            </div>

            {/* Corner accents */}
            <div className="absolute left-0 top-0 h-16 w-[1px] bg-gradient-to-b from-blue-500/50 to-transparent" />
            <div className="absolute left-0 top-0 h-[1px] w-16 bg-gradient-to-r from-blue-500/50 to-transparent" />
            <div className="absolute right-0 top-0 h-16 w-[1px] bg-gradient-to-b from-blue-500/50 to-transparent" />
            <div className="absolute right-0 top-0 h-[1px] w-16 bg-gradient-to-r from-transparent to-blue-500/50" />
          </motion.div>
        </div>
      </div>
    </div>
  )
}

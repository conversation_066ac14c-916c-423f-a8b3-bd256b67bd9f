'use client'

import { useUserContext } from '@/contexts/UserProvider'
import { useTheme } from '@/contexts/ThemeProvider'
import { PayPalCallbackHandler } from '@/components/dashboard/pages/payment/PayPalIntegration'
import {
  QuickStats,
  WebsitesTable,
  RecentActivityTable,
  DashboardSkeleton,
} from '@/components/dashboard/pages/dashboard'

export default function DashboardPage() {
  const { user, isLoading, error } = useUserContext()
  const { theme } = useTheme()

  // Show loading skeleton while data is being fetched
  if (isLoading) {
    return <DashboardSkeleton />
  }

  // Handle error state
  if (error) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="text-center">
          <div className="mb-4 text-red-500">
            <i className="pi pi-exclamation-triangle text-4xl"></i>
          </div>
          <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
            Failed to load dashboard data
          </h3>
          <p className="mb-4 text-gray-600 dark:text-gray-400">
            {error.message || 'An error occurred while fetching your data.'}
          </p>
          <button
            onClick={() => window.location.reload()}
            className="rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  // Handle case where user data is null/undefined
  if (!user) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="text-center">
          <div className="mb-4 text-yellow-500">
            <i className="pi pi-info-circle text-4xl"></i>
          </div>
          <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
            No user data available
          </h3>
          <p className="mb-4 text-gray-600 dark:text-gray-400">
            Unable to load your dashboard information.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
          >
            Reload
          </button>
        </div>
      </div>
    )
  }

  // Ensure websites array exists, default to empty array if not
  const websites = user.websites || []

  return (
    <>
      <PayPalCallbackHandler />
      <div className="space-y-6">
        <QuickStats user={user} theme={theme} />

        {/* Websites Section */}
        <div className="relative overflow-hidden rounded-md bg-white dark:bg-gradient-to-b dark:from-[#0f1117] dark:to-[#151821] border border-gray-100 dark:border-gray-900">
          {/* Enhanced glow effect */}
          <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-blue-300/50 to-transparent dark:via-blue-500/30" />

          {/* Header */}
          <div className="border-b border-gray-100 bg-white px-6 py-4 dark:border-blue-900/20 dark:bg-[#151821]/50">
            <div className="flex items-center gap-2">
              <h2 className="font-mono text-sm text-gray-800 dark:text-blue-400">Websites</h2>
            </div>
            <p className="mt-2 font-mono text-xs text-gray-500 dark:text-gray-500">
              <span className="text-gray-500 dark:text-gray-400">Registered:</span>{' '}
              <span className="text-blue-700 dark:text-blue-400">{websites.length}</span> |{' '}
              <span className="text-gray-500 dark:text-gray-400">Total Credits:</span>{' '}
              <span className="text-blue-700 dark:text-blue-400">
                {websites.reduce((acc, web) => acc + (web.credit || 0), 0)}
              </span>
            </p>
          </div>

          {/* Table */}
          <WebsitesTable websites={websites} />

        {/* Subtle scanline effect */}
        <div className="pointer-events-none absolute inset-0 bg-[linear-gradient(to_bottom,rgba(0,0,0,0.02)_50%,transparent_50%)] bg-[length:100%_4px] opacity-20 dark:bg-[linear-gradient(to_bottom,rgba(59,130,246,0.02)_50%,transparent_50%)]" />
      </div>

      {/* Optional: Activity Section */}
      {user?.recentActivity && user.recentActivity.length > 0 && (
        <div className="relative overflow-hidden rounded-md bg-white shadow-lg dark:bg-[#0f1117]">
          {/* Ambient glow effect */}
          <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-blue-300/50 to-transparent dark:via-blue-500/20" />

          <div className="border-b border-gray-100 bg-white px-6 py-4 dark:border-blue-900/20 dark:bg-[#151821]">
            <div className="flex items-center gap-2">
              <span className="font-mono text-xs text-blue-700 dark:text-blue-500">{'>'}</span>
              <h2 className="font-mono text-sm text-gray-800 dark:text-blue-400">
                system.activity
              </h2>
            </div>
            <p className="mt-2 font-mono text-xs text-gray-500 dark:text-gray-500">
              <span className="text-gray-500 dark:text-gray-400">recent_logs:</span>{' '}
              <span className="text-blue-700 dark:text-blue-400">
                {user.recentActivity.length}
              </span>
            </p>
          </div>
          <RecentActivityTable activity={user.recentActivity} />
        </div>
      )}

      {/* Scanline effect */}
      <div className="pointer-events-none fixed inset-0 bg-[linear-gradient(to_bottom,rgba(0,0,0,0.03)_50%,transparent_50%)] bg-[length:100%_4px] opacity-10 dark:bg-[linear-gradient(to_bottom,rgba(255,255,255,0.03)_50%,transparent_50%)] dark:opacity-20" />
      </div>
    </>
  )
}

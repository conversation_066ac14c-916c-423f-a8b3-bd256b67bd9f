'use client'

import { useUserContext } from '@/contexts/UserProvider'
import {
  QuickStats,
  RecentActivityTable,
  DashboardSkeleton,
} from '@/components/dashboard/pages/dashboard'

export default function ActivityPage() {
  const { user, isLoading, error } = useUserContext()

  if (isLoading) {
    return <DashboardSkeleton />
  }

  // Handle error state
  if (error) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="text-center">
          <div className="mb-4 text-red-500">
            <i className="pi pi-exclamation-triangle text-4xl"></i>
          </div>
          <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
            Failed to load activity data
          </h3>
          <p className="mb-4 text-gray-600 dark:text-gray-400">
            {error.message || 'An error occurred while fetching your data.'}
          </p>
          <button
            onClick={() => window.location.reload()}
            className="rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  // Handle case where user data is null/undefined
  if (!user) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="text-center">
          <div className="mb-4 text-yellow-500">
            <i className="pi pi-info-circle text-4xl"></i>
          </div>
          <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
            No user data available
          </h3>
          <p className="mb-4 text-gray-600 dark:text-gray-400">
            Unable to load your activity information.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
          >
            Reload
          </button>
        </div>
      </div>
    )
  }

  // Ensure websites array exists, default to empty array if not
  const websites = user.websites || []

  // Handle missing scanHistory data
  const recentScans = websites
    .filter(website => website.scanHistory && Array.isArray(website.scanHistory))
    .flatMap((website) =>
      website.scanHistory.map((scan) => ({
        ...scan,
        domain: website.domain,
      })),
    )
    .sort((a, b) => new Date(b.date) - new Date(a.date))

  // If no scan history available, create mock data or empty array
  const fallbackScans = websites.length > 0 ? websites.map(website => ({
    id: `scan_${website.domain}_${Date.now()}`,
    domain: website.domain,
    date: website.lastScanned || new Date().toISOString(),
    status: website.status === 'active' ? 'completed' : 'pending',
    scansCount: website.totalScans || 0,
    type: 'auto_scan'
  })) : []

  const activityData = recentScans.length > 0 ? recentScans : fallbackScans

  return (
    <div className="space-y-6">
      <QuickStats user={user} />

      <div className="relative overflow-hidden rounded-md bg-gradient-to-b from-[#0f1117] to-[#151821] border border-gray-100 dark:border-gray-900">
        {/* Ambient glow effect */}
        <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-blue-500/20 to-transparent" />

        {/* Header */}
        <div className="border-b border-gray-100 bg-white px-6 py-4 dark:border-blue-900/20 dark:bg-[#151821]/50">
          <div className="flex items-center gap-2">
            <h2 className="font-mono text-sm text-blue-400">Activity Logs</h2>
          </div>
          <p className="mt-2 font-mono text-xs text-gray-500">
            <span className="text-gray-400">Total Scans:</span>{' '}
            <span className="text-blue-400">{activityData.length}</span> |{' '}
            <span className="text-gray-400">Domains:</span>{' '}
            <span className="text-blue-400">
              {new Set(activityData.map((scan) => scan.domain)).size}
            </span>
          </p>
        </div>

        {/* Table */}
        <RecentActivityTable scanHistory={activityData} />

        {/* Subtle scanline effect */}
        <div className="pointer-events-none absolute inset-0 bg-[linear-gradient(to_bottom,rgba(59,130,246,0.02)_50%,transparent_50%)] bg-[length:100%_4px] opacity-20" />
      </div>

      {/* Global scanline effect */}
      <div className="pointer-events-none fixed inset-0 bg-[linear-gradient(to_bottom,rgba(255,255,255,0.03)_50%,transparent_50%)] bg-[length:100%_4px] opacity-20" />
    </div>
  )
}

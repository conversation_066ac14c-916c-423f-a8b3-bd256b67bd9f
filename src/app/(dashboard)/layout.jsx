'use client'
import QueryProvider from '@/providers/QueryProvider'
import { UserProvider } from '@/contexts/UserProvider'
import { PrimeReactProvider } from 'primereact/api'
import { DashboardShell } from '@/components/dashboard/layout/DashboardShell'
import { ThemeProvider } from '@/contexts/ThemeProvider'

export default function DashboardLayout({ children }) {
  return (
    <ThemeProvider>
      <QueryProvider>
        <UserProvider>
          <DashboardShell>
            <PrimeReactProvider>{children}</PrimeReactProvider>
          </DashboardShell>
        </UserProvider>
      </QueryProvider>
    </ThemeProvider>
  )
}

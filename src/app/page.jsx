import { Suspense } from 'react'
import dynamic from 'next/dynamic'
import { Footer } from '@/components/homepage/Footer'
import { Header } from '@/components/homepage/Header'
import { HeroTile } from '@/components/homepage/HeroTile'
import { StairsSeparator, CleanStairsSeparator } from '@/components/common/StairsSeparator'

const PrimaryFeatures = dynamic(
  () =>
    import('@/components/homepage/PrimaryFeatures').then(
      (mod) => mod.PrimaryFeatures,
    ),
  { suspense: true },
)

const SecondaryFeatures = dynamic(
  () =>
    import('@/components/homepage/SecondaryFeatures').then(
      (mod) => mod.SecondaryFeatures,
    ),
  { suspense: true },
)

const CallToAction = dynamic(
  () =>
    import('@/components/homepage/CallToAction').then(
      (mod) => mod.CallToAction,
    ),
  { suspense: true },
)

export default function Home() {
  return (
    <>
      <Header />
      <main>
        <HeroTile />
        
        {/* Stairs separator between Hero and Primary Features */}
        <StairsSeparator 
          direction="down" 
          topBg="bg-[#064ADA] bg-[radial-gradient(rgba(0,0,0,0.3)_1px,transparent_1px)] [background-size:16px_16px]" 
          bottomBg="bg-[repeating-conic-gradient(#fff_0deg,#fff_90deg,#f8f8f8_90deg,#f8f8f8_180deg)] [background-size:20px_20px]"
          animateFrom="left"
        />
        
        <Suspense fallback={<div>Loading...</div>}>
          <PrimaryFeatures />
          
          {/* Stairs separator between Primary and Secondary Features */}
          <CleanStairsSeparator 
            direction="down" 
            topBg="bg-[repeating-conic-gradient(#fff_0deg,#fff_90deg,#f8f8f8_90deg,#f8f8f8_180deg)] [background-size:20px_20px]" 
            bottomBg="bg-black"
            animateFrom="right"
          />
          
          <SecondaryFeatures />
          
          {/* Stairs separator between Secondary Features and CTA */}
          <CleanStairsSeparator 
            direction="up" 
            topBg="bg-[#064ADA]" 
            bottomBg="bg-black"
            animateFrom="left"
          />
          
          <CallToAction />
        </Suspense>
      </main>
      <Footer />
    </>
  )
}

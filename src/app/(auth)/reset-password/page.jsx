import { Suspense } from 'react'
import { ResetPassword } from '@/components/auth/ResetPassword'

function ResetPasswordFallback() {
  return (
    <div className="flex min-h-screen items-center justify-center bg-white">
      <div className="border-4 border-black bg-gray-100 p-8 shadow-[8px_8px_0_0_#064ADA]">
        <div className="flex items-center justify-center">
          <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-black"></div>
          <span className="ml-3 font-mono text-sm font-black uppercase text-black">
            Loading Reset Password...
          </span>
        </div>
      </div>
    </div>
  )
}

export default function ForgotPasswordPage() {
  return (
    <Suspense fallback={<ResetPasswordFallback />}>
      <ResetPassword />
    </Suspense>
  )
}

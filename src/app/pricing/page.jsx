'use client'

import { useState, useEffect } from 'react'
import { CheckIcon } from '@heroicons/react/20/solid'
import { Logo } from '@/components/logo/LogoBolt'
import { Header } from '@/components/homepage/Header'
import { Footer } from '@/components/homepage/Footer'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'

// Plans data
const plans = [
  {
    name: 'Basic',
    price: '$15',
    duration: 'per month',
    description: 'Essential auto-posting for small WordPress sites',
    features: [
      'Connect up to 5 WordPress sites',
      '100 auto-posts per month',
      'Simple keyword targeting',
      'Community forum access',
    ],
    cta: 'Get Started',
  },
  {
    name: 'Pro',
    price: '$40',
    duration: 'for 6 months',
    description: 'Advanced auto-posting for growing WordPress sites',
    features: [
      'Connect up to 15 WordPress sites',
      '500 auto-posts per month',
      'Smart keyword targeting',
      'Priority email support',
    ],
    discount: '56% OFF',
    cta: 'Choose Pro',
    popular: true,
  },
  {
    name: 'Enterprise',
    price: '$99',
    duration: 'per year',
    description: 'Complete auto-posting solution for WordPress professionals',
    features: [
      'Unlimited WordPress sites',
      '2,000 auto-posts per month',
      'Custom content templates',
      'Dedicated support team',
      'Advanced analytics dashboard',
    ],
    cta: 'Choose Enterprise',
  },
]

// Free plan data
const freePlan = {
  name: 'Starter',
  price: '$0',
  duration: 'forever',
  description: 'Basic WordPress auto-posting essentials',
  features: [
    'For a single WordPress site',
    '10 auto-posts per month',
    'Basic keyword targeting',
    'Community forum access',
    'Simple post scheduling',
  ],
  cta: 'Start Free',
  isFree: true,
}

export default function Pricing() {
  const [selectedPlan, setSelectedPlan] = useState('Pro')
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const router = useRouter()

  useEffect(() => {
    // Simulate checking user's login status
    const checkLoginStatus = () => {
      const loggedIn = localStorage.getItem('isLoggedIn') === 'true'
      setIsLoggedIn(loggedIn)
    }
    checkLoginStatus()
  }, [])

  const handlePlanSelection = (planName) => {
    if (isLoggedIn) {
      router.push(`/payment?plan=${planName}`)
    } else {
      router.push('/register')
    }
  }

  return (
    <>
      <Header />
      <main>
        <div className="relative min-h-screen bg-[repeating-conic-gradient(#fff_0deg,#fff_90deg,#f8f8f8_90deg,#f8f8f8_180deg)] pt-20 [background-size:20px_20px]">
          <div className="relative mx-auto max-w-7xl px-6 py-24">
            {/* Header Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-16 text-center"
            >
              <div className="inline-flex items-center space-x-2 border-2 border-black bg-gray-300 px-3 py-1 font-mono text-sm font-bold uppercase text-black shadow-[3px_3px_0_0_#000]">
                <span>Pricing Options</span>
              </div>

              <h1 className="mt-6 font-mono text-[clamp(2.5rem,5vw,4rem)] font-black uppercase leading-tight text-black">
                WordPress{' '}
                <span className="relative inline-block text-[#064ADA]">
                  Superpowers
                </span>
              </h1>

              <p className="mx-auto mt-8 max-w-4xl font-mono text-lg leading-relaxed text-black">
                <span className="font-bold">
                  Experience the next evolution of WordPress content creation
                  with AI-powered automation and intelligent optimization.
                </span>
              </p>
            </motion.div>

            <div className="mx-auto max-w-7xl space-y-6">
              {/* Free Plan - Displayed at the top */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="relative overflow-hidden border-4 border-black bg-gray-100 p-8 shadow-[8px_8px_0_0_#064ADA]"
              >
                <div className="flex flex-col md:flex-row">
                  <div className="flex-1 p-6">
                    <div className="flex items-center gap-3">
                      <h2 className="font-mono text-base font-bold uppercase text-black">
                        {freePlan.name}
                      </h2>
                      <div className="border-2 border-black bg-gray-300 px-2 py-0.5 font-mono text-xs font-bold uppercase text-black shadow-[2px_2px_0_0_#000]">
                        Free Forever
                      </div>
                    </div>
                    <div className="mt-2 flex items-baseline">
                      <span className="font-mono text-2xl font-black text-black">
                        {freePlan.price}
                      </span>
                      <span className="ml-1 font-mono text-xs font-bold uppercase text-black">
                        {freePlan.duration}
                      </span>
                    </div>
                    <p className="mt-2 font-mono text-xs text-black">
                      {freePlan.description}
                    </p>

                    <div className="mt-4 border-2 border-black bg-white p-3 shadow-[4px_4px_0_0_#000]">
                      <p className="font-mono text-xs font-bold uppercase text-black">
                        Perfect for individual WordPress sites and content
                        creators
                      </p>
                    </div>
                  </div>

                  <div className="flex-1 border-t-4 border-black p-6 md:border-l-4 md:border-t-0">
                    <ul className="space-y-2">
                      {freePlan.features.map((feature) => (
                        <li key={feature} className="flex items-start">
                          <CheckIcon className="mr-2 h-4 w-4 flex-shrink-0 text-[#064ADA]" />
                          <span className="font-mono text-xs font-bold uppercase text-black">
                            {feature}
                          </span>
                        </li>
                      ))}
                    </ul>
                    <button
                      onClick={() => handlePlanSelection(freePlan.name)}
                      className="relative mt-4 flex w-full items-center justify-center border-2 border-black bg-[#064ADA] py-2 font-mono text-sm font-bold uppercase text-white shadow-[3px_3px_0_0_#000] transition-all hover:shadow-[4px_4px_0px_0px_#000]"
                    >
                      {freePlan.cta}
                    </button>
                  </div>
                </div>
              </motion.div>

              {/* Paid Plans */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="relative overflow-hidden border-4 border-black bg-gray-100 p-8 shadow-[8px_8px_0_0_#064ADA]"
              >
                <div className="p-6">
                  <div className="grid gap-6 lg:grid-cols-3">
                    {plans.map((plan) => (
                      <div
                        key={plan.name}
                        className={`relative overflow-hidden border-4 border-black ${
                          selectedPlan === plan.name
                            ? 'bg-white shadow-[8px_8px_0_0_#064ADA]'
                            : 'bg-gray-100 shadow-[4px_4px_0_0_#000]'
                        } cursor-pointer p-6 transition-all hover:shadow-[10px_10px_0_0_#064ADA]`}
                        onClick={() => setSelectedPlan(plan.name)}
                      >
                        {plan.popular && (
                          <div className="absolute right-0 top-0">
                            <div className="border-b-4 border-l-4 border-black bg-[#064ADA] px-3 py-1 font-mono text-xs font-bold uppercase text-white">
                              Popular
                            </div>
                          </div>
                        )}

                        <div className="mb-4">
                          <h2 className="font-mono text-base font-bold uppercase text-black">
                            {plan.name}
                          </h2>
                          <div className="mt-2 flex items-baseline">
                            <span className="font-mono text-2xl font-black text-black">
                              {plan.price}
                            </span>
                            <span className="ml-1 font-mono text-xs font-bold uppercase text-black">
                              {plan.duration}
                            </span>
                          </div>
                          {plan.discount && (
                            <div className="mt-1 inline-block border-2 border-black bg-gray-300 px-2 py-0.5 font-mono text-xs font-bold uppercase text-black shadow-[2px_2px_0_0_#000]">
                              {plan.discount}
                            </div>
                          )}
                          <p className="mt-2 font-mono text-xs text-black">
                            {plan.description}
                          </p>
                        </div>

                        <ul className="mb-6 space-y-2">
                          {plan.features.map((feature) => (
                            <li key={feature} className="flex items-start">
                              <CheckIcon className="mr-2 h-4 w-4 flex-shrink-0 text-[#064ADA]" />
                              <span className="font-mono text-xs font-bold uppercase text-black">
                                {feature}
                              </span>
                            </li>
                          ))}
                        </ul>

                        <button
                          onClick={() => handlePlanSelection(plan.name)}
                          className={`relative w-full border-2 border-black py-2 font-mono text-sm font-bold uppercase text-white shadow-[3px_3px_0_0_#000] transition-all hover:shadow-[4px_4px_0px_0px_#000] ${
                            selectedPlan === plan.name
                              ? 'bg-[#064ADA]'
                              : 'bg-black'
                          }`}
                        >
                          {plan.cta}
                        </button>
                      </div>
                    ))}
                  </div>

                  <div className="mt-8 border-4 border-black bg-gray-100 p-4 shadow-[8px_8px_0_0_#064ADA]">
                    <p className="font-mono text-xs font-bold uppercase text-black">
                      All paid plans include a 14-day free trial with full
                      access to premium auto-posting features. No credit card
                      required to start. Cancel anytime.
                    </p>
                  </div>
                </div>

                {/* Subtle scanline effect */}
                <div className="pointer-events-none absolute inset-0 bg-[linear-gradient(to_bottom,rgba(255,255,255,0.03)_50%,transparent_50%)] bg-[length:100%_4px] opacity-20" />
              </motion.div>

              {/* FAQ Section */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="mt-16"
              >
                <div className="inline-flex items-center space-x-2 border-2 border-black bg-gray-300 px-3 py-1 font-mono text-sm font-bold uppercase text-black shadow-[3px_3px_0_0_#000]">
                  <span>FAQ</span>
                </div>

                <div className="mt-8 grid gap-6 md:grid-cols-2">
                  {[
                    {
                      q: 'How does the auto-posting work?',
                      a: 'Our plugin connects to your WordPress site and automatically creates and publishes content based on your keywords. Simply install the plugin, set your topics, and let it generate engaging posts for your site.',
                    },
                    {
                      q: 'Will this work with my WordPress theme?',
                      a: 'Yes! Our auto-posting plugin is compatible with all WordPress themes and popular page builders including Elementor, Divi, and the default WordPress editor.',
                    },
                    {
                      q: 'What payment methods do you accept?',
                      a: 'We accept all major credit cards and PayPal. The checkout process is simple and secure - just select your preferred plan and follow the payment steps.',
                    },
                    {
                      q: 'Can I upgrade my plan later?',
                      a: 'Absolutely! You can upgrade your auto-posting plan at any time. The remaining value of your current plan will be applied as credit toward your new plan.',
                    },
                  ].map((item, i) => (
                    <div
                      key={i}
                      className="border-4 border-black bg-gray-100 p-6 shadow-[8px_8px_0_0_#064ADA]"
                    >
                      <h3 className="font-mono text-lg font-black uppercase text-black">
                        {item.q}
                      </h3>
                      <p className="mt-2 font-mono text-sm text-black">
                        {item.a}
                      </p>
                    </div>
                  ))}
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </>
  )
}

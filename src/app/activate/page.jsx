'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

const ActivatePage = () => {
  const [countdown, setCountdown] = useState(5);
  const [activationStatus, setActivationStatus] = useState('pending'); // 'pending', 'success', 'error'
  const router = useRouter();

  useEffect(() => {
    const activateAccount = async () => {
      const urlParams = new URLSearchParams(window.location.search);
      const token = urlParams.get('token');

      if (!token) {
        setActivationStatus('error');
        return;
      }

      try {
        const response = await fetch(`/api/portal/activate?token=${token}`, {
          method: 'GET',
        });

        const data = await response.json();

        if (response.ok && data.success) {
          setActivationStatus('success');
          // Start countdown only on successful activation
          const timer = setInterval(() => {
            setCountdown((prevCountdown) => prevCountdown - 1);
          }, 1000);

          return () => clearInterval(timer); // Clear interval on unmount
        } else {
          setActivationStatus('error');
        }
      } catch (error) {
        console.error('Activation error:', error);
        setActivationStatus('error');
      }
    };

    activateAccount();
  }, [router]); // Remove countdown from dependency array

  useEffect(() => {
    if (activationStatus === 'success' && countdown === 0) {
      router.push('/login');
    }
  }, [countdown, router, activationStatus]);



  let message = '';
  let title = '';
  let titleColorClass = '';
  let textColorClass = '';

  if (activationStatus === 'pending') {
    title = 'Activating Account...';
    message = 'Please wait while your account is being activated.';
    titleColorClass = 'text-blue-500';
    textColorClass = 'text-gray-500';
  } else if (activationStatus === 'success') {
    title = 'Account Activated Successfully!';
    message = `You will be redirected to the login page in ${countdown} seconds...`;
    titleColorClass = 'text-blue-700';
    textColorClass = 'text-gray-600';
  } else {
    title = 'Activation Failed!';
    message = 'There was an issue activating your account. Please try again or contact support.';
    titleColorClass = 'text-red-600';
    textColorClass = 'text-red-500';
  }

  return (
    <div className={`relative flex min-h-screen flex-col items-center justify-center overflow-hidden border border-gray-200 dark:border-white/5 bg-white`}>
      <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-blue-500/30 to-transparent" />
      <div className={`relative z-10 p-8 rounded-md border border-gray-200 dark:border-white/5 bg-white/50 shadow-lg text-center`}>
        <h1 className={`font-mono text-2xl font-bold ${titleColorClass} mb-4`}>{title}</h1>
        <p className={`font-mono text-sm ${textColorClass}`}>
          {message}
        </p>
      </div>
      <div className="pointer-events-none absolute inset-0 bg-[linear-gradient(to_bottom,rgba(59,130,246,0.02)_50%,transparent_50%)] bg-[length:100%_4px] opacity-20" />
    </div>
  );
};

export default ActivatePage;

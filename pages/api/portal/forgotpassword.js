export const runtime = 'edge'

import config from '../../../lib/config'

export default async function handler(request) {
  if (request.method !== 'POST') {
    return Response.json({ message: 'Method not allowed' }, { status: 405 })
  }

  try {
    const body = await request.json()
    const { email, captcha, inputCaptcha } = body

    if (!email) {
      return Response.json({ message: 'Email is required' }, { status: 400 })
    }

    // CAPTCHA validation (additional frontend security layer)
    if (!captcha || !inputCaptcha) {
      return Response.json(
        { message: 'CAPTCHA verification is required' },
        { status: 400 },
      )
    }

    // Trim whitespace and compare
    const trimmedCaptcha = captcha.trim().toUpperCase()
    const trimmedInputCaptcha = inputCaptcha.trim().toUpperCase()

    console.log('API CAPTCHA Debug:')
    console.log('Received CAPTCHA:', `"${trimmedCaptcha}"`)
    console.log('Received Input:', `"${trimmedInputCaptcha}"`)
    console.log('Are they equal?', trimmedCaptcha === trimmedInputCaptcha)

    if (trimmedCaptcha !== trimmedInputCaptcha) {
      return Response.json(
        {
          message: 'CAPTCHA verification failed. Please try again.',
        },
        { status: 400 },
      )
    }

    // Forward request to backend
    const response = await fetch(
      `${config.backendUrl}/api/portal/forgotpassword`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
        }),
      },
    )

    const data = await response.json()

    if (response.ok) {
      // Success - forward the response
      return Response.json(data, { status: 200 })
    } else {
      // Error - forward the error response
      return Response.json(data, { status: response.status })
    }
  } catch (error) {
    console.error('Forgot password API error:', error)
    return Response.json(
      {
        message: 'Internal server error',
        error: error.message,
      },
      { status: 500 },
    )
  }
}

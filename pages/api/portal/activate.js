// Use edge runtime for Cloudflare Pages deployment
export const runtime = 'edge'

import config from '../../../lib/config'

export default async function handler(request) {
  if (request.method !== 'GET') {
    return Response.json({ message: 'Method not allowed' }, { status: 405 })
  }

  const url = new URL(request.url)
  const token = url.searchParams.get('token')

  try {
    // Debug logging
    console.log('Backend URL:', config.backendUrl)
    console.log('NODE_ENV:', process.env.NODE_ENV)
    console.log('BACKEND_API_URL:', process.env.BACKEND_API_URL)

    const response = await fetch(
      `${config.backendUrl}/api/portal/activate?token=${token}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      },
    )

    const data = await response.json()

    if (response.ok) {
      return Response.json(data, { status: 200 })
    } else {
      return Response.json(data, { status: response.status })
    }
  } catch (error) {
    console.error('Activate API error:', error)
    console.error('Config:', config)
    return Response.json(
      {
        message: 'Internal server error',
        error: error.message,
        config: {
          backendUrl: config.backendUrl,
          nodeEnv: process.env.NODE_ENV,
        },
      },
      { status: 500 },
    )
  }
}

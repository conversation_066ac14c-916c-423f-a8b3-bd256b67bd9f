// Get PayPal Purchase Status API
export const runtime = 'edge';

export default async function handler(req) {
  console.log('PayPal Purchase Status API called');
  console.log('Method:', req.method);
  console.log('URL:', req.url);

  if (req.method !== 'GET') {
    console.log('Method not allowed:', req.method);
    return new Response(JSON.stringify({
      success: false,
      message: 'Method not allowed'
    }), {
      status: 405,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  try {
    // Get token from cookies (Edge Runtime style like dashboard.js)
    const cookieHeader = req.headers.get('cookie')
    let token = null

    if (cookieHeader) {
      const cookies = cookieHeader.split(';').reduce((acc, cookie) => {
        const [key, value] = cookie.trim().split('=')
        acc[key] = value
        return acc
      }, {})
      token = cookies.token
    }

    if (!token) {
      console.log('Authentication failed - no token found');
      return new Response(JSON.stringify({
        success: false,
        error: 'Unauthorized - Bearer token required'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      })
    }

    const url = new URL(req.url);
    const orderId = url.searchParams.get('orderId');
    console.log('Order ID:', orderId);

    if (!orderId) {
      console.log('Validation failed - missing orderId');
      return new Response(JSON.stringify({
        success: false,
        message: 'Order ID is required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get backend URL from environment
    const backendUrl = process.env.BACKEND_URL || 'http://localhost:8000';
    console.log('Backend URL:', backendUrl);

    const backendEndpoint = `${backendUrl}/api/paypal/purchases/status?orderId=${encodeURIComponent(orderId)}`;
    console.log('Calling backend endpoint:', backendEndpoint);

    // Forward request to backend with authentication
    const response = await fetch(backendEndpoint, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      }
    });

    console.log('Backend response status:', response.status);
    console.log('Backend response headers:', Object.fromEntries(response.headers.entries()));

    let data;
    const statusCode = response.status;

    // Check if response is JSON
    const contentType = response.headers.get('content-type');
    console.log('Backend response content-type:', contentType);

    if (contentType && contentType.includes('application/json')) {
      try {
        data = await response.json();
        console.log('Backend response data:', data);
      } catch (jsonError) {
        console.error('Failed to parse backend JSON response:', jsonError);
        const textResponse = await response.text();
        console.log('Backend text response:', textResponse);
        data = {
          success: false,
          message: `Backend returned invalid JSON: ${jsonError.message}`,
          details: textResponse.substring(0, 200)
        };
      }
    } else {
      // If not JSON, get text and create error object
      const textResponse = await response.text();
      console.log('Non-JSON status response received:', textResponse);
      data = {
        success: false,
        message: `Backend returned non-JSON response (${contentType || 'unknown content-type'})`,
        details: textResponse.substring(0, 200),
        statusCode: statusCode
      };
    }

    if (!response.ok) {
      console.log('Backend error response:', data);
      return new Response(JSON.stringify(data), {
        status: statusCode,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    console.log('Sending success response to frontend');
    return new Response(JSON.stringify(data), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('PayPal purchase status error:', error);
    console.error('Error stack:', error.stack);

    return new Response(JSON.stringify({
      success: false,
      message: 'Internal server error',
      details: error.message,
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
// Get PayPal Subscription Status API
export const runtime = 'edge';

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    // Get token from cookies (Edge Runtime style like dashboard.js)
    const cookieHeader = req.headers.get('cookie')
    let token = null

    if (cookieHeader) {
      const cookies = cookieHeader.split(';').reduce((acc, cookie) => {
        const [key, value] = cookie.trim().split('=')
        acc[key] = value
        return acc
      }, {})
      token = cookies.token
    }

    if (!token) {
      return res.status(401).json({
        success: false,
        error: 'Unauthorized - Bearer token required'
      });
    }

    const { subscriptionId } = req.query;

    if (!subscriptionId) {
      return res.status(400).json({
        success: false,
        message: 'Subscription ID is required'
      });
    }

    // Get backend URL from environment
    const backendUrl = process.env.BACKEND_URL || 'http://localhost:8000';

    // Forward request to backend with authentication
    const response = await fetch(`${backendUrl}/api/paypal/subscriptions/${subscriptionId}/status`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      }
    });

    const data = await response.json();
    const statusCode = response.status;

    if (!response.ok) {
      return res.status(statusCode).json(data);
    }

    return res.status(200).json(data);

  } catch (error) {
    console.error('PayPal subscription status error:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      details: error.message 
    });
  }
}
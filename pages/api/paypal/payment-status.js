// Real-time Payment Status Check
export const runtime = 'edge';

export default async function handler(req, res) {
  console.log('Payment status check API called');
  console.log('Query params:', req.query);
  
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    // Get token from cookies (Edge Runtime style like dashboard.js)
    const cookieHeader = req.headers.get('cookie')
    let token = null

    if (cookieHeader) {
      const cookies = cookieHeader.split(';').reduce((acc, cookie) => {
        const [key, value] = cookie.trim().split('=')
        acc[key] = value
        return acc
      }, {})
      token = cookies.token
    }

    if (!token) {
      console.log('Authentication failed - no token found');
      return res.status(401).json({
        success: false,
        error: 'Unauthorized - Bearer token required'
      });
    }

    const { id, type } = req.query;
    console.log('Checking payment status for:', { id, type });

    if (!id || !type) {
      console.log('Missing required parameters');
      return res.status(400).json({
        success: false,
        message: 'Payment ID and type are required'
      });
    }

    // Get backend URL from environment
    const backendUrl = process.env.BACKEND_URL || 'http://localhost:8000';
    console.log('Backend URL:', backendUrl);

    let endpoint;
    if (type === 'subscription') {
      endpoint = `${backendUrl}/api/paypal/subscriptions/status?subscriptionId=${id}`;
    } else if (type === 'purchase') {
      endpoint = `${backendUrl}/api/paypal/purchases/status?orderId=${id}`;
    } else {
      console.log('Invalid payment type:', type);
      return res.status(400).json({
        success: false,
        message: 'Invalid payment type'
      });
    }

    console.log('Calling backend endpoint:', endpoint);

    // Forward request to backend with authentication
    const response = await fetch(endpoint, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      }
    });

    console.log('Backend response status:', response.status);
    console.log('Backend response ok:', response.ok);

    let data;
    const statusCode = response.status;
    
    // Check if response is JSON
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      data = await response.json();
    } else {
      // If not JSON, get text and create error object
      const textResponse = await response.text();
      console.log('Non-JSON payment status response received:', textResponse);
      data = {
        success: false,
        message: `Backend returned non-JSON response: ${textResponse.substring(0, 100)}...`,
        details: textResponse
      };
    }
    
    console.log('Backend response data:', data);

    if (!response.ok) {
      console.log('Backend error response:', data);
      return res.status(statusCode).json(data);
    }

    // Return status with additional metadata
    const finalResponse = {
      ...data,
      checkTime: new Date().toISOString(),
      paymentType: type
    };
    
    console.log('Sending final response:', finalResponse);
    return res.status(200).json(finalResponse);

  } catch (error) {
    console.error('Payment status check error:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      details: error.message 
    });
  }
}
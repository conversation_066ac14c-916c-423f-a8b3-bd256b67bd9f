// PayPal Webhook Handler
export const runtime = 'edge';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    // Get backend URL from environment
    const backendUrl = process.env.BACKEND_URL || 'http://localhost:8000';
    
    // Forward webhook to backend with all headers
    const response = await fetch(`${backendUrl}/api/paypal/webhooks`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Forward PayPal webhook headers for verification
        'paypal-transmission-sig': req.headers['paypal-transmission-sig'] || '',
        'paypal-cert-url': req.headers['paypal-cert-url'] || '',
        'paypal-transmission-id': req.headers['paypal-transmission-id'] || '',
        'paypal-transmission-time': req.headers['paypal-transmission-time'] || '',
      },
      body: JSON.stringify(req.body)
    });

    let data;
    const statusCode = response.status;
    
    // Check if response is JSON
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      data = await response.json();
    } else {
      // If not JSON, get text and create error object
      const textResponse = await response.text();
      console.log('Non-JSON webhook response received:', textResponse);
      data = {
        success: false,
        message: `Backend returned non-JSON response: ${textResponse.substring(0, 100)}...`,
        details: textResponse
      };
    }

    if (!response.ok) {
      console.error('Backend webhook error:', data);
      return res.status(statusCode).json(data);
    }

    // Log successful webhook processing
    console.log('PayPal webhook processed successfully:', {
      eventType: req.body.event_type,
      resourceId: req.body.resource?.id,
      timestamp: new Date().toISOString()
    });

    return res.status(200).json({ success: true });

  } catch (error) {
    console.error('PayPal webhook error:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      details: error.message 
    });
  }
}
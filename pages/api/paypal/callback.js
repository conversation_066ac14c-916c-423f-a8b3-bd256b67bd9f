// PayPal Callback Handler for Success/Cancel redirects
export const runtime = 'edge';

export default async function handler(req, res) {
  const { type, status, ba_token, token } = req.query;

  try {
    console.log('PayPal callback received:', { type, status, ba_token, token });

    // Create a simple HTML page that closes the window and signals parent
    const htmlResponse = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>PayPal Payment ${status === 'success' ? 'Successful' : 'Cancelled'}</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
          }
          .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 2rem;
            border-radius: 10px;
            backdrop-filter: blur(10px);
          }
          .success { color: #4ade80; }
          .cancel { color: #f87171; }
          .spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 3px solid white;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 1rem auto;
          }
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        </style>
      </head>
      <body>
        <div class="container">
          <h2 class="${status === 'success' ? 'success' : 'cancel'}">
            Payment ${status === 'success' ? 'Successful!' : 'Cancelled'}
          </h2>
          <div class="spinner"></div>
          <p>Closing window and updating payment status...</p>
        </div>
        
        <script>
          // Signal to parent window about payment status
          if (window.opener) {
            window.opener.postMessage({
              type: 'paypal_callback',
              status: '${status}',
              paymentType: '${type}',
              token: '${ba_token || token || ''}'
            }, '*');
            
            // Close window immediately after sending message
            setTimeout(() => {
              window.close();
            }, 500);
          } else {
            // If no opener, try to close immediately
            window.close();
          }
        </script>
      </body>
      </html>
    `;

    res.setHeader('Content-Type', 'text/html');
    return res.status(200).send(htmlResponse);

  } catch (error) {
    console.error('PayPal callback error:', error);
    
    const errorHtml = `
      <!DOCTYPE html>
      <html>
      <head><title>Payment Error</title></head>
      <body>
        <h2>Payment Error</h2>
        <p>There was an error processing your payment.</p>
        <script>
          if (window.opener) {
            window.opener.postMessage({
              type: 'paypal_callback',
              status: 'error',
              error: 'Payment processing error'
            }, '*');
          }
          setTimeout(() => window.close(), 3000);
        </script>
      </body>
      </html>
    `;
    
    res.setHeader('Content-Type', 'text/html');
    return res.status(200).send(errorHtml);
  }
}
// PayPal Subscription Creation API
export const runtime = 'edge';

export default async function handler(req) {
  console.log('PayPal Subscriptions API called');
  console.log('Method:', req.method);
  console.log('URL:', req.url);

  if (req.method !== 'POST') {
    console.log('Method not allowed:', req.method);
    return new Response(JSON.stringify({
      success: false,
      message: 'Method not allowed'
    }), {
      status: 405,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  try {
    // Parse request body for Edge Runtime
    let requestBody;
    try {
      requestBody = await req.json();
      console.log('Request body parsed:', requestBody);
    } catch (parseError) {
      console.error('Failed to parse request body:', parseError);
      return new Response(JSON.stringify({
        success: false,
        message: 'Invalid JSON in request body',
        details: parseError.message
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get token from cookies (Edge Runtime style like dashboard.js)
    const cookieHeader = req.headers.get('cookie')
    let token = null

    if (cookieHeader) {
      const cookies = cookieHeader.split(';').reduce((acc, cookie) => {
        const [key, value] = cookie.trim().split('=')
        acc[key] = value
        return acc
      }, {})
      token = cookies.token
    }

    if (!token) {
      console.log('Authentication failed - no token found');
      return new Response(JSON.stringify({
        success: false,
        error: 'Unauthorized - Bearer token required'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      })
    }

    const { tier, email, addons } = requestBody;
    console.log('Extracted data:', { tier, email, addons });

    // Validate required fields
    if (!tier || !email) {
      console.log('Validation failed - missing tier or email');
      return new Response(JSON.stringify({
        success: false,
        message: 'Tier and email are required',
        received: { tier: !!tier, email: !!email }
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get backend URL from environment
    const backendUrl = process.env.BACKEND_URL || 'http://localhost:8000';
    console.log('Backend URL:', backendUrl);

    const payload = {
      tier: tier.toLowerCase(),
      email,
      addons: addons || {}
    };
    console.log('Sending to backend:', payload);

    // Forward request to backend with authentication
    const response = await fetch(`${backendUrl}/api/paypal/subscriptions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(payload)
    });

    console.log('Backend response status:', response.status);
    console.log('Backend response headers:', Object.fromEntries(response.headers.entries()));

    let data;
    const statusCode = response.status;

    // Check if response is JSON
    const contentType = response.headers.get('content-type');
    console.log('Backend response content-type:', contentType);

    if (contentType && contentType.includes('application/json')) {
      try {
        data = await response.json();
        console.log('Backend response data:', data);
      } catch (jsonError) {
        console.error('Failed to parse backend JSON response:', jsonError);
        const textResponse = await response.text();
        console.log('Backend text response:', textResponse);
        data = {
          success: false,
          message: `Backend returned invalid JSON: ${jsonError.message}`,
          details: textResponse.substring(0, 200)
        };
      }
    } else {
      // If not JSON, get text and create error object
      const textResponse = await response.text();
      console.log('Non-JSON subscription response received:', textResponse);
      data = {
        success: false,
        message: `Backend returned non-JSON response (${contentType || 'unknown content-type'})`,
        details: textResponse.substring(0, 200),
        statusCode: statusCode
      };
    }

    if (!response.ok) {
      console.log('Backend error response:', data);
      return new Response(JSON.stringify(data), {
        status: statusCode,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    console.log('Sending success response to frontend');
    return new Response(JSON.stringify(data), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('PayPal subscription creation error:', error);
    console.error('Error stack:', error.stack);

    return new Response(JSON.stringify({
      success: false,
      message: 'Internal server error',
      details: error.message,
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
// Health check endpoint for debugging
export const runtime = 'edge';

export default async function handler(req) {
  console.log('Health check endpoint called');
  console.log('Method:', req.method);
  console.log('URL:', req.url);

  try {
    const backendUrl = process.env.BACKEND_URL || 'http://localhost:8000';
    
    const healthData = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      environment: {
        NODE_ENV: process.env.NODE_ENV,
        BACKEND_URL: backendUrl,
        FRONTEND_URL: process.env.FRONTEND_URL,
      },
      runtime: 'edge',
      method: req.method,
      url: req.url
    };

    // Test backend connection if requested
    const url = new URL(req.url);
    const testBackend = url.searchParams.get('test_backend');
    
    if (testBackend === 'true') {
      try {
        console.log('Testing backend connection...');
        const backendResponse = await fetch(`${backendUrl}/health`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          }
        });
        
        healthData.backend = {
          status: backendResponse.status,
          ok: backendResponse.ok,
          url: `${backendUrl}/health`,
          headers: Object.fromEntries(backendResponse.headers.entries())
        };

        if (backendResponse.ok) {
          try {
            const backendData = await backendResponse.json();
            healthData.backend.data = backendData;
          } catch (e) {
            const text = await backendResponse.text();
            healthData.backend.text = text;
          }
        }
      } catch (backendError) {
        healthData.backend = {
          error: backendError.message,
          url: `${backendUrl}/health`
        };
      }
    }

    return new Response(JSON.stringify(healthData, null, 2), {
      status: 200,
      headers: { 
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      }
    });

  } catch (error) {
    console.error('Health check error:', error);
    
    return new Response(JSON.stringify({
      status: 'error',
      message: error.message,
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

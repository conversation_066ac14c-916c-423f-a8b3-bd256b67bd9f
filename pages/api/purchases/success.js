// PayPal Purchase Success Callback Handler
export const runtime = 'edge';

export default async function handler(req) {
  try {
    // Parse URL to get query parameters
    const url = new URL(req.url)
    const token = url.searchParams.get('token')
    const PayerID = url.searchParams.get('PayerID')

    console.log('PayPal purchase success callback received:', {
      token,
      PayerID,
      url: req.url,
      method: req.method
    })

    // Get origin from headers or construct base URL
    const origin = req.headers.get('origin') ||
                  req.headers.get('referer')?.split('/').slice(0, 3).join('/') ||
                  'https://supersense-ett.pages.dev'

    // Redirect to our PayPal callback endpoint with proper parameters
    const callbackUrl = new URL('/api/paypal/callback', origin)
    callbackUrl.searchParams.set('type', 'purchase')
    callbackUrl.searchParams.set('status', 'success')

    if (token) callbackUrl.searchParams.set('token', token)
    if (PayerID) callbackUrl.searchParams.set('PayerID', PayerID)

    console.log('Redirecting to PayPal callback:', callbackUrl.toString())

    // Redirect to callback endpoint using Edge Runtime Response
    return Response.redirect(callbackUrl.toString(), 302)

  } catch (error) {
    console.error('PayPal purchase success callback error:', error)

    // Fallback redirect to callback with error
    const origin = req.headers.get('origin') ||
                  req.headers.get('referer')?.split('/').slice(0, 3).join('/') ||
                  'https://supersense-ett.pages.dev'

    const errorCallbackUrl = new URL('/api/paypal/callback', origin)
    errorCallbackUrl.searchParams.set('type', 'purchase')
    errorCallbackUrl.searchParams.set('status', 'error')

    return Response.redirect(errorCallbackUrl.toString(), 302)
  }
}

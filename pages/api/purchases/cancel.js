// PayPal Purchase Cancel Callback Handler
export const runtime = 'edge';

export default async function handler(req) {
  try {
    // Parse URL to get query parameters
    const url = new URL(req.url)
    const token = url.searchParams.get('token')
    
    console.log('PayPal purchase cancel callback received:', {
      token,
      url: req.url
    })

    // Get origin from headers or construct base URL
    const origin = req.headers.get('origin') || 
                  req.headers.get('referer')?.split('/').slice(0, 3).join('/') ||
                  'https://supersense-ett.pages.dev'

    // Redirect to our PayPal callback endpoint with proper parameters
    const callbackUrl = new URL('/api/paypal/callback', origin)
    callbackUrl.searchParams.set('type', 'purchase')
    callbackUrl.searchParams.set('status', 'cancel')
    
    if (token) callbackUrl.searchParams.set('token', token)

    console.log('Redirecting to PayPal callback:', callbackUrl.toString())

    // Redirect to callback endpoint using Edge Runtime Response
    return Response.redirect(callbackUrl.toString(), 302)
    
  } catch (error) {
    console.error('PayPal purchase cancel callback error:', error)

    // Fallback redirect to callback with error
    const origin = req.headers.get('origin') || 
                  req.headers.get('referer')?.split('/').slice(0, 3).join('/') ||
                  'https://supersense-ett.pages.dev'
    
    const errorCallbackUrl = new URL('/api/paypal/callback', origin)
    errorCallbackUrl.searchParams.set('type', 'purchase')
    errorCallbackUrl.searchParams.set('status', 'error')

    return Response.redirect(errorCallbackUrl.toString(), 302)
  }
}

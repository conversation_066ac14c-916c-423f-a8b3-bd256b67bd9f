export const runtime = 'edge'

import config from '../../lib/config'

export default async function (request) {
  if (request.method !== 'GET') {
    return Response.json({ message: 'Method not allowed' }, { status: 405 })
  }

  try {
    const backendGoogleAuthUrl = `${config.backendUrl}/api/portal/auth/google`

    // Debug logging
    console.log('Google Login Debug:', {
      backendUrl: config.backendUrl,
      fullUrl: backendGoogleAuthUrl,
      nodeEnv: process.env.NODE_ENV,
      timestamp: new Date().toISOString(),
    })

    // Redirect to backend Google authentication endpoint
    return Response.redirect(backendGoogleAuthUrl)
  } catch (error) {
    console.error('Google Login API error:', error)
    return Response.json(
      {
        message: 'Internal server error',
        error: error.message,
        backendUrl: config.backendUrl,
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    )
  }
}

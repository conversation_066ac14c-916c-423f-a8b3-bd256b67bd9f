export const runtime = 'edge'

// pages/api/login.js
import config from '../../lib/config'

export default async function handler(request) {
  if (request.method !== 'POST') {
    return Response.json({ message: 'Method not allowed' }, { status: 405 })
  }

  try {
    const body = await request.json()
    const { username, password } = body

    // Forward request to backend
    const response = await fetch(`${config.backendUrl}/api/portal/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username,
        password,
      }),
    })

    const data = await response.json()

    if (response.ok && data.success) {
      // Success - create response with cookie
      const loginResponse = Response.json(data, { status: 200 })

      // Manually set the cookie
      loginResponse.headers.set(
        'Set-Cookie',
        `token=${data.data.token}; Path=/; HttpOnly; SameSite=Lax`,
      )

      return loginResponse
    } else {
      // Error - forward the error response
      return Response.json(data, { status: response.status })
    }
  } catch (error) {
    console.error('Login API error:', error)
    return Response.json(
      {
        message: 'Internal server error',
        error: error.message,
      },
      { status: 500 },
    )
  }
}

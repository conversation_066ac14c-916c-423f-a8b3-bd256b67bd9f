// Xendit Success Redirect Handler - redirects to callback endpoint
export const runtime = 'edge';

export default async function handler(req) {
  try {
    // Parse URL to get query parameters
    const url = new URL(req.url)
    const ref = url.searchParams.get('ref')
    const external_id = url.searchParams.get('external_id')
    const subscription_id = url.searchParams.get('subscription_id')
    const invoice_id = url.searchParams.get('invoice_id')

    console.log('Xendit success redirect received:', {
      ref,
      external_id,
      subscription_id,
      invoice_id,
    })

    // Get origin from headers or construct base URL
    const origin = req.headers.get('origin') || 
                  req.headers.get('referer')?.split('/').slice(0, 3).join('/') ||
                  'http://localhost:3001'

    // Redirect to our callback endpoint with proper parameters
    const callbackUrl = new URL('/api/xendit/callback', origin)
    callbackUrl.searchParams.set('status', 'success')

    if (ref) callbackUrl.searchParams.set('ref', ref)
    if (external_id) callbackUrl.searchParams.set('external_id', external_id)
    if (subscription_id)
      callbackUrl.searchParams.set('subscription_id', subscription_id)
    if (invoice_id) callbackUrl.searchParams.set('invoice_id', invoice_id)

    console.log('Redirecting to callback:', callbackUrl.toString())

    // Redirect to callback endpoint using Edge Runtime Response
    return Response.redirect(callbackUrl.toString(), 302)
  } catch (error) {
    console.error('Xendit success redirect error:', error)

    // Fallback redirect to callback with error
    const origin = req.headers.get('origin') || 
                  req.headers.get('referer')?.split('/').slice(0, 3).join('/') ||
                  'http://localhost:3001'
    
    const errorCallbackUrl = new URL('/api/xendit/callback', origin)
    errorCallbackUrl.searchParams.set('status', 'error')

    return Response.redirect(errorCallbackUrl.toString(), 302)
  }
}

export const runtime = 'edge'

import config from '../../lib/config'

export default async function handler(request) {
  if (request.method !== 'POST') {
    return Response.json({ message: 'Method not allowed' }, { status: 405 })
  }

  try {
    const body = await request.json()
    const { token, newPassword, captcha, inputCaptcha } = body

    if (!token || !newPassword) {
      return Response.json(
        { message: 'Token and new password are required' },
        { status: 400 },
      )
    }

    // CAPTCHA validation (additional frontend security layer)
    if (!captcha || !inputCaptcha) {
      return Response.json(
        { message: 'CAPTCHA verification is required' },
        { status: 400 },
      )
    }

    if (captcha !== inputCaptcha) {
      return Response.json(
        { message: 'CAPTCHA verification failed. Please try again.' },
        { status: 400 },
      )
    }

    // Forward request to backend
    const response = await fetch(
      `${config.backendUrl}/api/portal/reset-password`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token,
          newPassword,
        }),
      },
    )

    const data = await response.json()

    if (response.ok) {
      // Success - forward the response
      return Response.json(data, { status: 200 })
    } else {
      // Error - forward the error response
      return Response.json(data, { status: response.status })
    }
  } catch (error) {
    console.error('Reset password API error:', error)
    return Response.json(
      {
        message: 'Internal server error',
        error: error.message,
      },
      { status: 500 },
    )
  }
}

export const runtime = 'edge'

// pages/api/dashboard.js
import config from '../../lib/config'

export default async function handler(request) {
  if (request.method !== 'GET') {
    return Response.json({ message: 'Method not allowed' }, { status: 405 })
  }

  try {
    // Get token from cookies
    const cookieHeader = request.headers.get('cookie')
    let token = null
    
    if (cookieHeader) {
      const cookies = cookieHeader.split(';').reduce((acc, cookie) => {
        const [key, value] = cookie.trim().split('=')
        acc[key] = value
        return acc
      }, {})
      token = cookies.token
    }

    if (!token) {
      return Response.json(
        { message: 'Authentication required' },
        { status: 401 }
      )
    }

    // Forward request to backend
    const response = await fetch(`${config.backendUrl}/api/portal/dashboard`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
    })

    const data = await response.json()

    if (response.ok && data.success) {
      return Response.json(data, { status: 200 })
    } else {
      return Response.json(data, { status: response.status })
    }
  } catch (error) {
    console.error('Dashboard API error:', error)
    return Response.json(
      {
        message: 'Internal server error',
        error: error.message,
      },
      { status: 500 },
    )
  }
}
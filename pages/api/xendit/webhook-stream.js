// Server-Sent Events untuk real-time webhook notifications
export const runtime = 'edge';

export default async function handler(req) {
  if (req.method !== 'GET') {
    return new Response(JSON.stringify({ message: 'Method not allowed' }), {
      status: 405,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  // Create a readable stream for SSE
  const stream = new ReadableStream({
    start(controller) {
      // Initialize global webhook clients array
      if (!global.webhookClients) {
        global.webhookClients = [];
      }

      // Add this client to the list
      const clientId = Date.now();
      const client = { 
        id: clientId, 
        controller,
        destroyed: false
      };
      global.webhookClients.push(client);

      console.log(`SSE client connected: ${clientId}`);

      // Send initial connection message
      const initialMessage = `data: ${JSON.stringify({ type: 'connected', clientId })}\n\n`;
      controller.enqueue(new TextEncoder().encode(initialMessage));

      // Keep connection alive with heartbeat
      const heartbeat = setInterval(() => {
        try {
          if (!client.destroyed) {
            const heartbeatMessage = `data: ${JSON.stringify({ type: 'heartbeat', timestamp: Date.now() })}\n\n`;
            controller.enqueue(new TextEncoder().encode(heartbeatMessage));
            console.log(`💓 Heartbeat sent to client ${clientId}`);
          } else {
            console.log(`🛑 Client ${clientId} connection is destroyed, stopping heartbeat`);
            clearInterval(heartbeat);
            global.webhookClients = global.webhookClients.filter(c => c.id !== clientId);
          }
        } catch (error) {
          console.error(`❌ Heartbeat error for client ${clientId}:`, error);
          clearInterval(heartbeat);
          client.destroyed = true;
          global.webhookClients = global.webhookClients.filter(c => c.id !== clientId);
        }
      }, 15000); // 15 seconds

      // Store cleanup function
      client.cleanup = () => {
        console.log(`SSE client disconnected: ${clientId}`);
        clearInterval(heartbeat);
        client.destroyed = true;
        global.webhookClients = global.webhookClients.filter(c => c.id !== clientId);
      };
    },
    
    cancel() {
      // This is called when the client disconnects
      console.log('SSE stream cancelled');
    }
  });

  // Return SSE response
  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control',
    },
  });
}
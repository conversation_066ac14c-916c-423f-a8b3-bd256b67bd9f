// Next.js API Proxy untuk Xendit Subscriptions
export const runtime = 'edge';

export default async function handler(req) {
  if (req.method !== 'POST') {
    return new Response(JSON.stringify({ message: 'Method not allowed' }), {
      status: 405,
      headers: { 'Content-Type': 'application/json' }
    })
  }

  try {
    const backendUrl = process.env.BACKEND_URL || 'http://localhost:3000'

    // Parse request body for Edge Runtime
    const requestBody = await req.json()

    console.log(
      'Proxying request to:',
      `${backendUrl}/api/xendit/subscriptions`,
    )
    console.log(
      'Request body from frontend:',
      JSON.stringify(requestBody, null, 2),
    )

    // Cek USD amount yang dikirim ke backend
    if (requestBody.usd_amount) {
      console.log(`💰 USD Amount sent to backend: $${requestBody.usd_amount}`)
      console.log(`🔄 Backend should convert this to IDR using RATE_IDR`)
    } else {
      console.log('⚠️  WARNING: usd_amount not found in request body!')
    }

    // Add success/failure redirect URLs to request body for proper modal handling
    // Get the current domain from the request headers
    console.log('Request headers for URL detection:', {
      host: req.headers.get('host'),
      'x-forwarded-host': req.headers.get('x-forwarded-host'),
      'x-forwarded-proto': req.headers.get('x-forwarded-proto'),
      'cf-connecting-ip': req.headers.get('cf-connecting-ip'),
      origin: req.headers.get('origin'),
      referer: req.headers.get('referer')
    });

    // Try multiple ways to get the correct domain
    let baseUrl = process.env.FRONTEND_URL;

    if (!baseUrl) {
      // Try to get from origin header first
      if (req.headers.get('origin')) {
        baseUrl = req.headers.get('origin');
      } else if (req.headers.get('referer')) {
        // Extract domain from referer
        try {
          const refererUrl = new URL(req.headers.get('referer'));
          baseUrl = `${refererUrl.protocol}//${refererUrl.host}`;
        } catch (e) {
          console.log('Failed to parse referer:', e);
        }
      } else {
        // Fallback to host header
        const protocol = req.headers.get('x-forwarded-proto') || 'https';
        const host = req.headers.get('host') || req.headers.get('x-forwarded-host');
        baseUrl = host ? `${protocol}://${host}` : 'http://localhost:3001';
      }
    }

    console.log('Detected baseUrl:', baseUrl);

    const modifiedBody = {
      ...requestBody,
      success_redirect_url: `${baseUrl}/api/xendit/callback?status=success`,
      failure_redirect_url: `${baseUrl}/api/xendit/callback?status=cancel`,
    }

    console.log('Added redirect URLs to request:', {
      success_redirect_url: modifiedBody.success_redirect_url,
      failure_redirect_url: modifiedBody.failure_redirect_url,
    })

        // Get token from cookies (Edge Runtime style like dashboard.js)
    const cookieHeader = req.headers.get('cookie')
    let token = null
    
    if (cookieHeader) {
      const cookies = cookieHeader.split(';').reduce((acc, cookie) => {
        const [key, value] = cookie.trim().split('=')
        acc[key] = value
        return acc
      }, {})
      token = cookies.token
    }

    if (!token) {
      return new Response(JSON.stringify({ message: 'Authentication required' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      })
    }

    // Forward request ke backend with all data (including userId)
    const response = await fetch(`${backendUrl}/api/xendit/subscriptions`, {
      method: 'POST',
      headers: {
        accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(modifiedBody),
    })

    const data = await response.json()
    const statusCode = response.status

    console.log('Backend response:', JSON.stringify(data, null, 2))

    // Debug: Check if backend used our redirect URLs
    if (data.success && data.data) {
      console.log('🔍 REDIRECT URL COMPARISON:')
      console.log('📤 Frontend sent:', modifiedBody.success_redirect_url)
      console.log(
        '📥 Backend returned:',
        data.data.success_redirect_url || 'Not found in response',
      )

      if (
        data.data.success_redirect_url &&
        data.data.success_redirect_url !== modifiedBody.success_redirect_url
      ) {
        console.log('❌ PROBLEM: Backend IGNORED our redirect URLs!')
        console.log(
          '❌ This causes modal to redirect internally instead of closing!',
        )
      } else if (
        data.data.success_redirect_url === modifiedBody.success_redirect_url
      ) {
        console.log('✅ Backend correctly used our redirect URLs')
      }
    }

    // Cek apakah backend melakukan konversi USD ke IDR dengan benar
    if (data.success && data.data) {
      console.log(`💸 Amount in backend response: ${data.data.amount}`)
      if (requestBody.usd_amount && data.data.amount) {
        const expectedIDR = requestBody.usd_amount * 15000 // Expected conversion
        console.log(`📤 Frontend sent USD: $${requestBody.usd_amount}`)
        console.log(
          `🧮 Expected IDR (${requestBody.usd_amount} × 15000): ${expectedIDR.toLocaleString('id-ID')}`,
        )
        console.log(`📥 Backend returned amount: ${data.data.amount}`)

        if (data.data.amount === expectedIDR) {
          console.log('✅ Backend correctly converted USD to IDR!')
        } else if (data.data.amount === requestBody.usd_amount) {
          console.log('❌ Backend NOT converting - still using USD amount!')
        } else {
          console.log(
            '⚠️  Backend returned unexpected amount - check conversion logic',
          )
        }
      }
    }

    // Return response dari backend
    return new Response(JSON.stringify(data), {
      status: statusCode,
      headers: { 'Content-Type': 'application/json' }
    })
  } catch (error) {
    console.error('Proxy error:', error)
    return new Response(JSON.stringify({
      success: false,
      message: 'Proxy server error',
      error: error.message,
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    })
  }
}

export const runtime = 'edge'

export default async function handler(request) {
  if (request.method !== 'GET') {
    return Response.json({ message: 'Method not allowed' }, { status: 405 })
  }

  const url = new URL(request.url)
  const token = url.searchParams.get('token')
  const email = url.searchParams.get('email')
  const redirect = url.searchParams.get('redirect')
  const type = url.searchParams.get('type')

  try {
    // Handle Google OAuth callback
    if (type === 'google_oauth' && token) {
      console.log(
        'Google OAuth callback received with token:',
        token.substring(0, 20) + '...',
      )

      // Create redirect response with cookie headers
      const headers = new Headers()
      headers.set('Location', 'https://supersense-ett.pages.dev/dashboard')
      headers.set(
        'Set-Cookie',
        `token=${token}; Path=/; HttpOnly; SameSite=Lax`,
      )

      return new Response(null, {
        status: 302,
        headers: headers,
      })
    }

    // If this is a password reset callback, redirect to the reset password page
    if (token && email) {
      const redirectUrl = `/forgotpass?token=${encodeURIComponent(token)}&email=${encodeURIComponent(email)}`
      return Response.redirect(
        new URL(redirectUrl, request.url).toString(),
        302,
      )
    }

    // Handle other auth callbacks here
    if (redirect) {
      return Response.redirect(decodeURIComponent(redirect), 302)
    }

    // Default redirect to login
    return Response.redirect(new URL('/login', request.url).toString(), 302)
  } catch (error) {
    console.error('Auth callback error:', error)
    return Response.redirect(
      new URL('/login?error=callback_error', request.url).toString(),
      302,
    )
  }
}

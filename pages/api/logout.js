export const runtime = 'edge'

// pages/api/logout.js

export default async function handler(request) {
  if (request.method !== 'POST') {
    return Response.json({ message: 'Method not allowed' }, { status: 405 })
  }

  try {
    // Create response with success message
    const logoutResponse = Response.json(
      { message: 'Logout successful' },
      { status: 200 },
    )

    // Clear the token cookie by setting its expiration to a past date
    logoutResponse.headers.set(
      'Set-Cookie',
      'token=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; HttpOnly; SameSite=Lax',
    )

    return logoutResponse
  } catch (error) {
    console.error('Logout API error:', error)
    return Response.json(
      { message: 'Internal server error', error: error.message },
      { status: 500 },
    )
  }
}

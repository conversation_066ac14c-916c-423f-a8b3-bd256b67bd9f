#!/bin/bash

echo "🧪 Testing webhook listener manually..."

# Test dengan data webhook yang sebenarnya
curl -X POST http://localhost:3001/api/xendit/webhook-listener \
  -H 'Content-Type: application/json' \
  -d '{
    "id": "687ba97aa59bfcd85c744f59",
    "amount": 225000,
    "status": "PAID",
    "created": "2025-07-19T14:19:39.529Z",
    "is_high": false,
    "paid_at": "2025-07-19T14:19:44.791Z",
    "updated": "2025-07-19T14:19:46.522Z",
    "user_id": "679e3e04efb310294787b54c",
    "currency": "IDR",
    "payment_id": "qrpy_49423eca-a0a5-46ca-8b43-2bdd1ead9593",
    "description": "Subscription for basic tier",
    "external_id": "sub_1752934778308_n2gbaznob",
    "paid_amount": 225000,
    "payer_email": "<EMAIL>",
    "merchant_name": "Grandis",
    "payment_method": "QR_CODE",
    "payment_channel": "QRIS",
    "payment_details": {
        "source": "DANA",
        "receipt_id": ""
    }
  }'

echo ""
echo "✅ Manual webhook test sent!"
echo "Check payment page console for webhook notification."
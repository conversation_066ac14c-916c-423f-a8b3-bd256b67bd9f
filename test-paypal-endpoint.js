#!/usr/bin/env node

/**
 * Test script for PayPal purchases endpoint
 * This script helps debug the 500 Internal Server Error
 */

const BASE_URL = process.env.FRONTEND_URL || 'http://localhost:3001';

async function testPayPalPurchases() {
  console.log('🧪 Testing PayPal Purchases Endpoint');
  console.log('📍 Base URL:', BASE_URL);
  console.log('=' .repeat(50));

  const testPayload = {
    tier: 'pro',
    email: '<EMAIL>',
    amount: 99.99,
    addons: {}
  };

  console.log('📤 Request payload:', JSON.stringify(testPayload, null, 2));
  console.log('-'.repeat(30));

  try {
    const response = await fetch(`${BASE_URL}/api/paypal/purchases`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPayload)
    });

    console.log('📥 Response status:', response.status);
    console.log('📥 Response status text:', response.statusText);
    console.log('📥 Response headers:');
    for (const [key, value] of response.headers.entries()) {
      console.log(`   ${key}: ${value}`);
    }
    console.log('-'.repeat(30));

    const contentType = response.headers.get('content-type');
    
    if (contentType && contentType.includes('application/json')) {
      try {
        const data = await response.json();
        console.log('✅ JSON Response received:');
        console.log(JSON.stringify(data, null, 2));
      } catch (jsonError) {
        console.error('❌ Failed to parse JSON response:', jsonError.message);
        const textResponse = await response.text();
        console.log('📄 Raw response text:');
        console.log(textResponse);
      }
    } else {
      console.log('⚠️  Non-JSON response received');
      const textResponse = await response.text();
      console.log('📄 Raw response text:');
      console.log(textResponse);
    }

  } catch (error) {
    console.error('❌ Request failed:', error.message);
    console.error('Stack trace:', error.stack);
  }

  console.log('=' .repeat(50));
}

async function testBackendConnection() {
  console.log('🔗 Testing Backend Connection');
  
  const backendUrl = process.env.BACKEND_URL || 'http://localhost:3000';
  console.log('📍 Backend URL:', backendUrl);
  
  try {
    const response = await fetch(`${backendUrl}/api/paypal/purchases`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        tier: 'pro',
        email: '<EMAIL>',
        amount: 99.99,
        addons: {}
      })
    });

    console.log('📥 Backend response status:', response.status);
    console.log('📥 Backend response headers:');
    for (const [key, value] of response.headers.entries()) {
      console.log(`   ${key}: ${value}`);
    }

    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      const data = await response.json();
      console.log('✅ Backend JSON Response:');
      console.log(JSON.stringify(data, null, 2));
    } else {
      const textResponse = await response.text();
      console.log('📄 Backend raw response:');
      console.log(textResponse);
    }

  } catch (error) {
    console.error('❌ Backend connection failed:', error.message);
    console.log('💡 This might be the root cause of the 500 error');
  }

  console.log('=' .repeat(50));
}

async function main() {
  console.log('🚀 PayPal Endpoint Debugging Tool');
  console.log('Time:', new Date().toISOString());
  console.log('');

  // Test backend connection first
  await testBackendConnection();
  
  console.log('');
  
  // Test frontend endpoint
  await testPayPalPurchases();
  
  console.log('');
  console.log('🏁 Testing completed');
}

// Run the tests
main().catch(console.error);

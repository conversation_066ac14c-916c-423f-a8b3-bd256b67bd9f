// lib/config.js
const config = {
  // Backend API Configuration
  backendUrl:
    process.env.BACKEND_API_URL ||
    (process.env.NODE_ENV === 'development'
      ? 'http://localhost:3000'
      : 'https://backend.kisworodsp.workers.dev'),

  // You can add more configuration here
  apiTimeout: process.env.API_TIMEOUT || 30000,

  // Environment
  isDevelopment: process.env.NODE_ENV === 'development',
  isProduction: process.env.NODE_ENV === 'production',
}

// Debug logging untuk troubleshooting
if (typeof console !== 'undefined') {
  console.log('Config Debug Info:', {
    backendUrl: config.backendUrl,
    nodeEnv: process.env.NODE_ENV,
    backendApiUrl: process.env.BACKEND_API_URL,
    isDevelopment: config.isDevelopment,
    isProduction: config.isProduction,
  })
}

export default config

# Changelog

## 2024-06-21

- Bump Headless UI dependency to v2.1
- Replaced `PopoverOverlay` with `PopoverBackdrop`
- Update to new data-attribute-based transition API

## 2024-06-18

- Update `prettier` and `prettier-plugin-tailwindcss` dependencies

## 2024-05-31

- Fix `npm audit` warnings

## 2024-05-07

- Bump Headless UI dependency to v2.0

## 2024-01-17

- Fix `sharp` dependency issues ([#1549](https://github.com/tailwindlabs/tailwindui-issues/issues/1549))

## 2024-01-16

- Replace Twitter with X

## 2024-01-10

- Update Tailwind CSS, Next.js, Prettier, TypeScript, ESLint, and other dependencies
- Fix indentation in `Button` component

## 2023-12-07

- Fix auth/404 pages being cut off on short screens ([#1528](https://github.com/tailwindlabs/tailwindui-issues/issues/1528))

## 2023-11-10

- Fix types in `Button` component

## 2023-09-07

- Added TypeScript version of template

## 2023-08-17

- Bump Headless UI dependency

## 2023-08-15

- Bump Next.js dependency

## 2023-07-31

- Port template to Next.js app router

## 2023-07-18

- Add 404 page
- Sort imports

## 2023-05-16

- Bump Next.js dependency

## 2023-04-11

- Bump Next.js dependency

## 2023-03-29

- Bump Tailwind CSS and Prettier dependencies
- Sort classes

## 2023-03-22

- Bump Headless UI dependency

## 2023-02-02

- Bump Headless UI dependency

## 2022-11-04

- Bump Tailwind CSS and Next.js dependencies

## 2022-09-27

- Update Headless UI, Next.js, and Autoprefixer dependencies

## 2022-09-09

- Update Next.js dependency

## 2022-09-07

- Update Headless UI dependency

## 2022-09-01

- Update Tailwind CSS, Next.js, Headless UI, ESLint, and other dependencies

## 2022-08-16

- Enable experimental Next.js `scrollRestoration` flag

## 2022-07-25

- Update Next.js and React dependencies

## 2022-07-13

- Improve wording of `aria-label` in `Plan` component

## 2022-07-06

- Replace `next/image` with `next/future/image`

## 2022-06-23

- Initial release

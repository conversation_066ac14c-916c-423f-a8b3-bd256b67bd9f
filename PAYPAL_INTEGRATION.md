# PayPal Integration Guide

## Overview
This frontend application integrates with your PayPal backend API to handle subscription and one-time purchase payments.

## Setup Instructions

### 1. Environment Variables
Create a `.env.local` file in your project root:

```bash
BACKEND_URL=https://your-backend-api.com
FRONTEND_URL=https://your-frontend-domain.com
```

For development:
```bash
BACKEND_URL=http://localhost:8000
FRONTEND_URL=http://localhost:3000
```

### 2. API Endpoints Created

#### Frontend API Routes (pages/api/paypal/)
- `POST /api/paypal/subscriptions` - Create PayPal subscription
- `POST /api/paypal/purchases` - Create PayPal one-time purchase
- `GET /api/paypal/subscriptions/status/[subscriptionId]` - Get subscription status
- `GET /api/paypal/purchases/status` - Get purchase status
- `GET /api/paypal/callback` - Handle PayPal redirects

### 3. Payment Flow

#### For Enterprise Plan (Example):
1. User navigates to `/plans`
2. User clicks "Choose Enterprise" 
3. User is redirected to `/payment?plan=Enterprise`
4. User selects payment type:
   - **Subscription**: $99/year recurring
   - **One-time Purchase**: $1,188 lifetime access
5. User selects PayPal as payment method
6. User clicks "Pay with PayPal"
7. Frontend calls appropriate API endpoint
8. User is redirected to PayPal for payment
9. After payment, user returns to dashboard with status

### 4. Plan Pricing Structure

```javascript
const planPricing = {
  'Basic': { 
    subscription: 15,    // $15/month
    purchase: 180       // $180 lifetime
  },
  'Pro': { 
    subscription: 40,    // $40/6 months
    purchase: 480       // $480 lifetime
  },
  'Enterprise': { 
    subscription: 99,    // $99/year
    purchase: 1188      // $1,188 lifetime
  }
}
```

### 5. Testing the Integration

Run the test script:
```bash
node tmp_rovodev_test_paypal.js
```

Or manually test:
1. Start your backend server
2. Start the frontend: `npm run dev`
3. Navigate to `/plans`
4. Select "Enterprise" plan
5. Choose PayPal payment method
6. Complete the payment flow

### 6. Backend Integration Requirements

Your backend must handle these endpoints:
- `POST /api/paypal/subscriptions`
- `POST /api/paypal/purchases`
- `GET /api/paypal/subscriptions/:id/status`
- `GET /api/paypal/purchases/status`

### 7. Success/Error Handling

The dashboard will show payment status via URL parameters:
- `?subscription_status=success&subscription_id=XXX`
- `?purchase_status=success&order_id=XXX`
- `?subscription_status=cancelled`
- `?payment_status=error`

### 8. Components Modified

- **Plans Page**: Added navigation to payment page with plan parameter
- **Payment Page**: Added PayPal integration with subscription/purchase options
- **Dashboard**: Added PayPal callback handler for status messages

### 9. Security Notes

- All sensitive PayPal credentials should be stored in your backend
- Frontend only handles UI and redirects
- Payment processing happens entirely on PayPal's secure servers
- Backend validates all payments before upgrading user tiers

### 10. Troubleshooting

**Common Issues:**
- Ensure `BACKEND_URL` is correctly set
- Verify backend PayPal API is running
- Check browser console for API errors
- Ensure user email is available in user context

**Debug Mode:**
Check browser console for detailed logs during payment flow.
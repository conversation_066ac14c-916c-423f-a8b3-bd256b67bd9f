// next.config.mjs
const setupConfig = async () => {
  const { setupDevPlatform } = await import(
    '@cloudflare/next-on-pages/next-dev'
  )

  // Here we use the @cloudflare/next-on-pages next-dev module to allow us to use bindings during local development
  // (when running the application with `next dev`), for more information see:
  // https://github.com/cloudflare/next-on-pages/blob/main/internal-packages/next-dev/README.md
  if (process.env.NODE_ENV === 'development') {
    await setupDevPlatform()
  }

  /** @type {import('next').NextConfig} */
  return {
    eslint: {
      // Warning: This allows production builds to successfully complete even if
      // your project has ESLint errors.
      ignoreDuringBuilds: false,
    },
  }
}

export default setupConfig()

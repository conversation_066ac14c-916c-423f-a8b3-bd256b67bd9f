#!/usr/bin/env node

/**
 * Test script untuk memeriksa konfigurasi PayPal backend
 * Script ini membantu debug masalah redirect URL
 */

const FRONTEND_URL = 'https://supersense-ett.pages.dev';
const BACKEND_URL = process.env.BACKEND_URL || 'https://backend.kisworodsp.workers.dev';

async function testPayPalBackendConfig() {
  console.log('🧪 Testing PayPal Backend Configuration');
  console.log('📍 Frontend URL:', FRONTEND_URL);
  console.log('📍 Backend URL:', BACKEND_URL);
  console.log('=' .repeat(50));

  const testPayload = {
    tier: 'pro',
    email: '<EMAIL>',
    amount: 99.99,
    addons: {}
  };

  console.log('📤 Request payload:', JSON.stringify(testPayload, null, 2));
  console.log('-'.repeat(30));

  try {
    console.log('🔄 Making request to backend...');
    const response = await fetch(`${BACKEND_URL}/api/paypal/purchases`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      },
      body: JSON.stringify(testPayload)
    });

    console.log('📥 Response status:', response.status);
    console.log('📥 Response headers:');
    for (const [key, value] of response.headers.entries()) {
      console.log(`   ${key}: ${value}`);
    }

    let data;
    const contentType = response.headers.get('content-type');
    
    if (contentType && contentType.includes('application/json')) {
      data = await response.json();
      console.log('📥 Response data:', JSON.stringify(data, null, 2));
      
      // Check if approval URL contains correct return URLs
      if (data.approval_url || data.data?.approvalUrl) {
        const approvalUrl = data.approval_url || data.data?.approvalUrl;
        console.log('\n🔍 Analyzing approval URL...');
        console.log('Approval URL:', approvalUrl);
        
        // Parse URL to check return URLs
        try {
          const url = new URL(approvalUrl);
          const returnUrl = url.searchParams.get('return_url');
          const cancelUrl = url.searchParams.get('cancel_url');
          
          console.log('\n📋 Return URLs in approval URL:');
          console.log('✅ Return URL:', returnUrl);
          console.log('❌ Cancel URL:', cancelUrl);
          
          // Check if return URLs point to our endpoints
          if (returnUrl && returnUrl.includes('/api/purchases/success')) {
            console.log('✅ Return URL correctly points to our success endpoint');
          } else {
            console.log('❌ Return URL does NOT point to our success endpoint');
            console.log('   Expected: something like .../api/purchases/success');
            console.log('   Actual:', returnUrl);
          }
          
          if (cancelUrl && cancelUrl.includes('/api/purchases/cancel')) {
            console.log('✅ Cancel URL correctly points to our cancel endpoint');
          } else {
            console.log('❌ Cancel URL does NOT point to our cancel endpoint');
            console.log('   Expected: something like .../api/purchases/cancel');
            console.log('   Actual:', cancelUrl);
          }
          
        } catch (urlError) {
          console.log('❌ Error parsing approval URL:', urlError.message);
        }
      } else {
        console.log('❌ No approval URL found in response');
      }
      
    } else {
      const textResponse = await response.text();
      console.log('📥 Non-JSON response:', textResponse);
    }

  } catch (error) {
    console.error('❌ Request failed:', error.message);
  }

  console.log('=' .repeat(50));
}

async function testReturnURLs() {
  console.log('\n🔗 Testing Return URL Endpoints');
  console.log('-'.repeat(30));
  
  const testUrls = [
    `${FRONTEND_URL}/api/purchases/success?token=test123&PayerID=testpayer`,
    `${FRONTEND_URL}/api/purchases/cancel?token=test123`
  ];
  
  for (const testUrl of testUrls) {
    console.log(`\n🧪 Testing: ${testUrl}`);
    try {
      const response = await fetch(testUrl, {
        method: 'GET',
        redirect: 'manual' // Don't follow redirects
      });
      
      console.log(`   Status: ${response.status}`);
      if (response.status >= 300 && response.status < 400) {
        const location = response.headers.get('location');
        console.log(`   Redirects to: ${location}`);
      }
      
    } catch (error) {
      console.log(`   Error: ${error.message}`);
    }
  }
}

// Run tests
async function runAllTests() {
  await testPayPalBackendConfig();
  await testReturnURLs();
  
  console.log('\n💡 Recommendations:');
  console.log('1. Check if backend is setting correct return_url and cancel_url');
  console.log('2. Ensure return URLs point to:');
  console.log(`   Success: ${FRONTEND_URL}/api/purchases/success`);
  console.log(`   Cancel:  ${FRONTEND_URL}/api/purchases/cancel`);
  console.log('3. Verify backend PayPal configuration includes these URLs');
}

runAllTests().catch(console.error);

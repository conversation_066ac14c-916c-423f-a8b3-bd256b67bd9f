{"name": "awp-dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 3001", "build": "next build && rm -rf .next/cache", "start": "next start", "lint": "npx eslint \"src/**/*.{js,jsx,mjs,cjs,ts,tsx}\"", "pages:build": "npx @cloudflare/next-on-pages", "preview": "npm run pages:build && wrangler pages dev", "deploy": "npm run pages:build && wrangler pages deploy"}, "dependencies": {"@headlessui/react": "^2.2.0", "@headlessui/tailwindcss": "^0.2.0", "@heroicons/react": "^2.2.0", "@tailwindcss/forms": "^0.5.3", "@tanstack/react-query": "^5.64.0", "autoprefixer": "^10.4.12", "clsx": "^2.1.1", "framer-motion": "^11.15.0", "jose": "^6.0.11", "jwt-decode": "^4.0.0", "next": "^15.1.4", "primeicons": "^7.0.0", "primereact": "^10.9.1", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "^3.4.1", "tanstack": "^1.0.0", "zustand": "^5.0.3"}, "devDependencies": {"@cloudflare/next-on-pages": "^1.13.7", "@cloudflare/workers-types": "^4.20241230.0", "@eslint/eslintrc": "^3.0.0", "@next/eslint-plugin-next": "^14.0.4", "eslint": "^8.57.1", "eslint-plugin-react": "^7.34.0", "eslint-plugin-react-hooks": "^4.6.0", "globals": "^15.0.0", "postcss": "^8", "prettier": "^3.3.2", "prettier-plugin-tailwindcss": "^0.6.5", "sharp": "0.33.1", "vercel": "^39.2.5", "wrangler": "^4.24.3"}}
const { SignJWT } = require('jose');

async function createResetToken() {
  const secret = new TextEncoder().encode('your-super-secret-key');
  const now = Math.floor(Date.now() / 1000);
  
  const token = await new SignJWT({
    userId: 'portal_1752532652518_8f9ktbpg3',
    type: 'password_reset'
  })
  .setProtectedHeader({ alg: 'HS256', typ: 'JWT' })
  .setIssuer('supersuser.id')
  .setAudience('portal_app')
  .setIssuedAt(now)
  .setExpirationTime(now + 3600) // 1 hour from now
  .sign(secret);
  
  const email = Buffer.from('<EMAIL>').toString('base64');
  
  console.log('New reset URL:');
  console.log(`http://localhost:3001/forgotpass?token=${encodeURIComponent(token)}&email=${encodeURIComponent(email)}`);
}

createResetToken().catch(console.error);

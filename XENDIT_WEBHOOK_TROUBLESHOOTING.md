# Xendit Webhook Troubleshooting Guide

## Masalah: "Processing Xendit Payment" tidak update setelah pembayaran berhasil

### Kemungkinan Penyebab:

1. **Webhook tidak sampai ke frontend**
2. **SSE connection terputus**
3. **ID tracking tidak cocok**
4. **Backend webhook tidak diteruskan ke frontend**

### Langkah Debugging:

#### 1. <PERSON><PERSON>rowser Console
```bash
# Buka Developer Tools > Console
# Cari log berikut:
- "🔌 Connecting to webhook stream..."
- "✅ SSE connection established"
- "📨 Webhook notification received:"
- "✅ Webhook matches our payment:"
```

#### 2. Cek Server Logs
```bash
# Frontend logs:
- "SSE client connected: [clientId]"
- "🔔 Webhook received from backend:"
- "📡 Broadcasting to X connected clients"

# Backend logs (dari backend service):
- Webhook dari Xendit ke backend
- POST ke frontend /api/xendit/webhook-listener
```

#### 3. Test Manual Webhook
```bash
# Gunakan script test webhook
./test-webhook-debug.sh [invoice_id]

# Atau manual curl:
curl -X POST "http://localhost:3001/api/xendit/webhook-listener" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "test-invoice-123",
    "external_id": "test-invoice-123", 
    "invoice_id": "test-invoice-123",
    "status": "PAID",
    "type": "invoice"
  }'
```

#### 4. Cek Network Tab
- Pastikan SSE connection `/api/xendit/webhook-stream` berjalan
- Status 200 dan connection kept alive
- Ada heartbeat messages setiap 30 detik

#### 5. Debug Tools (Development Mode)
- Buka halaman payment
- Klik QRIS dan subscribe
- Setelah status "Processing", akan muncul debug buttons
- Klik "Test PAID Webhook" untuk simulasi

### Perbaikan yang Telah Dilakukan:

1. **Fixed duplicate event listeners** di webhook-stream.js
2. **Added backup polling mechanism** setiap 5 detik
3. **Improved webhook matching logic** - lebih fleksibel matching ID
4. **Enhanced error handling** dengan proper cleanup
5. **Added debug tools** untuk development
6. **Better logging** untuk troubleshooting

### Status Flow:

```
User clicks Subscribe
↓
Frontend creates Xendit payment via /api/proxy/xendit/subscriptions  
↓
Opens modal window with payment URL
↓
User completes payment in modal
↓
Xendit sends webhook to backend
↓
Backend forwards webhook to frontend /api/xendit/webhook-listener
↓
Frontend broadcasts via SSE to all connected clients
↓
Payment page receives webhook and updates status to SUCCESS
↓
Redirects to dashboard
```

### Backup Mechanisms:

1. **SSE (Primary)**: Real-time webhook delivery
2. **Polling (Backup)**: Checks payment status every 5 seconds
3. **Debug Tools**: Manual webhook testing in development

### Environment Variables:

Pastikan environment variables sudah benar:
```bash
BACKEND_URL=http://localhost:3000  # Backend service URL
NEXT_PUBLIC_API_URL=http://localhost:3001  # Frontend URL
```

### Common Issues:

1. **"No clients connected to receive webhook"**
   - SSE connection tidak terbuat atau terputus
   - Cek browser console untuk SSE errors

2. **"Webhook does not match our payment"**
   - ID tracking tidak cocok antara payment creation dan webhook
   - Cek log untuk membandingkan IDs

3. **"SSE connection error"**
   - Network issues atau server restart
   - Backup polling akan mengambil alih

4. **Payment window ditutup manual**
   - Polling akan tetap berjalan sampai timeout (10 menit)
   - Status akan update otomatis jika payment berhasil

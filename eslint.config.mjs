import globals from 'globals'
import reactHooks from 'eslint-plugin-react-hooks'
import react from 'eslint-plugin-react'
import nextJs from '@next/eslint-plugin-next'

export default [
  {
    files: ['**/*.{js,jsx,mjs,cjs,ts,tsx}'],
    ignores: ['**/.next/**', '**/node_modules/**'],
    languageOptions: {
      parserOptions: {
        ecmaFeatures: {
          jsx: true,
        },
        ecmaVersion: 2021,
        sourceType: 'module',
      },
      globals: {
        ...globals.browser,
        ...globals.node,
      },
    },
    settings: {
      react: {
        version: 'detect',
      },
    },
    plugins: {
      react: react,
      'react-hooks': reactHooks,
      '@next/next': nextJs,
    },
    rules: {
      // React rules
      ...react.configs.recommended.rules,
      ...react.configs['jsx-runtime'].rules,

      // React Hooks rules
      'react-hooks/rules-of-hooks': 'error',
      'react-hooks/exhaustive-deps': 'warn',

      // Next.js rules
      ...nextJs.configs.recommended.rules,
      ...nextJs.configs['core-web-vitals'].rules,

      // Custom overrides
      '@next/next/no-img-element': 'off', // Disable if you use <img> instead of <Image>
      'react/prop-types': 'off', // Temporarily disable prop-types to focus on other errors
      'react/no-unescaped-entities': 'off', // Disable unescaped entities warning
      'react/no-find-dom-node': 'off', // Disable findDOMNode warning
      'react/jsx-no-duplicate-props': 'off', // Disable duplicate props warning
      'react/no-unknown-property': 'off', // Disable unknown property warning
      // Add more custom rules here
    },
  },
]

#!/bin/bash

# Script untuk test webhook manual
# Usage: ./test-webhook-debug.sh [invoice_id]

INVOICE_ID=${1:-"sub_1752935837331_sej6pz0p1"}  # Use actual invoice ID from logs as default
FRONTEND_URL="http://localhost:3001"

echo "🧪 Testing webhook for invoice_id: $INVOICE_ID"
echo "📡 Sending to: $FRONTEND_URL/api/xendit/webhook-listener"

# Test webhook payload yang match dengan payment yang sedang processing
curl -X POST "$FRONTEND_URL/api/xendit/webhook-listener" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "'$INVOICE_ID'",
    "external_id": "'$INVOICE_ID'",
    "invoice_id": "'$INVOICE_ID'",
    "status": "PAID",
    "type": "invoice",
    "payment_method": "QRIS",
    "amount": 225000,
    "paid_amount": 225000,
    "currency": "IDR",
    "paid_at": "'$(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")'",
    "created": "'$(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")'",
    "updated": "'$(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")'"
  }' \
  -v

echo ""
echo "✅ Webhook test completed!"
echo "Check your browser console and server logs for webhook processing details."
echo ""
echo "💡 To test with real processing payment:"
echo "   1. Start payment process in browser"
echo "   2. Note the invoice_id from console logs"  
echo "   3. Run: ./test-webhook-debug.sh <invoice_id>"
